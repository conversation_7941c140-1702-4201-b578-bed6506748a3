<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Agent extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'polling_station_id'
    ];

    function user() : BelongsTo {

        return $this->belongsTo(User::class,'user_id');
        
    }

    function votes() : HasMany{

        return $this->hasMany(Vote::class);
        
    }

    function agents(){

        return Agent::get();
        
    }

    function eveidences() : HasMany {

        return $this->hasMany(Eveidence::class,'agent_id');
        
    }

    function polling_station() : BelongsTo {

        return $this->belongsTo(PollingStation::class, 'polling_station_id');

    }

    function positions() : BelongsToMany {

        return $this->belongsToMany(Position::class,AgentPosition::class);
        
    }

    

    
}
