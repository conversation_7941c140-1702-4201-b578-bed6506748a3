<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Laravel\Sanctum\HasApiTokens;
use Illuminate\Support\Str;

class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'phone_number',
        'password',
        'user_type',
        'role_id',
        'is_active',
        'last_login_at',
        'created_by'
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'password' => 'hashed',
        'is_active' => 'boolean',
        'last_login_at' => 'datetime'
    ];

    /**
     * The model's default values for attributes.
     *
     * @var array
     */
    protected $attributes = [
        'user_type' => 'agent',
        'is_active' => true,
    ];


    public static function uploadImage($file)
    {
        try {
            if (empty($file) || !$file->isValid()) {
                return null;
            }

            $destinationPath = public_path('files');

            // Ensure directory exists
            if (!is_dir($destinationPath)) {
                mkdir($destinationPath, 0755, true);
            }

            $ext = $file->getClientOriginalExtension();

            // Validate file extension
            $allowedExtensions = ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'mp4', 'mov', 'avi', 'wmv', 'flv', 'webm'];
            if (!in_array(strtolower($ext), $allowedExtensions)) {
                return null;
            }

            $file_url = time() . Str::random(12) . '.' . $ext;

            // Move file with error handling
            if (!$file->move($destinationPath, $file_url)) {
                return null;
            }

            return $file_url;

        } catch (\Exception $e) {
            Log::error('File upload error: ' . $e->getMessage());
            return null;
        }
    }

    public static function getValidationMessage($request,$rules) {

        $val = Validator::make($request->all(),$rules);

        $message = NULL;

        if ($val->fails()) {           

            $errors = $val->errors()->toArray();

            if (isset($errors['name']))

                $message .= $errors['name'][0]." ";
                
            if (isset($errors['password']))

                $message .= $errors['password'][0]." ";

            if (isset($errors['phone_number']))

                $message .= $errors['phone_number'][0]." ";

            if (isset($errors['old_password']))

                $message .= $errors['old_password'][0]." ";

            

        }
        
        return $message;
        
    }

    function lastUpdated() {
        $votes = Vote::latest('updated_at')->first();

        if ($votes) {
            return $votes->updated_at->format('M d, Y h:i A');
        } else {
            return "No vote updated yet.";
        }
    }

    /**
     * Get the primary role for this user
     */
    public function role(): BelongsTo
    {
        return $this->belongsTo(Role::class);
    }

    /**
     * Get all roles for this user (many-to-many relationship)
     */
    public function roles(): BelongsToMany
    {
        return $this->belongsToMany(Role::class, 'role_user');
    }

    /**
     * Get the agent record for this user
     */
    public function agent(): HasOne
    {
        return $this->hasOne(Agent::class);
    }

    /**
     * Check if user has a specific role
     */
    public function hasRole(string $roleName): bool
    {
        // Check primary role
        if ($this->role && $this->role->name === $roleName) {
            return true;
        }

        // Check additional roles
        return $this->roles()->where('name', $roleName)->exists();
    }

    /**
     * Check if user has any of the given roles
     */
    public function hasAnyRole(array $roles): bool
    {
        foreach ($roles as $role) {
            if ($this->hasRole($role)) {
                return true;
            }
        }
        return false;
    }

    /**
     * Get user type role automatically based on user_type
     */
    public function getUserTypeRole(): string
    {
        return match($this->user_type) {
            'admin' => 'admin',
            'manager' => 'polling_station_manager',
            'viewer' => 'dashboard_viewer',
            'agent' => 'agent',
            default => 'user'
        };
    }

    /**
     * Check if user has role (including automatic user type roles)
     */
    public function hasRoleIncludingUserType(string $roleName): bool
    {
        // Check if user type automatically grants this role
        if ($this->getUserTypeRole() === $roleName) {
            return true;
        }

        // Check assigned roles
        return $this->hasRole($roleName);
    }

    /**
     * Check if user has a specific permission
     */
    public function hasPermission(string $permission): bool
    {
        // Admin users have all permissions
        if ($this->user_type === 'admin') {
            return true;
        }

        // Check user type automatic permissions
        $userTypePermissions = $this->getUserTypePermissions();
        if (in_array($permission, $userTypePermissions)) {
            return true;
        }

        // Check primary role permissions with defensive loading
        try {
            if ($this->role_id && $this->role && method_exists($this->role, 'hasPermission')) {
                if ($this->role->hasPermission($permission)) {
                    return true;
                }
            }
        } catch (\Exception $e) {
            // Log the error but continue checking other permissions
            Log::warning('Error checking role permission: ' . $e->getMessage());
        }

        // Check additional roles permissions with defensive loading
        try {
            if ($this->relationLoaded('roles')) {
                foreach ($this->roles as $role) {
                    if ($role && method_exists($role, 'hasPermission') && $role->hasPermission($permission)) {
                        return true;
                    }
                }
            }
        } catch (\Exception $e) {
            // Log the error but continue
            Log::warning('Error checking roles permission: ' . $e->getMessage());
        }

        return false;
    }

    /**
     * Get permissions automatically granted by user type
     */
    public function getUserTypePermissions(): array
    {
        return match($this->user_type) {
            'admin' => [
                // Admin has all permissions - handled in hasPermission method
            ],
            'agent' => [
                'submit_votes',
                'upload_evidence',
                'view_assigned_station',
                'view_dashboard'
            ],
            'manager' => [
                'view_polling_stations',
                'create_polling_stations',
                'edit_polling_stations',
                'manage_polling_stations',
                'view_votes',
                'view_dashboard',
                'view_analytics',
                'view_reports'
            ],
            'viewer' => [
                'view_dashboard',
                'view_analytics',
                'view_reports',
                'view_polling_stations',
                'view_votes',
                'view_candidates'
            ],
            default => []
        };
    }

    /**
     * Get redirect path after login based on user type
     */
    public function getRedirectPath(): string
    {
        return match($this->user_type) {
            'agent' => '/agent/dashboard',
            'manager' => '/manager/dashboard',
            'admin' => '/home',
            'viewer' => '/home',
            default => '/home'
        };
    }

    /**
     * Scope for active users
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for users by type
     */
    public function scopeByType($query, string $userType)
    {
        return $query->where('user_type', $userType);
    }

    /**
     * Get the user who created this user
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get users created by this user
     */
    public function createdUsers()
    {
        return $this->hasMany(User::class, 'created_by');
    }
}
