<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Support\Facades\DB;
use App\Models\Vote;

class PollingStation extends Model
{
    use HasFactory;
    
    protected $fillable = [
        'name',
        'constituency',
        'district',
        'county',
        'subcounty',
        'parish',
        'village',
        'latitude',
        'longitude'
    ];

    function agents() : HasMany {
        return $this->hasMany(Agent::class);
    }

    /**
     * Get the primary agent for this polling station
     * Note: This returns the first agent. Use agents() for multiple agents.
     *
     * @deprecated Consider using agents()->first() for clarity
     */
    function agent() : HasOne {
        return $this->hasOne(Agent::class);
    }
    
    /**
     * Check if the polling station has submitted any votes
     *
     * @return bool
     */
    public function hasSubmittedVotes(): bool
    {
        // Get all agent IDs for this polling station
        $agentIds = $this->agents()->pluck('id')->toArray();
        
        if (empty($agentIds)) {
            return false;
        }
        
        // Check if any votes exist for these agents
        $voteCount = Vote::whereIn('agent_id', $agentIds)->count();
        
        return $voteCount > 0;
    }
    
    /**
     * Get the count of votes submitted by this polling station
     *
     * @return int
     */
    public function submittedVotesCount(): int
    {
        // Get all agent IDs for this polling station
        $agentIds = $this->agents()->pluck('id')->toArray();
        
        if (empty($agentIds)) {
            return 0;
        }
        
        // Sum all votes submitted by agents of this polling station
        return Vote::whereIn('agent_id', $agentIds)->sum('number_of_votes');
    }
}
