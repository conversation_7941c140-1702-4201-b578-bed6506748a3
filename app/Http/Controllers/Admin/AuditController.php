<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\AuditLog;
use App\Models\VoteAuditLog;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class AuditController extends Controller
{
    /**
     * Display unified audit dashboard
     */
    public function index(Request $request)
    {
        // Get vote audit statistics
        $suspiciousActivity = $this->getVoteAuditStatistics();
        
        // Get best performing users
        $bestPerformingUsers = $this->getBestPerformingUsers();

        // Get system audit logs with more details
        $query = AuditLog::with('user')->orderBy('created_at', 'desc');

        // Apply filters
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function($q) use ($search) {
                $q->where('action', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('user_name', 'like', "%{$search}%")
                  ->orWhere('model_type', 'like', "%{$search}%");
            });
        }

        if ($request->filled('action')) {
            $query->where('action', $request->get('action'));
        }

        if ($request->filled('user_type')) {
            $query->where('user_type', $request->get('user_type'));
        }

        if ($request->filled('model_type')) {
            $query->where('model_type', 'like', '%' . $request->get('model_type') . '%');
        }

        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->get('date_from'));
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->get('date_to'));
        }

        // Handle per page parameter
        $perPage = $request->get('per_page', 25);
        $perPage = in_array($perPage, [25, 50, 100, 200]) ? $perPage : 25;

        $auditLogs = $query->paginate($perPage)->appends($request->query());

        // Get filter options (ensure they're always collections)
        try {
            $actions = AuditLog::distinct()->pluck('action')->filter()->sort()->values();
            $userTypes = AuditLog::distinct()->pluck('user_type')->filter()->sort()->values();
            $modelTypes = AuditLog::distinct()->pluck('model_type')->filter()->map(function($type) {
                return $type ? class_basename($type) : null;
            })->filter()->sort()->values();
        } catch (\Exception $e) {
            $actions = collect();
            $userTypes = collect();
            $modelTypes = collect();
        }

        // Ensure collections are never null
        $actions = $actions ?? collect();
        $userTypes = $userTypes ?? collect();
        $modelTypes = $modelTypes ?? collect();

        // Get recent vote audit logs (if available)
        $recentVoteAudits = collect();
        if (class_exists('App\Models\VoteAuditLog')) {
            try {
                $recentVoteAudits = VoteAuditLog::with(['user', 'pollingStation'])
                    ->orderBy('created_at', 'desc')
                    ->limit(10)
                    ->get();
            } catch (\Exception $e) {
                $recentVoteAudits = collect();
            }
        }

        return view('admin.audit.index', compact(
            'auditLogs',
            'actions',
            'userTypes',
            'modelTypes',
            'suspiciousActivity',
            'recentVoteAudits',
            'bestPerformingUsers'
        ));
    }

    /**
     * Get vote audit statistics
     */
    private function getVoteAuditStatistics()
    {
        // Check if VoteAuditLog model exists and table exists
        if (!class_exists('App\Models\VoteAuditLog')) {
            return [
                'total_flagged' => 0,
                'rapid_submissions' => 0,
                'large_changes' => 0,
                'agents_with_multiple_submissions' => 0,
            ];
        }

        try {
            $flaggedSubmissions = VoteAuditLog::where('is_flagged', true)->count();

            $rapidSubmissions = VoteAuditLog::where('created_at', '>=', now()->subHours(24))
                ->select('user_id')
                ->groupBy('user_id')
                ->havingRaw('COUNT(*) > 5')
                ->count();

            $largeChanges = VoteAuditLog::where('created_at', '>=', now()->subDays(7))
                ->whereNotNull('old_values')
                ->whereNotNull('new_values')
                ->get()
                ->filter(function($log) {
                    $oldValues = json_decode($log->old_values, true);
                    $newValues = json_decode($log->new_values, true);

                    if (!is_array($oldValues) || !is_array($newValues)) {
                        return false;
                    }

                    foreach ($newValues as $key => $newValue) {
                        $oldValue = $oldValues[$key] ?? 0;
                        if (is_numeric($newValue) && is_numeric($oldValue)) {
                            $change = abs($newValue - $oldValue);
                            if ($change > 100) { // Consider changes > 100 votes as large
                                return true;
                            }
                        }
                    }
                    return false;
                })
                ->count();

            $multipleSubmissions = VoteAuditLog::where('created_at', '>=', now()->subDays(1))
                ->select('user_id', 'polling_station_id')
                ->groupBy('user_id', 'polling_station_id')
                ->havingRaw('COUNT(*) > 1')
                ->count();

            return [
                'total_flagged' => $flaggedSubmissions,
                'rapid_submissions' => $rapidSubmissions,
                'large_changes' => $largeChanges,
                'agents_with_multiple_submissions' => $multipleSubmissions,
            ];
        } catch (\Exception $e) {
            // If there's any error (like table doesn't exist), return zeros
            return [
                'total_flagged' => 0,
                'rapid_submissions' => 0,
                'large_changes' => 0,
                'agents_with_multiple_submissions' => 0,
            ];
        }
    }

    /**
     * Show detailed audit log
     */
    public function show(AuditLog $auditLog)
    {
        // Load related models for more detailed view
        $auditLog->load('user');
        
        // Get previous and next audit logs for navigation
        $previousLog = AuditLog::where('id', '<', $auditLog->id)
            ->orderBy('id', 'desc')
            ->first();
            
        $nextLog = AuditLog::where('id', '>', $auditLog->id)
            ->orderBy('id', 'asc')
            ->first();
        
        return view('admin.audit.show', compact('auditLog', 'previousLog', 'nextLog'));
    }
    
    /**
     * Get best performing users based on vote submissions and accuracy
     */
    private function getBestPerformingUsers()
    {
        // Check if VoteAuditLog model exists and table exists
        if (!class_exists('App\Models\VoteAuditLog')) {
            return collect();
        }

        try {
            // Get users with most submissions
            $mostSubmissions = User::select('users.id', 'users.name', 'users.email', 'users.user_type')
                ->join('vote_audit_logs', 'users.id', '=', 'vote_audit_logs.submitted_by_user_id')
                ->selectRaw('COUNT(vote_audit_logs.id) as submission_count')
                ->selectRaw('SUM(CASE WHEN vote_audit_logs.is_verified = 1 THEN 1 ELSE 0 END) as verified_count')
                ->selectRaw('SUM(CASE WHEN vote_audit_logs.is_flagged = 1 THEN 1 ELSE 0 END) as flagged_count')
                ->groupBy('users.id', 'users.name', 'users.email', 'users.user_type')
                ->orderByDesc('submission_count')
                ->limit(10)
                ->get()
                ->map(function($user) {
                    // Calculate accuracy score
                    $total = $user->submission_count;
                    $verified = $user->verified_count;
                    $flagged = $user->flagged_count;
                    
                    $accuracyScore = $total > 0 
                        ? round((($verified - $flagged) / $total) * 100, 1) 
                        : 0;
                    
                    // Cap at 100%
                    $accuracyScore = min(100, max(0, $accuracyScore));
                    
                    $user->accuracy_score = $accuracyScore;
                    return $user;
                });

            return $mostSubmissions;
        } catch (\Exception $e) {
            return collect();
        }
    }
}
