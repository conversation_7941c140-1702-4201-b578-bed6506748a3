<?php

namespace App\Http\Controllers;

use App\Models\Agent;
use App\Models\Candidate;
use App\Models\Eveidence;
use App\Models\PollingStation;
use App\Models\Position;
use App\Models\SpoiledVote;
use App\Models\User;
use App\Models\Vote;
use App\Services\VoteAuditService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class PollingManagerController extends Controller
{
    /**
     * Display the polling manager dashboard
     */
    public function dashboard(Request $request)
    {
        // Get filter parameters
        $search = $request->get('search');
        $subcounty = $request->get('subcounty');
        $parish = $request->get('parish');
        $village = $request->get('village');
        $status = $request->get('status'); // all, submitted, pending

        // Build polling stations query
        $query = PollingStation::with(['agent.user', 'agent.votes', 'agent.eveidences']);

        // Apply filters
        if ($search) {
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('district', 'like', "%{$search}%")
                  ->orWhere('county', 'like', "%{$search}%")
                  ->orWhere('subcounty', 'like', "%{$search}%")
                  ->orWhere('parish', 'like', "%{$search}%")
                  ->orWhere('village', 'like', "%{$search}%");
            });
        }

        if ($subcounty) {
            $query->where('subcounty', $subcounty);
        }

        if ($parish) {
            $query->where('parish', $parish);
        }

        if ($village) {
            $query->where('village', $village);
        }

        // Apply status filter
        if ($status === 'submitted') {
            $query->whereHas('agent.votes');
        } elseif ($status === 'pending') {
            $query->whereDoesntHave('agent.votes');
        }

        $pollingStations = $query->paginate(20)->withQueryString();

        // Get filter options
        $subcounties = PollingStation::distinct()->pluck('subcounty')->filter()->sort();
        $parishes = PollingStation::distinct()->pluck('parish')->filter()->sort();
        $villages = PollingStation::distinct()->pluck('village')->filter()->sort();

        // Get positions for vote submission
        $positions = Position::with('candidates')->get();

        // Get summary statistics
        $totalStations = PollingStation::count();
        $submittedStations = PollingStation::whereHas('agent.votes')->count();
        $pendingStations = $totalStations - $submittedStations;
        $totalVotes = Vote::sum('number_of_votes');

        // Handle AJAX requests for realtime search
        if ($request->ajax() || $request->get('ajax')) {
            $stationsData = $pollingStations->map(function($station) {
                return [
                    'id' => $station->id,
                    'name' => $station->name,
                    'district' => $station->district,
                    'county' => $station->county,
                    'subcounty' => $station->subcounty,
                    'parish' => $station->parish,
                    'village' => $station->village,
                    'agent' => $station->agents->first() ? [
                        'user' => [
                            'name' => $station->agents->first()->user->name,
                            'phone_number' => $station->agents->first()->user->phone_number,
                        ]
                    ] : null,
                    'has_votes' => $station->agents->first() && $station->agents->first()->votes->count() > 0,
                    'total_votes' => $station->agents->sum(function($agent) { return $agent->votes->sum('number_of_votes'); }),
                    'evidence_count' => $station->agents->sum(function($agent) { return $agent->eveidences->count(); }),
                ];
            });

            return response()->json([
                'stations' => $stationsData,
                'total' => $pollingStations->total(),
                'submitted' => $submittedStations,
                'pending' => $pendingStations,
                'pagination' => [
                    'current_page' => $pollingStations->currentPage(),
                    'last_page' => $pollingStations->lastPage(),
                    'per_page' => $pollingStations->perPage(),
                    'total' => $pollingStations->total(),
                ]
            ]);
        }

        return view('manager.dashboard', compact(
            'pollingStations',
            'subcounties',
            'parishes',
            'villages',
            'positions',
            'totalStations',
            'submittedStations',
            'pendingStations',
            'totalVotes'
        ));
    }

    /**
     * Show vote submission form for a specific polling station
     */
    public function showVoteForm(PollingStation $station)
    {
        $positions = Position::with('candidates')->get();
        $agent = $station->agent;

        // Get existing votes for this station
        $existingVotes = [];
        $existingEvidence = collect();

        if ($agent) {
            $existingVotes = Vote::where('agent_id', $agent->id)
                ->pluck('number_of_votes', 'candidate_id')
                ->toArray();

            // Get existing evidence for this agent
            $existingEvidence = $agent->eveidences;
        } else {
            // If no agent, check if there are any votes submitted by current user for this station
            $tempAgent = Agent::where('user_id', Auth::id())
                ->where('polling_station_id', $station->id)
                ->first();

            if ($tempAgent) {
                $existingVotes = Vote::where('agent_id', $tempAgent->id)
                    ->pluck('number_of_votes', 'candidate_id')
                    ->toArray();
                $existingEvidence = $tempAgent->eveidences;
            }
        }

        return view('manager.vote-form', compact('station', 'positions', 'agent', 'existingVotes', 'existingEvidence'));
    }

    /**
     * Submit votes and evidence for a polling station
     */
    public function submitVotes(Request $request, PollingStation $station)
    {
        $request->validate([
            'votes' => 'required|array',
            'votes.*' => 'required|integer|min:0',
            'evidence_files.*' => 'nullable|file|mimes:jpg,jpeg,png,pdf,mp4,mov,avi,wmv,flv,webm|max:51200', // 50MB max for videos
            'file_names.*' => 'nullable|string|max:255',
            'comments' => 'nullable|string|max:1000',
        ]);

        // Get the first available agent or create one if none exists
        $agent = $station->agents()->first();

        // If no agent exists, create one using the current user
        if (!$agent) {
            $agent = new Agent();
            $agent->user_id = Auth::id();
            $agent->polling_station_id = $station->id;
            $agent->save();
        }

        DB::beginTransaction();
        try {
            $auditService = new VoteAuditService();
            $uploadedFiles = [];

            // Process vote submissions
            foreach ($request->votes as $candidateId => $voteCount) {
                // Get existing vote to determine action type and previous count
                $existingVote = Vote::where('agent_id', $agent->id)
                    ->where('candidate_id', $candidateId)
                    ->first();

                $actionType = $existingVote ? 'update' : 'create';
                $previousVotes = $existingVote ? $existingVote->number_of_votes : 0;

                // Log the submission in audit trail BEFORE updating the vote
                $notes = "Votes submitted by polling manager for {$station->name}";
                if ($request->filled('comments')) {
                    $notes .= ". Manager comments: " . $request->input('comments');
                }

                $auditService->logVoteSubmission(
                    $agent->id,
                    $candidateId,
                    $voteCount,
                    $actionType,
                    'manager_portal',
                    $request,
                    $notes
                );

                // Update or create vote record AFTER logging
                Vote::updateOrCreate(
                    [
                        'agent_id' => $agent->id,
                        'candidate_id' => $candidateId
                    ],
                    [
                        'number_of_votes' => $voteCount,
                        'latitude' => $station->latitude,
                        'longitude' => $station->longitude,
                    ]
                );
            }

            // Process evidence uploads if any files are provided
            if ($request->hasFile('evidence_files')) {
                foreach ($request->file('evidence_files') as $index => $file) {
                    if ($file && $file->isValid()) {
                        $fileName = $request->file_names[$index] ?? 'Evidence ' . ($index + 1);

                        // Upload the file
                        $fileUrl = User::uploadImage($file);

                        if (!$fileUrl) {
                            throw new \Exception("Failed to upload file: {$fileName}");
                        }

                        $evidence = new Eveidence();
                        $evidence->file_url = $fileUrl;
                        $evidence->file_name = $fileName;
                        $evidence->agent_id = $agent->id;

                        if (!$evidence->save()) {
                            throw new \Exception("Failed to save evidence record: {$fileName}");
                        }

                        $uploadedFiles[] = $fileName;
                    } else {
                        Log::warning("Invalid file upload at index {$index}", [
                            'file_name' => $file ? $file->getClientOriginalName() : 'null',
                            'file_error' => $file ? $file->getError() : 'file is null'
                        ]);
                    }
                }
            }

            DB::commit();

            // Create success message
            $message = "Votes submitted and logged successfully for {$station->name}";
            if (!empty($uploadedFiles)) {
                $message .= ". Evidence uploaded: " . implode(', ', $uploadedFiles);
            }

            return redirect()->route('manager.dashboard')->with('success', $message);

        } catch (\Exception $e) {
            DB::rollback();
            Log::error('Vote submission failed', [
                'station_id' => $station->id,
                'agent_id' => $agent->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return redirect()->back()
                ->with('error', 'Failed to submit votes and evidence: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Show evidence upload form for a polling station
     */
    public function showEvidenceForm(PollingStation $station)
    {
        $agent = $station->agent;
        $existingEvidence = [];
        
        if ($agent) {
            $existingEvidence = $agent->eveidences;
        }

        return view('manager.evidence-form', compact('station', 'agent', 'existingEvidence'));
    }

    /**
     * Upload evidence for a polling station
     */
    public function uploadEvidence(Request $request, PollingStation $station)
    {
        $request->validate([
            'evidence_files.*' => 'required|file|mimes:jpg,jpeg,png,pdf,mp4,mov,avi,wmv,flv,webm|max:51200', // 50MB max for videos
            'file_names.*' => 'nullable|string|max:255',
        ]);

        // Get the first available agent or create one if none exists
        $agent = $station->agents()->first();

        // If no agent exists, create one using the current user
        if (!$agent) {
            $agent = new Agent();
            $agent->user_id = Auth::id();
            $agent->polling_station_id = $station->id;
            $agent->save();
        }

        $uploadedFiles = [];
        
        if ($request->hasFile('evidence_files')) {
            foreach ($request->file('evidence_files') as $index => $file) {
                $fileName = $request->file_names[$index] ?? 'Evidence ' . ($index + 1);
                
                $evidence = new Eveidence();
                $evidence->file_url = User::uploadImage($file);
                $evidence->file_name = $fileName;
                $evidence->agent_id = $agent->id;
                $evidence->save();
                
                $uploadedFiles[] = $fileName;
            }
        }

        $message = 'Evidence uploaded successfully for ' . $station->name;
        if (!empty($uploadedFiles)) {
            $message .= ': ' . implode(', ', $uploadedFiles);
        }

        return redirect()->route('manager.dashboard')->with('success', $message);
    }

    /**
     * Submit spoiled votes for a polling station
     */
    public function submitSpoiledVotes(Request $request, PollingStation $station)
    {
        $request->validate([
            'spoiled_votes' => 'required|array',
            'spoiled_votes.*.position_id' => 'required|exists:positions,id',
            'spoiled_votes.*.number_of_votes' => 'required|integer|min:0',
            'spoiled_votes.*.remarks' => 'nullable|string|max:500',
        ]);

        // Get the first available agent or create one if none exists
        $agent = $station->agents()->first();

        // If no agent exists, create one using the current user
        if (!$agent) {
            $agent = new Agent();
            $agent->user_id = Auth::id();
            $agent->polling_station_id = $station->id;
            $agent->save();
        }

        DB::beginTransaction();
        try {
            foreach ($request->spoiled_votes as $spoiledVote) {
                if ($spoiledVote['number_of_votes'] > 0) {
                    SpoiledVote::updateOrCreate(
                        [
                            'polling_station_id' => $station->id,
                            'position_id' => $spoiledVote['position_id'],
                            'agent_id' => $agent->id,
                        ],
                        [
                            'number_of_votes' => $spoiledVote['number_of_votes'],
                            'remarks' => $spoiledVote['remarks'] ?? null,
                        ]
                    );
                }
            }

            DB::commit();
            return redirect()->route('manager.dashboard')
                ->with('success', "Spoiled votes submitted successfully for {$station->name}");

        } catch (\Exception $e) {
            DB::rollback();
            return redirect()->back()
                ->with('error', 'Failed to submit spoiled votes. Please try again.')
                ->withInput();
        }
    }

    /**
     * Get polling station details via AJAX
     */
    public function getStationDetails(PollingStation $station)
    {
        $station->load(['agents.user', 'agents.votes.candidate', 'agents.eveidences']);

        return response()->json([
            'station' => $station,
            'vote_summary' => $this->getVoteSummary($station),
            'evidence_count' => $station->agents->sum(function($agent) { return $agent->eveidences->count(); }),
        ]);
    }

    /**
     * Get vote summary for a station
     */
    private function getVoteSummary(PollingStation $station)
    {
        if (!$station->agent) {
            return [];
        }

        $votes = Vote::where('agent_id', $station->agent->id)
            ->with('candidate.position')
            ->get()
            ->groupBy('candidate.position.name');

        $summary = [];
        foreach ($votes as $position => $positionVotes) {
            $summary[$position] = [
                'total_votes' => $positionVotes->sum('number_of_votes'),
                'candidates' => $positionVotes->map(function($vote) {
                    return [
                        'name' => $vote->candidate->name,
                        'votes' => $vote->number_of_votes
                    ];
                })
            ];
        }

        return $summary;
    }

    /**
     * Get parishes for a specific subcounty (for cascading filters)
     */
    public function getParishesBySubcounty(Request $request)
    {
        $subcounty = $request->get('subcounty');

        $query = PollingStation::distinct()->whereNotNull('parish')->where('parish', '!=', '');

        if ($subcounty) {
            $query->where('subcounty', $subcounty);
        }

        $parishes = $query->pluck('parish')->sort()->values();

        return response()->json([
            'parishes' => $parishes
        ]);
    }

    /**
     * Get villages for a specific parish (for cascading filters)
     */
    public function getVillagesByParish(Request $request)
    {
        $subcounty = $request->get('subcounty');
        $parish = $request->get('parish');

        $query = PollingStation::distinct()->whereNotNull('village')->where('village', '!=', '');

        if ($subcounty) {
            $query->where('subcounty', $subcounty);
        }

        if ($parish) {
            $query->where('parish', $parish);
        }

        $villages = $query->pluck('village')->sort()->values();

        return response()->json([
            'villages' => $villages
        ]);
    }
}
