<?php

namespace App\Http\Controllers;

use App\Models\Agent;
use App\Models\Eveidence;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class EveidenceController extends Controller
{
    /**
     * View Latest Evidence
     * @group Votes
     * @response   {
     *   "status": "success",
     *   "message": "Latest uploaded files",
     *   "evedence": [
     *       {
     *           "id": 2,
     *           "created_at": "2025-07-05T10:43:49.000000Z",
     *           "updated_at": "2025-07-05T10:43:49.000000Z",
     *           "agent_id": 1,
     *           "file_url": "1751708629XsEueTFKd760.jpg",
     *           "file_name": "Latest DR FORM"
     *       },
     *       {
     *           "id": 1,
     *           "created_at": "2025-07-05T09:43:49.000000Z",
     *           "updated_at": "2025-07-05T09:43:49.000000Z",
     *           "agent_id": 1,
     *           "file_url": "1751708629XsEueTFKd761.jpg",
     *           "file_name": "Previous DR FORM"
     *       }
     *   ]
     * }
     * @response 200 {
     *   "status": "success",
     *   "message": "No files uploaded",
     *   "evedence": []
     * }
    **/
    public function index()
    {
        $agent = Agent::where('user_id',Auth::id())->get()->last();

        if(!$agent){
            return  response()->json(['status' => 'failed', 'message' => 'Only Agents can view their own results'],422);
        }

        // Get the latest 2 evidence records for this agent
        $latestEvidence = Eveidence::where('agent_id',$agent->id)
            ->latest('created_at')
            ->limit(2)
            ->get();

        $data = [
            'status' => 'success',
            'message' => $latestEvidence->count() > 0 ? 'Latest uploaded files' : 'No files uploaded',
            'evedence' => $latestEvidence,
        ];

        return  response()->json($data,200);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /** 
     * Store Evedence
     * @group Votes 
     * @bodyParam picture file required
     * @bodyParam file_name string
     * @response  {
     *       "status": "success",
     *       "message": "Evendance uploaded successfully",
     *       "evedence": {
     *           "file_url": "1751708629XsEueTFKd760.jpg",
     *           "file_name": "DR FORM",
     *           "agent_id": 1,
     *           "updated_at": "2025-07-05T09:43:49.000000Z",
     *           "created_at": "2025-07-05T09:43:49.000000Z",
     *           "id": 1
     *       }
     *   }
    **/

    public function store(Request $request)
    {
        try {
            // Enhanced validation with better error messages
            $rules = [
                'picture' => 'required|file|mimes:jpeg,jpg,png,pdf,mp4,mov,avi,wmv,flv,webm|max:51200', // 50MB max for videos
                'file_name' => 'nullable|string|max:255',
            ];

            $messages = [
                'picture.required' => 'Evidence file is required',
                'picture.file' => 'Picture must be a valid file',
                'picture.mimes' => 'File must be JPEG, JPG, PNG, PDF, MP4, MOV, AVI, WMV, FLV, or WEBM',
                'picture.max' => 'File size must not exceed 50MB',
                'file_name.max' => 'File name must not exceed 255 characters',
            ];

            $validator = Validator::make($request->all(), $rules, $messages);

            if ($validator->fails()) {
                return response()->json([
                    'status' => 'failed',
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            // Check if user is authenticated
            if (!Auth::check()) {
                return response()->json([
                    'status' => 'failed',
                    'message' => 'Authentication required'
                ], 401);
            }

            // Find agent record with better error handling
            $agent = Agent::where('user_id', Auth::id())->first();

            if (!$agent) {
                return response()->json([
                    'status' => 'failed',
                    'message' => 'Only Agents can post results. No agent record found for this user.'
                ], 422);
            }

            // Check if files directory exists and is writable
            $filesDir = public_path('files');
            if (!is_dir($filesDir)) {
                if (!mkdir($filesDir, 0755, true)) {
                    return response()->json([
                        'status' => 'failed',
                        'message' => 'Unable to create files directory'
                    ], 500);
                }
            }

            if (!is_writable($filesDir)) {
                return response()->json([
                    'status' => 'failed',
                    'message' => 'Files directory is not writable'
                ], 500);
            }

            // Upload the image with error handling
            $uploadedFile = $request->file('picture');

            if (!$uploadedFile || !$uploadedFile->isValid()) {
                return response()->json([
                    'status' => 'failed',
                    'message' => 'Invalid file upload'
                ], 422);
            }

            $fileUrl = User::uploadImage($uploadedFile);

            if (!$fileUrl) {
                return response()->json([
                    'status' => 'failed',
                    'message' => 'Failed to upload image'
                ], 500);
            }

            // Create evidence record
            $evedence = new Eveidence();
            $evedence->file_url = $fileUrl;
            $evedence->file_name = $request->file_name;
            $evedence->agent_id = $agent->id;

            if (!$evedence->save()) {
                // Clean up uploaded file if database save fails
                $filePath = public_path('files/' . $fileUrl);
                if (file_exists($filePath)) {
                    unlink($filePath);
                }

                return response()->json([
                    'status' => 'failed',
                    'message' => 'Failed to save evidence record'
                ], 500);
            }

            // Return success response for web requests
            if (!$request->is('api/*')) {
                return back()->with('success', 'Evidence uploaded successfully');
            }

            // Return JSON response for API requests
            $data = [
                'status' => 'success',
                'message' => 'Evendance uploaded successfully',
                'evedence' => $evedence,
            ];

            return response()->json($data, 200);

        } catch (\Exception $e) {
            // Enhanced error logging for production debugging
            $errorContext = [
                'user_id' => Auth::id(),
                'user_type' => Auth::check() ? Auth::user()->user_type : null,
                'file_name' => $request->file_name ?? null,
                'has_file' => $request->hasFile('picture'),
                'file_size' => $request->hasFile('picture') ? $request->file('picture')->getSize() : null,
                'file_mime' => $request->hasFile('picture') ? $request->file('picture')->getMimeType() : null,
                'files_dir_exists' => is_dir(public_path('files')),
                'files_dir_writable' => is_writable(public_path('files')),
                'error_message' => $e->getMessage(),
                'error_file' => $e->getFile(),
                'error_line' => $e->getLine(),
                'timestamp' => now()->toISOString(),
                'request_ip' => $request->ip(),
                'user_agent' => $request->userAgent(),
            ];

            Log::error('Evidence upload error - Production Debug', $errorContext);

            // Also log the full stack trace separately for detailed debugging
            Log::error('Evidence upload stack trace', [
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'status' => 'failed',
                'message' => 'An unexpected error occurred while uploading evidence',
                'error' => config('app.debug') ? $e->getMessage() : 'Server Error',
                'error_id' => uniqid('err_', true) // Unique error ID for tracking
            ], 500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(Eveidence $eveidence)
    {
        //
    }

    /**
     * Production diagnostic endpoint
     * GET /api/record_envidence/diagnostic
     */
    public function diagnostic(Request $request)
    {
        try {
            $diagnostics = [
                'timestamp' => now()->toISOString(),
                'environment' => app()->environment(),
                'php_version' => PHP_VERSION,
                'laravel_version' => app()->version(),
            ];

            // Check authentication
            if (!Auth::check()) {
                $diagnostics['auth_status'] = 'not_authenticated';
                return response()->json([
                    'status' => 'diagnostic',
                    'data' => $diagnostics
                ]);
            }

            $user = Auth::user();
            $diagnostics['auth_status'] = 'authenticated';
            $diagnostics['user_type'] = $user->user_type;
            $diagnostics['user_id'] = $user->id;

            // Check agent record
            $agent = Agent::where('user_id', $user->id)->first();
            if (!$agent) {
                $diagnostics['agent_status'] = 'no_agent_record';
            } else {
                $diagnostics['agent_status'] = 'agent_found';
                $diagnostics['agent_id'] = $agent->id;
            }

            // Check files directory
            $filesDir = public_path('files');
            $diagnostics['files_directory'] = [
                'exists' => is_dir($filesDir),
                'writable' => is_writable($filesDir),
                'path' => $filesDir,
            ];

            // Check database connectivity
            try {
                $evidenceCount = Eveidence::count();
                $diagnostics['database'] = [
                    'connected' => true,
                    'evidence_count' => $evidenceCount,
                ];
            } catch (\Exception $e) {
                $diagnostics['database'] = [
                    'connected' => false,
                    'error' => $e->getMessage(),
                ];
            }

            // Check PHP extensions
            $requiredExtensions = ['gd', 'fileinfo', 'json'];
            $diagnostics['php_extensions'] = [];
            foreach ($requiredExtensions as $ext) {
                $diagnostics['php_extensions'][$ext] = extension_loaded($ext);
            }

            // Check PHP configuration
            $diagnostics['php_config'] = [
                'upload_max_filesize' => ini_get('upload_max_filesize'),
                'post_max_size' => ini_get('post_max_size'),
                'max_execution_time' => ini_get('max_execution_time'),
                'memory_limit' => ini_get('memory_limit'),
            ];

            return response()->json([
                'status' => 'diagnostic',
                'data' => $diagnostics
            ]);

        } catch (\Exception $e) {
            Log::error('Diagnostic endpoint error: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'status' => 'diagnostic_error',
                'message' => 'Diagnostic failed',
                'error' => config('app.debug') ? $e->getMessage() : 'Internal error'
            ], 500);
        }
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Eveidence $eveidence)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Eveidence $eveidence)
    {
        //
    }

    /** 
     * Delete Evedence
     * @group Votes 
     * @urlParam eveidence_id integer required
     * @response   {
     *       "status": "success",
     *       "message": "Evendance deleted successfully"
     *   }
    **/

    public function destroy(Eveidence $record_envidence)
    {
        $record_envidence->delete();

        $data = [
            'status' => 'success', 
            'message'=>'Evendance deleted successfully',             
           
        ];
        
        return  response()->json($data,200);
    }

}
