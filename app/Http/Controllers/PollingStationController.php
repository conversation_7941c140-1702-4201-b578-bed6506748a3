<?php

namespace App\Http\Controllers;

use App\Models\Agent;
use App\Models\Eveidence;
use App\Models\PollingStation;
use App\Models\Position;
use App\Models\Role;
use App\Models\User;
use App\Models\Vote;
use App\Services\VoteAuditService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class PollingStationController extends Controller
{
    /**
     * Check if coordinates are within Uganda
     *
     * @param float|null $latitude
     * @param float|null $longitude
     * @return bool
     */
    private function isWithinUganda($latitude, $longitude)
    {
        // If either coordinate is null, skip validation
        if ($latitude === null || $longitude === null) {
            return true;
        }
        
        // Uganda's approximate bounding box
        $ugandaBounds = [
            'minLat' => -1.5, // Southern border
            'maxLat' => 4.3,  // Northern border
            'minLng' => 29.5, // Western border
            'maxLng' => 35.0  // Eastern border
        ];
        
        return $latitude >= $ugandaBounds['minLat'] && 
               $latitude <= $ugandaBounds['maxLat'] && 
               $longitude >= $ugandaBounds['minLng'] && 
               $longitude <= $ugandaBounds['maxLng'];
    }
    
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        // Permission check is handled by middleware

        // Build polling stations query with filters
        $pollingStationsQuery = PollingStation::with(['agents.user']);

        // Apply filters
        if ($request->filled('search')) {
            $search = $request->get('search');
            $pollingStationsQuery->where(function($query) use ($search) {
                $query->where('name', 'like', "%{$search}%")
                      ->orWhere('district', 'like', "%{$search}%")
                      ->orWhere('county', 'like', "%{$search}%")
                      ->orWhere('subcounty', 'like', "%{$search}%")
                      ->orWhere('parish', 'like', "%{$search}%")
                      ->orWhere('village', 'like', "%{$search}%");
            });
        }

        if ($request->filled('district')) {
            $pollingStationsQuery->where('district', $request->get('district'));
        }

        if ($request->filled('county')) {
            $pollingStationsQuery->where('county', $request->get('county'));
        }

        if ($request->filled('subcounty')) {
            $pollingStationsQuery->where('subcounty', $request->get('subcounty'));
        }

        if ($request->filled('coordinates')) {
            if ($request->get('coordinates') === 'with') {
                $pollingStationsQuery->whereNotNull('latitude')->whereNotNull('longitude');
            } elseif ($request->get('coordinates') === 'without') {
                $pollingStationsQuery->where(function($query) {
                    $query->whereNull('latitude')->orWhereNull('longitude');
                });
            }
        }

        if ($request->filled('agents')) {
            if ($request->get('agents') === 'with') {
                $pollingStationsQuery->whereHas('agents');
            } elseif ($request->get('agents') === 'without') {
                $pollingStationsQuery->whereDoesntHave('agents');
            }
        }

        if ($request->filled('results')) {
            if ($request->get('results') === 'submitted') {
                $pollingStationsQuery->whereHas('agents.votes');
            } elseif ($request->get('results') === 'pending') {
                $pollingStationsQuery->whereHas('agents')->whereDoesntHave('agents.votes');
            }
        }

        if ($request->filled('evidence')) {
            if ($request->get('evidence') === 'with') {
                $pollingStationsQuery->whereHas('agents.eveidences');
            } elseif ($request->get('evidence') === 'without') {
                $pollingStationsQuery->whereHas('agents')->whereDoesntHave('agents.eveidences');
            }
        }

        // Get pagination parameters
        $perPage = $request->get('per_page', 20);
        $perPage = in_array($perPage, [10, 20, 50, 100]) ? $perPage : 20;

        // Apply sorting
        $sortBy = $request->get('sort_by', 'name');
        $sortOrder = $request->get('sort_order', 'asc');

        if (in_array($sortBy, ['name', 'district', 'county', 'subcounty', 'created_at'])) {
            $pollingStationsQuery->orderBy($sortBy, $sortOrder);
        } else {
            $pollingStationsQuery->orderBy('name', 'asc');
        }

        // Paginate results
        $polling_stations = $pollingStationsQuery->paginate($perPage)->appends($request->query());

        // Get filter options for dropdowns
        $districts = PollingStation::distinct()->whereNotNull('district')->pluck('district')->sort();
        $counties = PollingStation::distinct()->whereNotNull('county')->pluck('county')->sort();
        $subcounties = PollingStation::distinct()->whereNotNull('subcounty')->pluck('subcounty')->sort();

        // Get statistics
        $totalStations = PollingStation::count();
        $stationsWithCoordinates = PollingStation::whereNotNull('latitude')->whereNotNull('longitude')->count();
        $stationsWithAgents = PollingStation::whereHas('agents')->count();
        $stationsWithResults = PollingStation::whereHas('agents.votes')->count();

        // Get positions for vote calculations
        $positions = \App\Models\Position::with(['candidates'])->get();

        // Get spoiled votes data
        $spoiledVotesByStation = \App\Models\SpoiledVote::with('agent.polling_station')
            ->selectRaw('agent_id, SUM(number_of_votes) as total')
            ->groupBy('agent_id')
            ->get()
            ->mapWithKeys(function($item) {
                return [$item->agent->polling_station_id ?? 0 => $item];
            });

        $data = [
            'polling_stations' => $polling_stations,
            'districts' => $districts,
            'counties' => $counties,
            'subcounties' => $subcounties,
            'totalStations' => $totalStations,
            'stationsWithCoordinates' => $stationsWithCoordinates,
            'stationsWithAgents' => $stationsWithAgents,
            'stationsWithResults' => $stationsWithResults,
            'positions' => $positions,
            'spoiledVotesByStation' => $spoiledVotesByStation,
            'title' => 'Polling Stations',
            'currentFilters' => $request->only(['search', 'district', 'county', 'subcounty', 'coordinates', 'agents', 'results', 'evidence', 'per_page', 'sort_by', 'sort_order'])
        ];

        return view('polling_stations.list')->with($data);
    }

    /**
     * Display polling stations with the same functionality as home page
     *
     * @return \Illuminate\Http\Response
     */
    public function pollingStations(Request $request)
    {
        // Get positions with optimized loading
        $positions = \App\Models\Position::with(['candidates' => function($query) {
            $query->with('monitoring');
        }])->get();

        // Build polling stations query with filters (same as home page)
        $pollingStationsQuery = PollingStation::with(['agents.user']);

        // Apply filters (same logic as home page)
        if ($request->filled('search')) {
            $search = $request->get('search');
            $pollingStationsQuery->where(function($query) use ($search) {
                $query->where('name', 'like', "%{$search}%")
                      ->orWhere('district', 'like', "%{$search}%")
                      ->orWhere('county', 'like', "%{$search}%")
                      ->orWhere('subcounty', 'like', "%{$search}%")
                      ->orWhere('parish', 'like', "%{$search}%")
                      ->orWhere('village', 'like', "%{$search}%");
            });
        }

        if ($request->filled('district')) {
            $pollingStationsQuery->where('district', $request->get('district'));
        }

        if ($request->filled('county')) {
            $pollingStationsQuery->where('county', $request->get('county'));
        }

        if ($request->filled('subcounty')) {
            $pollingStationsQuery->where('subcounty', $request->get('subcounty'));
        }

        if ($request->filled('coordinates')) {
            $coordinates = $request->get('coordinates');
            if ($coordinates === 'with') {
                $pollingStationsQuery->whereNotNull('latitude')->whereNotNull('longitude');
            } elseif ($coordinates === 'without') {
                $pollingStationsQuery->where(function($q) {
                    $q->whereNull('latitude')->orWhereNull('longitude');
                });
            }
        }

        if ($request->filled('agents')) {
            $agents = $request->get('agents');
            if ($agents === 'with') {
                $pollingStationsQuery->whereHas('agents');
            } elseif ($agents === 'without') {
                $pollingStationsQuery->whereDoesntHave('agents');
            }
        }

        if ($request->filled('results')) {
            $results = $request->get('results');
            if ($results === 'submitted') {
                $pollingStationsQuery->whereHas('agents.votes');
            } elseif ($results === 'pending') {
                $pollingStationsQuery->whereDoesntHave('agents.votes');
            }
        }

        if ($request->filled('evidence')) {
            $evidence = $request->get('evidence');
            if ($evidence === 'with') {
                $pollingStationsQuery->whereHas('agents.eveidences');
            } elseif ($evidence === 'without') {
                $pollingStationsQuery->whereDoesntHave('agents.eveidences');
            }
        }

        // Get pagination parameters
        $perPage = $request->get('per_page', 15);
        $perPage = in_array($perPage, [10, 15, 25, 50, 100]) ? $perPage : 15;

        // Paginate results for the table
        $polling_stations = $pollingStationsQuery->paginate($perPage)->appends($request->query());

        // Get filter options for dropdowns
        $districts = PollingStation::distinct()->whereNotNull('district')->pluck('district')->sort();
        $counties = PollingStation::distinct()->whereNotNull('county')->pluck('county')->sort();
        $subcounties = PollingStation::distinct()->whereNotNull('subcounty')->pluck('subcounty')->sort();

        // Get spoiled votes data (same as home page)
        $spoiledVotesTotal = \App\Models\SpoiledVote::sum('number_of_votes');
        $spoiledVotesByPosition = \App\Models\SpoiledVote::with('position')
            ->selectRaw('position_id, SUM(number_of_votes) as total')
            ->groupBy('position_id')
            ->get()
            ->keyBy('position_id');

        $spoiledVotesByStation = \App\Models\SpoiledVote::with('agent.polling_station')
            ->selectRaw('agent_id, SUM(number_of_votes) as total')
            ->groupBy('agent_id')
            ->get()
            ->mapWithKeys(function($item) {
                return [$item->agent->polling_station_id ?? 0 => $item];
            });

        $data = [
            'positions' => $positions,
            'title' => 'Polling Stations',
            'polling_stations' => $polling_stations,
            'districts' => $districts,
            'counties' => $counties,
            'subcounties' => $subcounties,
            'spoiledVotesTotal' => $spoiledVotesTotal,
            'spoiledVotesByPosition' => $spoiledVotesByPosition,
            'spoiledVotesByStation' => $spoiledVotesByStation,
            'currentFilters' => $request->only(['search', 'district', 'county', 'subcounty', 'coordinates', 'agents', 'results', 'evidence', 'per_page'])
        ];

        return view('polling_stations.stations')->with($data);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        // Permission check is handled by middleware, no need for additional checks here
        $data = [
            'title' => 'Create Polling Station'
        ];

        return view('polling_stations.create')->with($data);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $rules = [
            'name' => 'required',
            'district' => 'required',
            'county' => 'required',
            'subcounty' => 'required',
            'parish' => 'required',
            'village' => 'required',
            'latitude' => 'nullable|numeric|between:-90,90',
            'longitude' => 'nullable|numeric|between:-180,180',
        ];

        // Validate request
        $validator = Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            // Handle AJAX requests
            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'errors' => $validator->errors()
                ], 422);
            }

            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        // Check if coordinates are within Uganda
        if ($request->filled('latitude') && $request->filled('longitude')) {
            $latitude = $request->latitude;
            $longitude = $request->longitude;

            if (!$this->isWithinUganda($latitude, $longitude)) {
                $errorMessage = 'The coordinates must be within Uganda.';

                // Handle AJAX requests
                if ($request->ajax()) {
                    return response()->json([
                        'success' => false,
                        'message' => $errorMessage,
                        'errors' => ['coordinates' => [$errorMessage]]
                    ], 422);
                }

                return redirect()->back()
                    ->withInput()
                    ->withErrors(['coordinates' => $errorMessage]);
            }
        }

        $savePollingStation = new PollingStation();
        $savePollingStation->name = $request->name;
        $savePollingStation->constituency = $request->constituency;
        $savePollingStation->district = $request->district;
        $savePollingStation->county = $request->county;
        $savePollingStation->subcounty = $request->subcounty;
        $savePollingStation->parish = $request->parish;
        $savePollingStation->village = $request->village;
        $savePollingStation->latitude = $request->latitude;
        $savePollingStation->longitude = $request->longitude;

        $savePollingStation->save();

        // Handle AJAX requests (for modal)
        if (request()->ajax()) {
            return response()->json([
                'success' => true,
                'message' => 'Polling station created successfully.',
                'station' => $savePollingStation
            ]);
        }

        return redirect()->route('polling_stations.index')->with('success', 'Polling station created successfully.');

    }

    /**
     * Display the specified resource.
     */
    public function show(PollingStation $pollingStation)
    {
        // Permission check is handled by middleware

        $positions = Position::get();

        $agents = Agent::with('user')->where('polling_station_id',$pollingStation->id)->get();

        $data = [
            'agents'=>$agents,
            'title'=>'Agents in '.$pollingStation->name,
            'pollingStation'=>$pollingStation,
            'positions'=>$positions,
        ];

        return view('polling_stations.agents')->with($data);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(PollingStation $pollingStation)
    {
        $data = [
            'pollingStation'=>$pollingStation,
            'title'=>'Edit Polling Station'
        ];

        return view('polling_stations.edit_polling_station')->with($data);


    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, $pollingStation_id)
    {
        $rules = [
            'name' => 'required',
            'district' => 'required',
            'county' => 'required',
            'subcounty' => 'required',
            'parish' => 'required',
            'village' => 'required',
            'latitude' => 'nullable|numeric|between:-90,90',
            'longitude' => 'nullable|numeric|between:-180,180',
        ];

        $this->validate($request, $rules);

        // Check if coordinates are within Uganda
        if ($request->filled('latitude') && $request->filled('longitude')) {
            $latitude = $request->latitude;
            $longitude = $request->longitude;

            if (!$this->isWithinUganda($latitude, $longitude)) {
                return redirect()->back()
                    ->withInput()
                    ->withErrors(['coordinates' => 'The coordinates must be within Uganda.']);
            }
        }

        $savePollingStation = PollingStation::findOrFail($pollingStation_id);
        $savePollingStation->name = $request->name;
        $savePollingStation->constituency = $request->constituency;
        $savePollingStation->district = $request->district;
        $savePollingStation->county = $request->county;
        $savePollingStation->subcounty = $request->subcounty;
        $savePollingStation->parish = $request->parish;
        $savePollingStation->village = $request->village;
        $savePollingStation->latitude = $request->latitude;
        $savePollingStation->longitude = $request->longitude;

        $savePollingStation->save();

        return redirect()->route('polling_stations.index')->with('success', 'Polling station updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($pollingStation)
    {
        try {
            PollingStation::destroy($pollingStation);
        } catch (\Throwable $th) {}

        return back();
    }

    /**
     * Get polling stations data for map display
     */
    public function getMapData()
    {
        $polling_stations = PollingStation::with('agents')->get();
        $positions = Position::with('candidates')->get();

        $mapData = [];

        foreach ($polling_stations as $station) {
            $stationData = [
                'id' => $station->id,
                'name' => $station->name,
                'latitude' => $station->latitude ?: (0.3476 + (rand(-100, 100) / 1000)), // Default to Uganda area with some randomness
                'longitude' => $station->longitude ?: (32.5825 + (rand(-200, 200) / 1000)),
                'votes' => []
            ];

            // Calculate votes for each candidate at this station
            foreach ($positions as $position) {
                foreach ($position->candidates as $candidate) {
                    $votes = Vote::join('agents', 'votes.agent_id', '=', 'agents.id')
                        ->where('agents.polling_station_id', $station->id)
                        ->where('votes.candidate_id', $candidate->id)
                        ->sum('votes.number_of_votes') ?? 0;

                    $stationData['votes'][$candidate->id] = $votes;
                }
            }

            $mapData[] = $stationData;
        }

        return response()->json($mapData);
    }

    /**
     * Get evidence files for a specific polling station
     */
    public function getStationEvidence($stationId)
    {
        try {
            $pollingStation = PollingStation::with(['agents.eveidences', 'agents.user'])->findOrFail($stationId);

            // Check if station has any agents
            if ($pollingStation->agents->isEmpty()) {
                return response()->json([
                    'success' => false,
                    'message' => 'No agent assigned to this polling station',
                    'evidence' => [],
                    'station_name' => $pollingStation->name
                ]);
            }

            // Get evidence from all agents (but typically there should be only one)
            $allEvidence = collect();
            $agentNames = [];

            foreach ($pollingStation->agents as $agent) {
                if ($agent->eveidences) {
                    // Get latest 2 evidence records for this agent (as per user preference)
                    $latestEvidence = $agent->eveidences()
                        ->latest('created_at')
                        ->limit(2)
                        ->get();

                    $allEvidence = $allEvidence->merge($latestEvidence);
                    $agentNames[] = $agent->user->name;
                }
            }

            // Sort by created_at descending and take latest 2 overall
            $evidence = $allEvidence->sortByDesc('created_at')->take(2)->map(function($evidence) {
                return [
                    'id' => $evidence->id,
                    'file_url' => $evidence->file_url,
                    'file_name' => $evidence->file_name ?: 'Evidence_' . $evidence->id,
                    'created_at' => $evidence->created_at->format('Y-m-d H:i:s'),
                    'agent_name' => $evidence->agent->user->name ?? 'Unknown Agent'
                ];
            })->values();

            return response()->json([
                'success' => true,
                'evidence' => $evidence,
                'station_name' => $pollingStation->name,
                'agent_names' => implode(', ', $agentNames),
                'count' => $evidence->count()
            ]);

        } catch (\Exception $e) {
            Log::error('Error fetching station evidence: ' . $e->getMessage(), [
                'station_id' => $stationId,
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch evidence: ' . $e->getMessage(),
                'evidence' => []
            ], 500);
        }
    }

    /**
     * Display polling villages with administrative hierarchy
     */
    public function pollingVillages(Request $request)
    {
        // Permission check is handled by middleware

        // Build polling stations query with filters
        $pollingStationsQuery = PollingStation::with(['agents.user', 'agents.eveidences']);

        // Apply filters
        if ($request->filled('search')) {
            $search = $request->get('search');
            $pollingStationsQuery->where(function($query) use ($search) {
                $query->where('name', 'like', "%{$search}%")
                      ->orWhere('district', 'like', "%{$search}%")
                      ->orWhere('county', 'like', "%{$search}%")
                      ->orWhere('subcounty', 'like', "%{$search}%")
                      ->orWhere('parish', 'like', "%{$search}%")
                      ->orWhere('village', 'like', "%{$search}%");
            });
        }

        if ($request->filled('district')) {
            $pollingStationsQuery->where('district', $request->get('district'));
        }

        if ($request->filled('county')) {
            $pollingStationsQuery->where('county', $request->get('county'));
        }

        if ($request->filled('subcounty')) {
            $pollingStationsQuery->where('subcounty', $request->get('subcounty'));
        }

        if ($request->filled('parish')) {
            $pollingStationsQuery->where('parish', $request->get('parish'));
        }

        if ($request->filled('village')) {
            $pollingStationsQuery->where('village', $request->get('village'));
        }

        if ($request->filled('coordinates')) {
            if ($request->get('coordinates') === 'with') {
                $pollingStationsQuery->whereNotNull('latitude')->whereNotNull('longitude');
            } elseif ($request->get('coordinates') === 'without') {
                $pollingStationsQuery->where(function($query) {
                    $query->whereNull('latitude')->orWhereNull('longitude');
                });
            }
        }

        if ($request->filled('agents')) {
            if ($request->get('agents') === 'with') {
                $pollingStationsQuery->whereHas('agents');
            } elseif ($request->get('agents') === 'without') {
                $pollingStationsQuery->whereDoesntHave('agents');
            }
        }

        if ($request->filled('evidence')) {
            if ($request->get('evidence') === 'with') {
                $pollingStationsQuery->whereHas('agents.eveidences');
            } elseif ($request->get('evidence') === 'without') {
                $pollingStationsQuery->whereHas('agents')->whereDoesntHave('agents.eveidences');
            }
        }

        // Sorting
        $sortBy = $request->get('sort_by', 'subcounty');
        $sortOrder = $request->get('sort_order', 'asc');

        $validSortColumns = ['district', 'county', 'subcounty', 'parish', 'village', 'name'];
        if (in_array($sortBy, $validSortColumns)) {
            $pollingStationsQuery->orderBy($sortBy, $sortOrder);
        }

        // Pagination
        $perPage = $request->get('per_page', 50);
        $polling_stations = $pollingStationsQuery->paginate($perPage);

        // Get unique values for filters
        $districts = PollingStation::distinct()->whereNotNull('district')->pluck('district')->sort()->values();
        $counties = PollingStation::distinct()->whereNotNull('county')->pluck('county')->sort()->values();
        $subcounties = PollingStation::distinct()->whereNotNull('subcounty')->pluck('subcounty')->sort()->values();
        $parishes = PollingStation::distinct()->whereNotNull('parish')->pluck('parish')->sort()->values();
        $villages = PollingStation::distinct()->whereNotNull('village')->pluck('village')->sort()->values();

        // Statistics
        $totalStations = PollingStation::count();
        $stationsWithCoordinates = PollingStation::whereNotNull('latitude')->whereNotNull('longitude')->count();
        $stationsWithAgents = PollingStation::whereHas('agents')->count();
        $stationsWithEvidence = PollingStation::whereHas('agents.eveidences')->count();

        // Count unique administrative areas
        $totalSubcounties = PollingStation::distinct()->whereNotNull('subcounty')->where('subcounty', '!=', '')->count('subcounty');
        $totalParishes = PollingStation::distinct()->whereNotNull('parish')->where('parish', '!=', '')->count('parish');
        $totalVillages = PollingStation::distinct()->whereNotNull('village')->where('village', '!=', '')->count('village');

        $data = [
            'polling_stations' => $polling_stations,
            'districts' => $districts,
            'counties' => $counties,
            'subcounties' => $subcounties,
            'parishes' => $parishes,
            'villages' => $villages,
            'totalStations' => $totalStations,
            'stationsWithCoordinates' => $stationsWithCoordinates,
            'stationsWithAgents' => $stationsWithAgents,
            'stationsWithEvidence' => $stationsWithEvidence,
            'totalSubcounties' => $totalSubcounties,
            'totalParishes' => $totalParishes,
            'totalVillages' => $totalVillages,
            'title' => 'Polling Villages',
            'currentFilters' => $request->only(['search', 'district', 'county', 'subcounty', 'parish', 'village', 'coordinates', 'agents', 'evidence', 'per_page', 'sort_by', 'sort_order'])
        ];

        return view('polling_stations.villages')->with($data);
    }

    /**
     * Show vote submission form for a specific polling station (accessible to users with view permissions)
     */
    public function showVoteForm(PollingStation $station)
    {
        $positions = Position::with('candidates')->get();
        $agent = $station->agent;

        // Get existing votes for this station
        $existingVotes = [];
        $existingEvidence = collect();

        if ($agent) {
            $existingVotes = Vote::where('agent_id', $agent->id)
                ->pluck('number_of_votes', 'candidate_id')
                ->toArray();

            // Get existing evidence for this agent
            $existingEvidence = $agent->eveidences;
        }

        return view('polling_stations.vote-form', compact('station', 'positions', 'agent', 'existingVotes', 'existingEvidence'));
    }

    /**
     * Submit votes and evidence for a polling station (accessible to users with view permissions)
     */
    public function submitVotes(Request $request, PollingStation $station)
    {
        $request->validate([
            'votes' => 'required|array',
            'votes.*' => 'required|integer|min:0',
            'evidence_files.*' => 'nullable|file|mimes:jpg,jpeg,png,pdf|max:5120', // 5MB max
            'file_names.*' => 'nullable|string|max:255',
        ]);

        $agent = $station->agent;
        if (!$agent) {
            return redirect()->back()->with('error', 'No agent assigned to this polling station.');
        }

        DB::beginTransaction();
        try {
            $auditService = new VoteAuditService();
            $uploadedFiles = [];

            // Process vote submissions
            foreach ($request->votes as $candidateId => $voteCount) {
                // Get existing vote to determine action type and previous count
                $existingVote = Vote::where('agent_id', $agent->id)
                    ->where('candidate_id', $candidateId)
                    ->first();

                $actionType = $existingVote ? 'update' : 'create';
                $previousVotes = $existingVote ? $existingVote->number_of_votes : 0;

                // Log the submission in audit trail BEFORE updating the vote
                $auditService->logVoteSubmission(
                    $agent->id,
                    $candidateId,
                    $voteCount,
                    $actionType,
                    'station_portal',
                    $request,
                    "Votes submitted via station portal for {$station->name} (Previous: {$previousVotes}, New: {$voteCount})"
                );

                // Create or update vote record AFTER logging
                Vote::updateOrCreate(
                    [
                        'agent_id' => $agent->id,
                        'candidate_id' => $candidateId
                    ],
                    [
                        'number_of_votes' => $voteCount,
                        'latitude' => $request->latitude,
                        'longitude' => $request->longitude,
                    ]
                );
            }

            // Process evidence files if any
            if ($request->hasFile('evidence_files')) {
                foreach ($request->file('evidence_files') as $index => $file) {
                    if ($file && $file->isValid()) {
                        $fileName = $request->file_names[$index] ?? 'Evidence_' . time() . '_' . ($index + 1);

                        // Store file
                        $filePath = $file->store('evidence', 'public');
                        $fileUrl = Storage::url($filePath);

                        // Create evidence record
                        $evidence = Eveidence::create([
                            'agent_id' => $agent->id,
                            'file_url' => $fileUrl,
                            'file_name' => $fileName,
                        ]);

                        $uploadedFiles[] = $evidence;
                    }
                }
            }

            DB::commit();

            $message = 'Votes submitted successfully';
            if (count($uploadedFiles) > 0) {
                $message .= ' with ' . count($uploadedFiles) . ' evidence file(s)';
            }

            return redirect()->back()->with('success', $message);

        } catch (\Exception $e) {
            DB::rollback();
            Log::error('Vote submission failed', [
                'station_id' => $station->id,
                'agent_id' => $agent->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return redirect()->back()
                ->with('error', 'Failed to submit votes and evidence: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Show import form for polling stations and agents
     */
    public function showImportForm()
    {
        return view('polling_stations.import');
    }

    /**
     * Process the import of polling stations and agents
     */
    public function processImport(Request $request)
    {
        // Optimize for large file imports
        $this->optimizeForLargeImport($request);

        $request->validate([
            'import_file' => 'required|file|mimes:csv,txt,xlsx,xls|max:51200', // 50MB max
        ]);

        // Get the agent role
        $agentRole = Role::where('name', 'agent')->first();
        if (!$agentRole) {
            return redirect()->back()->with('error', 'Agent role not found. Please create it first.');
        }

        $file = $request->file('import_file');
        $fileSize = $file->getSize();
        $fileName = $file->getClientOriginalName();

        // Log import start
        Log::info("Starting optimized import", [
            'file_name' => $fileName,
            'file_size' => $fileSize,
            'user_id' => Auth::id()
        ]);

        try {
            // Use optimized processing based on file size
            if ($fileSize > 5 * 1024 * 1024) { // > 5MB
                return $this->processLargeFileOptimized($file, $agentRole);
            } else {
                return $this->processStandardFileOptimized($file, $agentRole);
            }
        } catch (\Exception $e) {
            Log::error("Import failed", [
                'file_name' => $fileName,
                'error' => $e->getMessage(),
                'memory_peak' => memory_get_peak_usage(true),
                'user_id' => Auth::id()
            ]);
            return redirect()->back()->with('error', 'Import failed: ' . $e->getMessage());
        }
    }

    /**
     * Process standard size files (< 5MB) with basic optimization
     */
    private function processStandardFileOptimized($file, $agentRole)
    {
        $path = $file->getRealPath();
        $stations = [];
        $errors = [];
        $success = 0;
        $failed = 0;

        // Open the file with optimized settings
        if (($handle = fopen($path, "r")) !== FALSE) {
            // Read the CSV header
            $header = fgetcsv($handle, 0, ","); // 0 = no length limit

            // Validate header efficiently
            $validationResult = $this->validateCsvHeader($header);
            if (!$validationResult['valid']) {
                fclose($handle);
                return redirect()->back()->with('error', $validationResult['message']);
            }

            // Create column mapping
            $columnMap = array_flip($header);

            // Process with optimized batch transactions
            $result = $this->processCsvDataOptimized($handle, $columnMap, $agentRole);

            fclose($handle);

            return $this->returnImportResults(
                $result['stations'],
                $result['errors'],
                $result['success'],
                $result['failed']
            );
        }

        return redirect()->back()->with('error', 'Could not open the file.');
    }

    /**
     * Process large files (> 5MB) with advanced optimization
     */
    private function processLargeFileOptimized($file, $agentRole)
    {
        $path = $file->getRealPath();
        $stations = [];
        $errors = [];
        $success = 0;
        $failed = 0;
        $chunkSize = 50; // Smaller chunks for memory efficiency
        $processedRows = 0;

        // Open file with memory-efficient settings
        if (($handle = fopen($path, "r")) !== FALSE) {
            // Set larger buffer for better I/O performance
            stream_set_read_buffer($handle, 8192);

            // Read and validate header
            $header = fgetcsv($handle, 0, ",");
            $validationResult = $this->validateCsvHeader($header);

            if (!$validationResult['valid']) {
                fclose($handle);
                return redirect()->back()->with('error', $validationResult['message']);
            }

            $columnMap = array_flip($header);

            try {
                $rowNumber = 1;
                $chunkData = [];

                while (($data = fgetcsv($handle, 0, ",")) !== FALSE) {
                    $rowNumber++;

                    // Skip empty rows efficiently
                    if (empty(array_filter($data, 'strlen'))) {
                        continue;
                    }

                    $chunkData[] = ['data' => $data, 'rowNumber' => $rowNumber];

                    // Process chunk when it reaches the chunk size
                    if (count($chunkData) >= $chunkSize) {
                        $chunkResult = $this->processChunkOptimized($chunkData, $columnMap, $agentRole);

                        // Merge results efficiently
                        $stations = array_merge($stations, $chunkResult['stations']);
                        $errors = array_merge($errors, $chunkResult['errors']);
                        $success += $chunkResult['success'];
                        $failed += $chunkResult['failed'];

                        $processedRows += count($chunkData);
                        $chunkData = []; // Reset chunk

                        // Memory management every 200 rows
                        if ($processedRows % 200 === 0) {
                            $this->performMemoryCleanup($processedRows, $success, $failed);
                        }
                    }
                }

                // Process remaining data
                if (!empty($chunkData)) {
                    $chunkResult = $this->processChunkOptimized($chunkData, $columnMap, $agentRole);

                    $stations = array_merge($stations, $chunkResult['stations']);
                    $errors = array_merge($errors, $chunkResult['errors']);
                    $success += $chunkResult['success'];
                    $failed += $chunkResult['failed'];
                }

                fclose($handle);

                return $this->returnImportResults($stations, $errors, $success, $failed);

            } catch (\Exception $e) {
                fclose($handle);
                throw $e;
            }
        }

        return redirect()->back()->with('error', 'Could not open the file.');
    }

    /**
     * Normalize Uganda phone numbers to international format
     */
    private function normalizeUgandaPhoneNumber($phoneNumber)
    {
        // Remove all non-digit characters except +
        $cleaned = preg_replace('/[^\d+]/', '', $phoneNumber);

        // Handle different Uganda phone number formats
        if (preg_match('/^\+256([7-9]\d{8})$/', $cleaned, $matches)) {
            // Already in international format: +256XXXXXXXXX
            return $cleaned;
        } elseif (preg_match('/^256([7-9]\d{8})$/', $cleaned, $matches)) {
            // Missing + sign: 256XXXXXXXXX
            return '+' . $cleaned;
        } elseif (preg_match('/^0([7-9]\d{8})$/', $cleaned, $matches)) {
            // National format with leading zero: 0XXXXXXXXX
            return '+256' . $matches[1];
        } elseif (preg_match('/^([7-9]\d{8})$/', $cleaned, $matches)) {
            // Mobile format without country code or leading zero: 7XXXXXXXX, 8XXXXXXXX, 9XXXXXXXX
            return '+256' . $matches[1];
        } elseif (preg_match('/^([7-9]\d{9})$/', $cleaned, $matches)) {
            // 10-digit mobile format: 7XXXXXXXXX, 8XXXXXXXXX, 9XXXXXXXXX
            return '+256' . $matches[1];
        } elseif (preg_match('/^([7-9]\d{7})$/', $cleaned, $matches)) {
            // Short mobile format (8 digits): 7XXXXXXX, 8XXXXXXX, 9XXXXXXX
            return '+256' . $matches[1];
        }

        // If none of the patterns match, return false
        return false;
    }

    /**
     * Optimize PHP settings for large file imports
     */
    private function optimizeForLargeImport($request)
    {
        $file = $request->file('import_file');
        $fileSize = $file ? $file->getSize() : 0;

        // Dynamic resource allocation based on file size
        if ($fileSize > 20 * 1024 * 1024) { // > 20MB
            set_time_limit(3600); // 1 hour
            ini_set('memory_limit', '2G');
            ini_set('max_execution_time', 3600);
        } elseif ($fileSize > 10 * 1024 * 1024) { // > 10MB
            set_time_limit(1800); // 30 minutes
            ini_set('memory_limit', '1G');
            ini_set('max_execution_time', 1800);
        } elseif ($fileSize > 5 * 1024 * 1024) { // > 5MB
            set_time_limit(900); // 15 minutes
            ini_set('memory_limit', '768M');
            ini_set('max_execution_time', 900);
        } else {
            set_time_limit(300); // 5 minutes
            ini_set('memory_limit', '512M');
            ini_set('max_execution_time', 300);
        }

        // Additional optimizations for large files
        ini_set('max_input_time', 3600);
        ini_set('auto_detect_line_endings', true);
        ini_set('max_input_vars', 10000);

        // Disable output buffering for large files to save memory
        if ($fileSize > 5 * 1024 * 1024) {
            while (ob_get_level()) {
                ob_end_clean();
            }
        }

        // Force garbage collection
        gc_enable();
        gc_collect_cycles();
    }





    /**
     * Validate CSV header efficiently
     */
    private function validateCsvHeader($header)
    {
        $requiredColumns = ['name', 'district', 'county', 'subcounty', 'parish', 'village', 'agent_name', 'agent_phone'];
        $missingColumns = array_diff($requiredColumns, $header);

        if (!empty($missingColumns)) {
            $errorMessage = 'CSV Import Error: Missing required columns.<br><br>';
            $errorMessage .= '<strong>Required columns:</strong> ' . implode(', ', $requiredColumns) . '<br>';
            $errorMessage .= '<strong>Found columns:</strong> ' . implode(', ', $header) . '<br>';
            $errorMessage .= '<strong>Missing columns:</strong> ' . implode(', ', $missingColumns) . '<br><br>';
            $errorMessage .= 'Please ensure your CSV file has the exact column names listed above (case-sensitive).';

            return ['valid' => false, 'message' => $errorMessage];
        }

        return ['valid' => true, 'message' => null];
    }

    /**
     * Process a chunk of data with optimized transaction management
     */
    private function processChunkOptimized($chunkData, $columnMap, $agentRole)
    {
        $stations = [];
        $errors = [];
        $success = 0;
        $failed = 0;

        DB::beginTransaction();

        try {
            foreach ($chunkData as $item) {
                $result = $this->processImportRowOptimized($item['data'], $columnMap, $item['rowNumber'], $agentRole);

                if ($result['success']) {
                    $stations[] = $result['data'];
                    $success++;
                } else {
                    $errors[] = $result['error'];
                    $failed++;
                }
            }

            DB::commit();

        } catch (\Exception $e) {
            DB::rollback();
            throw $e;
        }

        return [
            'stations' => $stations,
            'errors' => $errors,
            'success' => $success,
            'failed' => $failed
        ];
    }

    /**
     * Process a single import row with optimization
     */
    private function processImportRowOptimized($data, $columnMap, $rowNumber, $agentRole)
    {
        try {
            // Extract data efficiently
            $stationData = [
                'name' => $data[$columnMap['name']] ?? null,
                'district' => $data[$columnMap['district']] ?? null,
                'county' => $data[$columnMap['county']] ?? null,
                'subcounty' => $data[$columnMap['subcounty']] ?? null,
                'parish' => $data[$columnMap['parish']] ?? null,
                'village' => $data[$columnMap['village']] ?? null,
                'latitude' => $data[$columnMap['latitude'] ?? -1] ?? null,
                'longitude' => $data[$columnMap['longitude'] ?? -1] ?? null,
            ];

            $agentData = [
                'name' => $data[$columnMap['agent_name']] ?? null,
                'phone_number' => $data[$columnMap['agent_phone']] ?? null,
            ];

            // Validate required fields (only station name and village are truly required)
            if (empty($stationData['name']) || empty($stationData['village'])) {
                $missingFields = [];
                if (empty($stationData['name'])) {
                    $missingFields[] = 'station name';
                }
                if (empty($stationData['village'])) {
                    $missingFields[] = 'village';
                }
                return [
                    'success' => false,
                    'error' => "Row {$rowNumber}: Missing required fields: " . implode(', ', $missingFields) . "."
                ];
            }

            // Handle missing agent name - use default name
            if (empty($agentData['name'])) {
                $agentData['name'] = 'Missing name';
            }

            // Handle phone number - normalize if provided, generate if missing
            if (!empty($agentData['phone_number'])) {
                $phoneNumber = $this->normalizeUgandaPhoneNumber($agentData['phone_number']);
                if (!$phoneNumber) {
                    return [
                        'success' => false,
                        'error' => "Row {$rowNumber}: Invalid phone number format for '{$agentData['phone_number']}'. Expected formats: +256XXXXXXXXX, 0XXXXXXXXX, 7XXXXXXXX, or 7XXXXXXXXX."
                    ];
                }
                $agentData['phone_number'] = $phoneNumber;
            } else {
                // Generate a unique phone number when missing
                $agentData['phone_number'] = '+256700' . str_pad($rowNumber, 6, '0', STR_PAD_LEFT);
            }

            // Use optimized queries with select specific columns
            $existingStation = PollingStation::select('id', 'name', 'village')
                ->where('name', $stationData['name'])
                ->where('village', $stationData['village'])
                ->first();

            if ($existingStation) {
                $station = $existingStation;
            } else {
                $station = PollingStation::create($stationData);
            }

            // Check if this village already has agents from previous imports
            $existingAgentsForVillage = Agent::whereHas('polling_station', function($query) use ($stationData) {
                $query->where('village', $stationData['village']);
            })->with('user')->get();

            // Optimized user lookup
            $existingUser = User::select('id', 'name', 'phone_number')
                ->where('phone_number', $agentData['phone_number'])
                ->first();

            if ($existingUser) {
                $user = $existingUser;
            } else {
                // Check what was auto-generated
                $originalPhoneProvided = !empty($data[$columnMap['agent_phone']] ?? null);
                $originalNameProvided = !empty($data[$columnMap['agent_name']] ?? null);

                if ($originalPhoneProvided) {
                    // Use the agent phone number as password when provided
                    $password = $agentData['phone_number'];
                } else {
                    // Use default password when phone number was missing
                    $password = 'agent123';
                }

                // Create new user with optimized hashing
                $user = User::create([
                    'name' => $agentData['name'],
                    'phone_number' => $agentData['phone_number'],
                    'password' => bcrypt($password), // Use bcrypt directly for better performance
                    'user_type' => 'agent',
                    'role_id' => $agentRole->id,
                    'is_active' => true,
                ]);

                // Store the plain password and flags for display in the results
                $user->plain_password = $password;
                $user->phone_generated = !$originalPhoneProvided;
                $user->name_generated = !$originalNameProvided;
            }

            // Handle duplicate agents for the same village
            $result = $this->handleDuplicateAgentsForVillage($station, $user, $existingAgentsForVillage, $agentData, $rowNumber);

            if (!$result['success']) {
                return $result;
            }

            $agent = $result['agent'];

            return [
                'success' => true,
                'data' => [
                    'station' => $station,
                    'user' => $user,
                    'agent' => $agent,
                    'password' => $user->plain_password ?? null,
                    'merge_action' => $result['action'] ?? 'created_new_agent',
                    'merge_details' => $result['merge_details'] ?? null,
                    'warning' => $result['warning'] ?? null
                ]
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => "Row {$rowNumber}: " . $e->getMessage()
            ];
        }
    }

    /**
     * Handle duplicate agents for the same village during import
     */
    private function handleDuplicateAgentsForVillage($station, $user, $existingAgentsForVillage, $agentData, $rowNumber)
    {
        try {
            // Check if this user already has an agent record for this station
            $existingAgentForUser = Agent::where('user_id', $user->id)
                ->where('polling_station_id', $station->id)
                ->first();

            if ($existingAgentForUser) {
                // User already has an agent for this station, return existing agent
                return [
                    'success' => true,
                    'agent' => $existingAgentForUser,
                    'action' => 'existing_agent_found'
                ];
            }

            // Check if there are existing agents for this village
            if ($existingAgentsForVillage->isNotEmpty()) {
                // Strategy 1: Check if any existing agent has similar name or phone
                $similarAgent = $this->findSimilarAgent($existingAgentsForVillage, $agentData);

                if ($similarAgent) {
                    // Merge with similar agent - update the existing agent's user info if needed
                    $mergeResult = $this->mergeAgentData($similarAgent, $user, $agentData, $rowNumber);

                    if ($mergeResult['merged']) {
                        return [
                            'success' => true,
                            'agent' => $similarAgent,
                            'action' => 'merged_with_similar',
                            'merge_details' => $mergeResult['details']
                        ];
                    }
                }

                // Strategy 2: If no similar agent found, check if we should create a new agent or use existing
                if ($existingAgentsForVillage->count() >= 3) {
                    // Too many agents for one village, use the first active agent
                    $primaryAgent = $existingAgentsForVillage->first();

                    Log::warning("Too many agents for village, using primary agent", [
                        'village' => $station->village,
                        'station_id' => $station->id,
                        'existing_agents_count' => $existingAgentsForVillage->count(),
                        'primary_agent_id' => $primaryAgent->id,
                        'new_user_id' => $user->id,
                        'row_number' => $rowNumber
                    ]);

                    return [
                        'success' => true,
                        'agent' => $primaryAgent,
                        'action' => 'used_primary_agent',
                        'warning' => "Village {$station->village} has multiple agents. Using primary agent."
                    ];
                }
            }

            // Create new agent for this user and station
            $agent = Agent::create([
                'user_id' => $user->id,
                'polling_station_id' => $station->id
            ]);

            return [
                'success' => true,
                'agent' => $agent,
                'action' => 'created_new_agent'
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => "Row {$rowNumber}: Failed to handle agent assignment - " . $e->getMessage()
            ];
        }
    }

    /**
     * Find similar agent based on name or phone similarity
     */
    private function findSimilarAgent($existingAgents, $agentData)
    {
        foreach ($existingAgents as $existingAgent) {
            $existingUser = $existingAgent->user;

            // Check for exact phone match
            if ($existingUser->phone_number === $agentData['phone_number']) {
                return $existingAgent;
            }

            // Check for similar names (fuzzy matching)
            $nameSimilarity = $this->calculateNameSimilarity($existingUser->name, $agentData['name']);
            if ($nameSimilarity > 0.8) { // 80% similarity threshold
                return $existingAgent;
            }
        }

        return null;
    }

    /**
     * Calculate name similarity using Levenshtein distance
     */
    private function calculateNameSimilarity($name1, $name2)
    {
        $name1 = strtolower(trim($name1));
        $name2 = strtolower(trim($name2));

        if ($name1 === $name2) {
            return 1.0;
        }

        $maxLength = max(strlen($name1), strlen($name2));
        if ($maxLength === 0) {
            return 1.0;
        }

        $distance = levenshtein($name1, $name2);
        return 1 - ($distance / $maxLength);
    }

    /**
     * Merge agent data when similar agent is found
     */
    private function mergeAgentData($existingAgent, $newUser, $newAgentData, $rowNumber)
    {
        $existingUser = $existingAgent->user;
        $merged = false;
        $details = [];

        try {
            // Check if we should update the existing user's information
            $shouldUpdate = false;
            $updates = [];

            // Update name if the new one is more complete (longer and not "Missing name")
            if (strlen($newAgentData['name']) > strlen($existingUser->name) &&
                $newAgentData['name'] !== 'Missing name' &&
                $existingUser->name === 'Missing name') {
                $updates['name'] = $newAgentData['name'];
                $shouldUpdate = true;
                $details[] = "Updated name from '{$existingUser->name}' to '{$newAgentData['name']}'";
            }

            // Update phone if the new one looks more legitimate (not auto-generated)
            if (!str_contains($newAgentData['phone_number'], '700') &&
                str_contains($existingUser->phone_number, '700')) {
                $updates['phone_number'] = $newAgentData['phone_number'];
                $shouldUpdate = true;
                $details[] = "Updated phone from '{$existingUser->phone_number}' to '{$newAgentData['phone_number']}'";
            }

            if ($shouldUpdate) {
                $existingUser->update($updates);
                $merged = true;

                Log::info("Merged agent data during import", [
                    'existing_agent_id' => $existingAgent->id,
                    'existing_user_id' => $existingUser->id,
                    'new_user_id' => $newUser->id,
                    'updates' => $updates,
                    'row_number' => $rowNumber
                ]);

                // Delete the new user since we're using the existing one
                $newUser->delete();
            }

            return [
                'merged' => $merged,
                'details' => $details
            ];

        } catch (\Exception $e) {
            Log::error("Failed to merge agent data", [
                'existing_agent_id' => $existingAgent->id,
                'new_user_id' => $newUser->id,
                'error' => $e->getMessage(),
                'row_number' => $rowNumber
            ]);

            return [
                'merged' => false,
                'details' => ["Failed to merge: " . $e->getMessage()]
            ];
        }
    }

    /**
     * Perform memory cleanup and logging
     */
    private function performMemoryCleanup($processedRows, $success, $failed)
    {
        // Force garbage collection
        gc_collect_cycles();

        // Log progress for large imports
        Log::info("Import progress", [
            'processed_rows' => $processedRows,
            'success' => $success,
            'failed' => $failed,
            'memory_usage' => memory_get_usage(true),
            'memory_peak' => memory_get_peak_usage(true)
        ]);
    }

    /**
     * Return import results with optimized view
     */
    private function returnImportResults($stations, $errors, $success, $failed)
    {
        $message = "Import completed. Success: {$success}, Failed: {$failed}";

        if ($failed > 0) {
            $message .= ". Please check the results for details.";
        }

        // Log final results
        Log::info("Import completed", [
            'success' => $success,
            'failed' => $failed,
            'memory_peak' => memory_get_peak_usage(true),
            'user_id' => Auth::id()
        ]);

        return view('polling_stations.import_results', [
            'stations' => $stations,
            'errors' => $errors,
            'success' => $success,
            'failed' => $failed,
            'message' => $message
        ]);
    }

    /**
     * Process CSV data with optimized batch transactions
     */
    private function processCsvDataOptimized($handle, $columnMap, $agentRole)
    {
        $stations = [];
        $errors = [];
        $success = 0;
        $failed = 0;
        $batchSize = 50; // Larger batches for smaller files
        $rowNumber = 1;
        $processedCount = 0;

        DB::beginTransaction();

        try {
            while (($data = fgetcsv($handle, 0, ",")) !== FALSE) {
                $rowNumber++;

                // Skip empty rows efficiently
                if (empty(array_filter($data, 'strlen'))) {
                    continue;
                }

                // Commit and restart transaction every batch
                if ($processedCount > 0 && $processedCount % $batchSize === 0) {
                    DB::commit();
                    DB::beginTransaction();

                    // Memory cleanup every 200 rows
                    if ($processedCount % 200 === 0) {
                        gc_collect_cycles();
                    }
                }

                $result = $this->processImportRowOptimized($data, $columnMap, $rowNumber, $agentRole);

                if ($result['success']) {
                    $stations[] = $result['data'];
                    $success++;
                } else {
                    $errors[] = $result['error'];
                    $failed++;
                }

                $processedCount++;
            }

            DB::commit();

        } catch (\Exception $e) {
            DB::rollback();
            throw $e;
        }

        return [
            'stations' => $stations,
            'errors' => $errors,
            'success' => $success,
            'failed' => $failed
        ];
    }
}
