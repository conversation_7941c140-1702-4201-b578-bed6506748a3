<?php

namespace App\Http\Controllers;

use App\Models\PollingStation;
use App\Models\Vote;
use App\Models\VoteAuditLog;
use App\Models\Agent;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class ReportController extends Controller
{
    /**
     * Generate comprehensive polling station submission report
     */
    public function pollingStationSubmissionReport(Request $request)
    {
        // Get filter parameters
        $districtFilter = $request->get('district');
        $countyFilter = $request->get('county');
        $subcountyFilter = $request->get('subcounty');
        $parishFilter = $request->get('parish');
        $dateFrom = $request->get('date_from');
        $dateTo = $request->get('date_to');
        $perPage = $request->get('per_page', 25); // Default 25 items per page

        // Build the hierarchical tree report data
        $reportData = $this->buildHierarchicalTreeData($districtFilter, $countyFilter, $subcountyFilter, $parishFilter, $dateFrom, $dateTo, $perPage);

        // Get summary statistics
        $summaryStats = $this->getSummaryStatistics($districtFilter, $countyFilter, $subcountyFilter, $parishFilter, $dateFrom, $dateTo);

        // Get filter options for dropdowns
        $filterOptions = $this->getFilterOptions();

        return view('reports.polling-station-submission', compact(
            'reportData',
            'summaryStats',
            'filterOptions',
            'districtFilter',
            'countyFilter',
            'subcountyFilter',
            'parishFilter',
            'dateFrom',
            'dateTo',
            'perPage'
        ));
    }

    /**
     * Build hierarchical tree data for tree-table display
     */
    private function buildHierarchicalTreeData($districtFilter = null, $countyFilter = null, $subcountyFilter = null, $parishFilter = null, $dateFrom = null, $dateTo = null, $perPage = 25)
    {
        // Base query for polling stations
        $stationsQuery = PollingStation::query();

        // Apply filters
        if ($districtFilter) {
            $stationsQuery->where('district', $districtFilter);
        }
        if ($countyFilter) {
            $stationsQuery->where('county', $countyFilter);
        }
        if ($subcountyFilter) {
            $stationsQuery->where('subcounty', $subcountyFilter);
        }
        if ($parishFilter) {
            $stationsQuery->where('parish', $parishFilter);
        }

        // Get all polling stations with their submission status
        $stations = $stationsQuery->with(['agents.user', 'agents.votes'])->get();

        // Get vote audit logs for user attribution and timestamps
        $auditLogsQuery = VoteAuditLog::with(['submittedBy'])
            ->select('polling_station_id', 'submitted_by_user_id', 'submitted_by_user_type', 'submission_time')
            ->distinct();

        if ($dateFrom) {
            $auditLogsQuery->where('submission_time', '>=', Carbon::parse($dateFrom)->startOfDay());
        }
        if ($dateTo) {
            $auditLogsQuery->where('submission_time', '<=', Carbon::parse($dateTo)->endOfDay());
        }

        $auditLogs = $auditLogsQuery->get()->groupBy('polling_station_id');

        // Build hierarchical structure
        $hierarchicalData = [];
        $rowId = 1;

        foreach ($stations as $station) {
            $hasVotes = $this->stationHasVotes($station, $dateFrom, $dateTo);
            $submissionUsers = $this->getSubmissionUsers($station, $auditLogs);
            $lastSubmission = $this->getLastSubmissionTime($station, $auditLogs);

            $subcounty = $station->subcounty ?: 'Unknown Subcounty';
            $parish = $station->parish ?: 'Unknown Parish';
            $village = $station->village ?: 'Unknown Village';

            // Create unique IDs for hierarchy levels
            $subcountyId = 'subcounty_' . md5($subcounty);
            $parishId = 'parish_' . md5($subcounty . '|' . $parish);
            $villageId = 'village_' . md5($subcounty . '|' . $parish . '|' . $village);
            $stationId = 'station_' . $station->id;

            // Initialize subcounty if not exists
            if (!isset($hierarchicalData[$subcountyId])) {
                $hierarchicalData[$subcountyId] = [
                    'id' => $subcountyId,
                    'row_id' => $rowId++,
                    'name' => $subcounty,
                    'level' => 1,
                    'type' => 'subcounty',
                    'parent_id' => null,
                    'total_stations' => 0,
                    'submitted_stations' => 0,
                    'percentage' => 0,
                    'users' => [],
                    'children' => [],
                    'is_expanded' => false,
                    'has_children' => false
                ];
            }

            // Initialize parish if not exists
            if (!isset($hierarchicalData[$subcountyId]['children'][$parishId])) {
                $hierarchicalData[$subcountyId]['children'][$parishId] = [
                    'id' => $parishId,
                    'row_id' => $rowId++,
                    'name' => $parish,
                    'level' => 2,
                    'type' => 'parish',
                    'parent_id' => $subcountyId,
                    'total_stations' => 0,
                    'submitted_stations' => 0,
                    'percentage' => 0,
                    'users' => [],
                    'children' => [],
                    'is_expanded' => false,
                    'has_children' => false
                ];
                $hierarchicalData[$subcountyId]['has_children'] = true;
            }

            // Initialize village if not exists
            if (!isset($hierarchicalData[$subcountyId]['children'][$parishId]['children'][$villageId])) {
                $hierarchicalData[$subcountyId]['children'][$parishId]['children'][$villageId] = [
                    'id' => $villageId,
                    'row_id' => $rowId++,
                    'name' => $village,
                    'level' => 3,
                    'type' => 'village',
                    'parent_id' => $parishId,
                    'total_stations' => 0,
                    'submitted_stations' => 0,
                    'percentage' => 0,
                    'users' => [],
                    'children' => [],
                    'is_expanded' => false,
                    'has_children' => false
                ];
                $hierarchicalData[$subcountyId]['children'][$parishId]['has_children'] = true;
            }

            // Add station
            $stationData = [
                'id' => $stationId,
                'row_id' => $rowId++,
                'name' => $station->name,
                'level' => 4,
                'type' => 'station',
                'parent_id' => $villageId,
                'has_votes' => $hasVotes,
                'submission_status' => $hasVotes ? 'Submitted' : 'Pending',
                'submission_users' => $submissionUsers,
                'last_submission' => $lastSubmission,
                'station_data' => $station,
                'is_expanded' => false,
                'has_children' => false
            ];

            $hierarchicalData[$subcountyId]['children'][$parishId]['children'][$villageId]['children'][$stationId] = $stationData;
            $hierarchicalData[$subcountyId]['children'][$parishId]['children'][$villageId]['has_children'] = true;

            // Update counts
            $hierarchicalData[$subcountyId]['total_stations']++;
            $hierarchicalData[$subcountyId]['children'][$parishId]['total_stations']++;
            $hierarchicalData[$subcountyId]['children'][$parishId]['children'][$villageId]['total_stations']++;

            if ($hasVotes) {
                $hierarchicalData[$subcountyId]['submitted_stations']++;
                $hierarchicalData[$subcountyId]['children'][$parishId]['submitted_stations']++;
                $hierarchicalData[$subcountyId]['children'][$parishId]['children'][$villageId]['submitted_stations']++;

                // Aggregate users
                foreach ($submissionUsers as $user) {
                    $this->aggregateUser($hierarchicalData[$subcountyId]['users'], $user);
                    $this->aggregateUser($hierarchicalData[$subcountyId]['children'][$parishId]['users'], $user);
                    $this->aggregateUser($hierarchicalData[$subcountyId]['children'][$parishId]['children'][$villageId]['users'], $user);
                }
            }
        }

        // Calculate percentages
        $this->calculateHierarchicalPercentages($hierarchicalData);

        // Sort subcounties by percentage (desc), then by submitted count (desc)
        uasort($hierarchicalData, function($a, $b) {
            if ($a['percentage'] == $b['percentage']) {
                return $b['submitted_stations'] <=> $a['submitted_stations'];
            }
            return $b['percentage'] <=> $a['percentage'];
        });

        // Convert to array and paginate subcounties only
        $subcounties = array_values($hierarchicalData);
        $total = count($subcounties);
        $currentPage = request()->get('page', 1);
        $offset = ($currentPage - 1) * $perPage;
        $paginatedSubcounties = array_slice($subcounties, $offset, $perPage);

        $paginator = new \Illuminate\Pagination\LengthAwarePaginator(
            $paginatedSubcounties,
            $total,
            $perPage,
            $currentPage,
            ['path' => request()->url(), 'query' => request()->query()]
        );

        return [
            'data' => $paginatedSubcounties,
            'pagination' => $paginator,
            'type' => 'hierarchical'
        ];
    }

    /**
     * Calculate percentages for hierarchical data
     */
    private function calculateHierarchicalPercentages(&$hierarchicalData)
    {
        foreach ($hierarchicalData as &$subcounty) {
            // Calculate subcounty percentage
            $subcounty['percentage'] = $subcounty['total_stations'] > 0
                ? round(($subcounty['submitted_stations'] / $subcounty['total_stations']) * 100, 1)
                : 0;

            // Sort subcounty users
            uasort($subcounty['users'], function($a, $b) {
                return $b['submission_count'] <=> $a['submission_count'];
            });
            $subcounty['users'] = array_values($subcounty['users']);

            foreach ($subcounty['children'] as &$parish) {
                // Calculate parish percentage
                $parish['percentage'] = $parish['total_stations'] > 0
                    ? round(($parish['submitted_stations'] / $parish['total_stations']) * 100, 1)
                    : 0;

                // Sort parish users
                uasort($parish['users'], function($a, $b) {
                    return $b['submission_count'] <=> $a['submission_count'];
                });
                $parish['users'] = array_values($parish['users']);

                // Sort parishes by percentage
                uasort($parish['children'], function($a, $b) {
                    if ($a['percentage'] == $b['percentage']) {
                        return $b['submitted_stations'] <=> $a['submitted_stations'];
                    }
                    return $b['percentage'] <=> $a['percentage'];
                });

                foreach ($parish['children'] as &$village) {
                    // Calculate village percentage
                    $village['percentage'] = $village['total_stations'] > 0
                        ? round(($village['submitted_stations'] / $village['total_stations']) * 100, 1)
                        : 0;

                    // Sort village users
                    uasort($village['users'], function($a, $b) {
                        return $b['submission_count'] <=> $a['submission_count'];
                    });
                    $village['users'] = array_values($village['users']);

                    // Sort villages by percentage
                    uasort($village['children'], function($a, $b) {
                        if (isset($a['last_submission']) && isset($b['last_submission'])) {
                            return $b['last_submission'] <=> $a['last_submission'];
                        }
                        return $b['has_votes'] <=> $a['has_votes'];
                    });
                }
            }
        }
    }

    /**
     * Build tabular report data for pagination (kept for backward compatibility)
     */
    private function buildTabularReportData($districtFilter = null, $countyFilter = null, $subcountyFilter = null, $parishFilter = null, $dateFrom = null, $dateTo = null, $level = 'village', $perPage = 25)
    {
        // Base query for polling stations
        $stationsQuery = PollingStation::query();

        // Apply filters
        if ($districtFilter) {
            $stationsQuery->where('district', $districtFilter);
        }
        if ($countyFilter) {
            $stationsQuery->where('county', $countyFilter);
        }
        if ($subcountyFilter) {
            $stationsQuery->where('subcounty', $subcountyFilter);
        }
        if ($parishFilter) {
            $stationsQuery->where('parish', $parishFilter);
        }

        // Get vote audit logs for user attribution and timestamps
        $auditLogsQuery = VoteAuditLog::with(['submittedBy'])
            ->select('polling_station_id', 'submitted_by_user_id', 'submitted_by_user_type', 'submission_time')
            ->distinct();

        if ($dateFrom) {
            $auditLogsQuery->where('submission_time', '>=', Carbon::parse($dateFrom)->startOfDay());
        }
        if ($dateTo) {
            $auditLogsQuery->where('submission_time', '<=', Carbon::parse($dateTo)->endOfDay());
        }

        $auditLogs = $auditLogsQuery->get()->groupBy('polling_station_id');

        // Process data based on selected level
        if ($level === 'station') {
            // Station level - paginate individual stations
            $stations = $stationsQuery->with(['agents.user', 'agents.votes'])->paginate($perPage);

            $tableData = [];
            foreach ($stations as $station) {
                $hasVotes = $this->stationHasVotes($station, $dateFrom, $dateTo);
                $submissionUsers = $this->getSubmissionUsers($station, $auditLogs);
                $lastSubmission = $this->getLastSubmissionTime($station, $auditLogs);

                $tableData[] = [
                    'id' => $station->id,
                    'name' => $station->name,
                    'district' => $station->district,
                    'county' => $station->county,
                    'subcounty' => $station->subcounty,
                    'parish' => $station->parish,
                    'village' => $station->village,
                    'has_votes' => $hasVotes,
                    'submission_status' => $hasVotes ? 'Submitted' : 'Pending',
                    'submission_users' => $submissionUsers,
                    'last_submission' => $lastSubmission,
                    'level' => 'station',
                    'detail_type' => 'station'
                ];
            }

            return [
                'data' => $tableData,
                'pagination' => $stations,
                'level' => 'station'
            ];
        } else {
            // Group by selected level (subcounty, parish, or village)
            $groupedData = [];
            $stations = $stationsQuery->with(['agents.user', 'agents.votes'])->get();

            foreach ($stations as $station) {
                $hasVotes = $this->stationHasVotes($station, $dateFrom, $dateTo);
                $submissionUsers = $this->getSubmissionUsers($station, $auditLogs);

                $subcounty = $station->subcounty ?: 'Unknown Subcounty';
                $parish = $station->parish ?: 'Unknown Parish';
                $village = $station->village ?: 'Unknown Village';

                // Group by selected level
                $groupKey = '';
                $groupName = '';
                $parentName = '';
                $grandparentName = '';

                if ($level === 'subcounty') {
                    $groupKey = $subcounty;
                    $groupName = $subcounty;
                } elseif ($level === 'parish') {
                    $groupKey = $subcounty . '|' . $parish;
                    $groupName = $parish;
                    $parentName = $subcounty;
                } elseif ($level === 'village') {
                    $groupKey = $subcounty . '|' . $parish . '|' . $village;
                    $groupName = $village;
                    $parentName = $parish;
                    $grandparentName = $subcounty;
                }

                if (!isset($groupedData[$groupKey])) {
                    $groupedData[$groupKey] = [
                        'name' => $groupName,
                        'parent_name' => $parentName,
                        'grandparent_name' => $grandparentName,
                        'total_stations' => 0,
                        'submitted_stations' => 0,
                        'percentage' => 0,
                        'users' => [],
                        'stations' => [],
                        'level' => $level,
                        'detail_type' => 'location'
                    ];
                }

                // Add station to group
                $groupedData[$groupKey]['stations'][] = [
                    'id' => $station->id,
                    'name' => $station->name,
                    'has_votes' => $hasVotes,
                    'submission_users' => $submissionUsers,
                    'last_submission' => $this->getLastSubmissionTime($station, $auditLogs)
                ];

                $groupedData[$groupKey]['total_stations']++;

                if ($hasVotes) {
                    $groupedData[$groupKey]['submitted_stations']++;

                    // Aggregate users
                    foreach ($submissionUsers as $user) {
                        $this->aggregateUser($groupedData[$groupKey]['users'], $user);
                    }
                }
            }

            // Calculate percentages
            foreach ($groupedData as &$group) {
                $group['percentage'] = $group['total_stations'] > 0
                    ? round(($group['submitted_stations'] / $group['total_stations']) * 100, 1)
                    : 0;

                // Sort users by submission count
                uasort($group['users'], function($a, $b) {
                    return $b['submission_count'] <=> $a['submission_count'];
                });

                $group['users'] = array_values($group['users']);
            }

            // Sort by percentage (desc), then by submitted count (desc)
            uasort($groupedData, function($a, $b) {
                if ($a['percentage'] == $b['percentage']) {
                    return $b['submitted_stations'] <=> $a['submitted_stations'];
                }
                return $b['percentage'] <=> $a['percentage'];
            });

            // Convert to array and paginate
            $groupedArray = array_values($groupedData);
            $total = count($groupedArray);
            $currentPage = request()->get('page', 1);
            $offset = ($currentPage - 1) * $perPage;
            $paginatedData = array_slice($groupedArray, $offset, $perPage);

            $paginator = new \Illuminate\Pagination\LengthAwarePaginator(
                $paginatedData,
                $total,
                $perPage,
                $currentPage,
                ['path' => request()->url(), 'query' => request()->query()]
            );

            return [
                'data' => $paginatedData,
                'pagination' => $paginator,
                'level' => $level
            ];
        }
    }

    /**
     * Get summary statistics for the report
     */
    private function getSummaryStatistics($districtFilter = null, $countyFilter = null, $subcountyFilter = null, $parishFilter = null, $dateFrom = null, $dateTo = null)
    {
        // Base query for polling stations
        $stationsQuery = PollingStation::query();

        // Apply filters
        if ($districtFilter) {
            $stationsQuery->where('district', $districtFilter);
        }
        if ($countyFilter) {
            $stationsQuery->where('county', $countyFilter);
        }
        if ($subcountyFilter) {
            $stationsQuery->where('subcounty', $subcountyFilter);
        }
        if ($parishFilter) {
            $stationsQuery->where('parish', $parishFilter);
        }

        // Get total stations count
        $totalStations = $stationsQuery->count();

        // Get stations with votes
        $stationsWithVotes = 0;
        $topContributors = [];
        $recentSubmissions = [];

        if ($totalStations > 0) {
            $stations = $stationsQuery->with(['agents.votes'])->get();

            // Get vote audit logs
            $auditLogsQuery = VoteAuditLog::with(['submittedBy'])
                ->select('polling_station_id', 'submitted_by_user_id', 'submitted_by_user_type', 'submission_time')
                ->orderBy('submission_time', 'desc');

            if ($dateFrom) {
                $auditLogsQuery->where('submission_time', '>=', Carbon::parse($dateFrom)->startOfDay());
            }
            if ($dateTo) {
                $auditLogsQuery->where('submission_time', '<=', Carbon::parse($dateTo)->endOfDay());
            }

            $auditLogs = $auditLogsQuery->get();
            $auditLogsByStation = $auditLogs->groupBy('polling_station_id');
            $auditLogsByUser = $auditLogs->groupBy('submitted_by_user_id');

            // Count stations with votes
            foreach ($stations as $station) {
                if ($this->stationHasVotes($station, $dateFrom, $dateTo)) {
                    $stationsWithVotes++;
                }
            }

            // Get top 5 contributors
            $userCounts = [];
            foreach ($auditLogsByUser as $userId => $logs) {
                $user = $logs->first()->submittedBy;
                if ($user) {
                    $userCounts[$userId] = [
                        'id' => $userId,
                        'name' => $user->name,
                        'type' => $logs->first()->submitted_by_user_type,
                        'submission_count' => $logs->count(),
                        'last_submission' => $logs->max('submission_time')
                    ];
                }
            }

            // Sort by submission count
            uasort($userCounts, function($a, $b) {
                return $b['submission_count'] <=> $a['submission_count'];
            });

            $topContributors = array_slice(array_values($userCounts), 0, 5);

            // Get 5 most recent submissions
            $recentSubmissions = $auditLogs->take(5)->map(function($log) {
                $station = PollingStation::find($log->polling_station_id);
                return [
                    'station_id' => $log->polling_station_id,
                    'station_name' => $station ? $station->name : 'Unknown Station',
                    'user_name' => $log->submittedBy ? $log->submittedBy->name : 'Unknown User',
                    'user_type' => $log->submitted_by_user_type,
                    'submission_time' => $log->submission_time
                ];
            });
        }

        // Calculate completion percentage
        $completionPercentage = $totalStations > 0 ? round(($stationsWithVotes / $totalStations) * 100, 1) : 0;

        return [
            'total_stations' => $totalStations,
            'submitted_stations' => $stationsWithVotes,
            'pending_stations' => $totalStations - $stationsWithVotes,
            'completion_percentage' => $completionPercentage,
            'top_contributors' => $topContributors,
            'recent_submissions' => $recentSubmissions
        ];
    }

    /**
     * Check if a station has submitted votes within the date range
     */
    private function stationHasVotes($station, $dateFrom = null, $dateTo = null)
    {
        $agentIds = $station->agents->pluck('id');

        if ($agentIds->isEmpty()) {
            return false;
        }

        $votesQuery = Vote::whereIn('agent_id', $agentIds);

        if ($dateFrom) {
            $votesQuery->where('created_at', '>=', Carbon::parse($dateFrom)->startOfDay());
        }
        if ($dateTo) {
            $votesQuery->where('created_at', '<=', Carbon::parse($dateTo)->endOfDay());
        }

        return $votesQuery->exists();
    }

    /**
     * Export report data as CSV
     */
    public function exportSubmissionReport(Request $request)
    {
        // Get hierarchical tree data and transform it for export
        $treeData = $this->buildHierarchicalTreeData(
            $request->get('district'),
            $request->get('county'),
            $request->get('subcounty'),
            $request->get('parish'),
            $request->get('date_from'),
            $request->get('date_to'),
            1000 // Large number to get all data for export
        );

        // Transform tree data to export format
        $reportData = $this->transformTreeDataForExport($treeData['data']);

        $filename = 'polling_station_submission_report_' . date('Y-m-d_H-i-s') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function() use ($reportData) {
            $file = fopen('php://output', 'w');

            // CSV Headers
            fputcsv($file, [
                'Level',
                'Location Name',
                'Total Stations',
                'Submitted Stations',
                'Pending Stations',
                'Completion Percentage',
                'Contributors',
                'Last Submission'
            ]);

            foreach ($reportData as $subcounty) {
                // Subcounty row
                fputcsv($file, [
                    'Subcounty',
                    $subcounty['name'],
                    $subcounty['total_stations'],
                    $subcounty['submitted_stations'],
                    $subcounty['total_stations'] - $subcounty['submitted_stations'],
                    $subcounty['percentage'] . '%',
                    count($subcounty['users']) . ' users',
                    ''
                ]);

                foreach ($subcounty['parishes'] as $parish) {
                    // Parish row
                    fputcsv($file, [
                        'Parish',
                        '  ' . $parish['name'],
                        $parish['total_stations'],
                        $parish['submitted_stations'],
                        $parish['total_stations'] - $parish['submitted_stations'],
                        $parish['percentage'] . '%',
                        count($parish['users']) . ' users',
                        ''
                    ]);

                    foreach ($parish['villages'] as $village) {
                        // Village row
                        $lastSubmission = '';
                        if (!empty($village['users'])) {
                            $lastSubmission = max(array_column($village['users'], 'last_submission'));
                            $lastSubmission = Carbon::parse($lastSubmission)->format('Y-m-d H:i:s');
                        }

                        fputcsv($file, [
                            'Village',
                            '    ' . $village['name'],
                            $village['total_stations'],
                            $village['submitted_stations'],
                            $village['total_stations'] - $village['submitted_stations'],
                            $village['percentage'] . '%',
                            implode(', ', array_column($village['users'], 'name')),
                            $lastSubmission
                        ]);
                    }
                }
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * Transform tree data to export format
     */
    private function transformTreeDataForExport($treeData)
    {
        $exportData = [];

        foreach ($treeData as $subcounty) {
            $subcountyData = [
                'name' => $subcounty['name'],
                'total_stations' => $subcounty['total_stations'],
                'submitted_stations' => $subcounty['submitted_stations'],
                'percentage' => $subcounty['percentage'],
                'users' => $subcounty['users'],
                'parishes' => []
            ];

            foreach ($subcounty['children'] as $parish) {
                $parishData = [
                    'name' => $parish['name'],
                    'total_stations' => $parish['total_stations'],
                    'submitted_stations' => $parish['submitted_stations'],
                    'percentage' => $parish['percentage'],
                    'users' => $parish['users'],
                    'villages' => []
                ];

                foreach ($parish['children'] as $village) {
                    $villageData = [
                        'name' => $village['name'],
                        'total_stations' => $village['total_stations'],
                        'submitted_stations' => $village['submitted_stations'],
                        'percentage' => $village['percentage'],
                        'users' => $village['users']
                    ];

                    $parishData['villages'][] = $villageData;
                }

                $subcountyData['parishes'][] = $parishData;
            }

            $exportData[] = $subcountyData;
        }

        return $exportData;
    }

    /**
     * Get detailed information for a location (for modal display)
     */
    public function getLocationDetails(Request $request)
    {
        $level = $request->get('level');
        $name = $request->get('name');
        $parentName = $request->get('parent_name');
        $grandparentName = $request->get('grandparent_name');
        $dateFrom = $request->get('date_from');
        $dateTo = $request->get('date_to');

        // Build query based on level
        $stationsQuery = PollingStation::query();

        if ($level === 'subcounty') {
            $stationsQuery->where('subcounty', $name);
        } elseif ($level === 'parish') {
            $stationsQuery->where('parish', $name)
                         ->where('subcounty', $grandparentName);
        } elseif ($level === 'village') {
            $stationsQuery->where('village', $name)
                         ->where('parish', $parentName)
                         ->where('subcounty', $grandparentName);
        }

        $stations = $stationsQuery->with(['agents.user', 'agents.votes'])->get();

        // Get audit logs for this location
        $stationIds = $stations->pluck('id');
        $auditLogsQuery = VoteAuditLog::with(['submittedBy'])
            ->whereIn('polling_station_id', $stationIds)
            ->select('polling_station_id', 'submitted_by_user_id', 'submitted_by_user_type', 'submission_time')
            ->orderBy('submission_time', 'desc');

        if ($dateFrom) {
            $auditLogsQuery->where('submission_time', '>=', Carbon::parse($dateFrom)->startOfDay());
        }
        if ($dateTo) {
            $auditLogsQuery->where('submission_time', '<=', Carbon::parse($dateTo)->endOfDay());
        }

        $auditLogs = $auditLogsQuery->get();
        $auditLogsByStation = $auditLogs->groupBy('polling_station_id');

        // Process station details
        $stationDetails = [];
        $totalStations = 0;
        $submittedStations = 0;
        $allUsers = [];

        foreach ($stations as $station) {
            $hasVotes = $this->stationHasVotes($station, $dateFrom, $dateTo);
            $submissionUsers = $this->getSubmissionUsers($station, $auditLogsByStation);
            $lastSubmission = $this->getLastSubmissionTime($station, $auditLogsByStation);

            $stationDetails[] = [
                'id' => $station->id,
                'name' => $station->name,
                'district' => $station->district,
                'county' => $station->county,
                'subcounty' => $station->subcounty,
                'parish' => $station->parish,
                'village' => $station->village,
                'has_votes' => $hasVotes,
                'submission_users' => $submissionUsers,
                'last_submission' => $lastSubmission,
                'vote_count' => $hasVotes ? $station->agents->sum(function($agent) {
                    return $agent->votes->sum('number_of_votes');
                }) : 0
            ];

            $totalStations++;
            if ($hasVotes) {
                $submittedStations++;

                // Aggregate users
                foreach ($submissionUsers as $user) {
                    $this->aggregateUser($allUsers, $user);
                }
            }
        }

        // Sort users by submission count
        uasort($allUsers, function($a, $b) {
            return $b['submission_count'] <=> $a['submission_count'];
        });

        $percentage = $totalStations > 0 ? round(($submittedStations / $totalStations) * 100, 1) : 0;

        return response()->json([
            'location' => [
                'name' => $name,
                'parent_name' => $parentName,
                'grandparent_name' => $grandparentName,
                'level' => $level,
                'total_stations' => $totalStations,
                'submitted_stations' => $submittedStations,
                'pending_stations' => $totalStations - $submittedStations,
                'percentage' => $percentage
            ],
            'stations' => $stationDetails,
            'users' => array_values($allUsers),
            'recent_submissions' => $auditLogs->take(10)->map(function($log) use ($stations) {
                $station = $stations->firstWhere('id', $log->polling_station_id);
                return [
                    'station_name' => $station ? $station->name : 'Unknown',
                    'user_name' => $log->submittedBy ? $log->submittedBy->name : 'Unknown',
                    'user_type' => $log->submitted_by_user_type,
                    'submission_time' => $log->submission_time
                ];
            })
        ]);
    }

    /**
     * Get child data for tree expansion (lazy loading)
     */
    public function getTreeChildData(Request $request)
    {
        $parentId = $request->get('parent_id');
        $parentType = $request->get('parent_type');
        $parentName = $request->get('parent_name');
        $grandparentName = $request->get('grandparent_name');
        $greatGrandparentName = $request->get('great_grandparent_name');
        $dateFrom = $request->get('date_from');
        $dateTo = $request->get('date_to');

        // Apply same filters as main report
        $districtFilter = $request->get('district');
        $countyFilter = $request->get('county');
        $subcountyFilter = $request->get('subcounty');
        $parishFilter = $request->get('parish');

        $stationsQuery = PollingStation::query();

        // Apply filters
        if ($districtFilter) {
            $stationsQuery->where('district', $districtFilter);
        }
        if ($countyFilter) {
            $stationsQuery->where('county', $countyFilter);
        }
        if ($subcountyFilter) {
            $stationsQuery->where('subcounty', $subcountyFilter);
        }
        if ($parishFilter) {
            $stationsQuery->where('parish', $parishFilter);
        }

        // Filter based on parent type and name
        if ($parentType === 'subcounty') {
            $stationsQuery->where('subcounty', $parentName);
        } elseif ($parentType === 'parish') {
            $stationsQuery->where('parish', $parentName)
                         ->where('subcounty', $grandparentName);
        } elseif ($parentType === 'village') {
            $stationsQuery->where('village', $parentName)
                         ->where('parish', $grandparentName)
                         ->where('subcounty', $greatGrandparentName);
        }

        $stations = $stationsQuery->with(['agents.user', 'agents.votes'])->get();

        // Get audit logs
        $stationIds = $stations->pluck('id');
        $auditLogsQuery = VoteAuditLog::with(['submittedBy'])
            ->whereIn('polling_station_id', $stationIds)
            ->select('polling_station_id', 'submitted_by_user_id', 'submitted_by_user_type', 'submission_time');

        if ($dateFrom) {
            $auditLogsQuery->where('submission_time', '>=', Carbon::parse($dateFrom)->startOfDay());
        }
        if ($dateTo) {
            $auditLogsQuery->where('submission_time', '<=', Carbon::parse($dateTo)->endOfDay());
        }

        $auditLogs = $auditLogsQuery->get()->groupBy('polling_station_id');

        $childData = [];
        $rowId = 1000; // Start with high number to avoid conflicts

        if ($parentType === 'subcounty') {
            // Return parishes
            $parishes = $stations->groupBy('parish');
            foreach ($parishes as $parishName => $parishStations) {
                $parishId = 'parish_' . md5($parentName . '|' . $parishName);
                $totalStations = $parishStations->count();
                $submittedStations = 0;
                $users = [];

                foreach ($parishStations as $station) {
                    if ($this->stationHasVotes($station, $dateFrom, $dateTo)) {
                        $submittedStations++;
                        $submissionUsers = $this->getSubmissionUsers($station, $auditLogs);
                        foreach ($submissionUsers as $user) {
                            $this->aggregateUser($users, $user);
                        }
                    }
                }

                $percentage = $totalStations > 0 ? round(($submittedStations / $totalStations) * 100, 1) : 0;

                $childData[] = [
                    'id' => $parishId,
                    'row_id' => $rowId++,
                    'name' => $parishName ?: 'Unknown Parish',
                    'level' => 2,
                    'type' => 'parish',
                    'parent_id' => $parentId,
                    'total_stations' => $totalStations,
                    'submitted_stations' => $submittedStations,
                    'percentage' => $percentage,
                    'users' => array_values($users),
                    'has_children' => true,
                    'is_expanded' => false
                ];
            }
        } elseif ($parentType === 'parish') {
            // Return villages
            $villages = $stations->groupBy('village');
            foreach ($villages as $villageName => $villageStations) {
                $villageId = 'village_' . md5($grandparentName . '|' . $parentName . '|' . $villageName);
                $totalStations = $villageStations->count();
                $submittedStations = 0;
                $users = [];

                foreach ($villageStations as $station) {
                    if ($this->stationHasVotes($station, $dateFrom, $dateTo)) {
                        $submittedStations++;
                        $submissionUsers = $this->getSubmissionUsers($station, $auditLogs);
                        foreach ($submissionUsers as $user) {
                            $this->aggregateUser($users, $user);
                        }
                    }
                }

                $percentage = $totalStations > 0 ? round(($submittedStations / $totalStations) * 100, 1) : 0;

                $childData[] = [
                    'id' => $villageId,
                    'row_id' => $rowId++,
                    'name' => $villageName ?: 'Unknown Village',
                    'level' => 3,
                    'type' => 'village',
                    'parent_id' => $parentId,
                    'total_stations' => $totalStations,
                    'submitted_stations' => $submittedStations,
                    'percentage' => $percentage,
                    'users' => array_values($users),
                    'has_children' => true,
                    'is_expanded' => false
                ];
            }
        } elseif ($parentType === 'village') {
            // Return stations
            foreach ($stations as $station) {
                $stationId = 'station_' . $station->id;
                $hasVotes = $this->stationHasVotes($station, $dateFrom, $dateTo);
                $submissionUsers = $this->getSubmissionUsers($station, $auditLogs);
                $lastSubmission = $this->getLastSubmissionTime($station, $auditLogs);

                $childData[] = [
                    'id' => $stationId,
                    'row_id' => $rowId++,
                    'name' => $station->name,
                    'level' => 4,
                    'type' => 'station',
                    'parent_id' => $parentId,
                    'has_votes' => $hasVotes,
                    'submission_status' => $hasVotes ? 'Submitted' : 'Pending',
                    'submission_users' => $submissionUsers,
                    'last_submission' => $lastSubmission,
                    'station_data' => $station,
                    'has_children' => false,
                    'is_expanded' => false
                ];
            }
        }

        // Sort child data
        if ($parentType !== 'village') {
            usort($childData, function($a, $b) {
                if ($a['percentage'] == $b['percentage']) {
                    return $b['submitted_stations'] <=> $a['submitted_stations'];
                }
                return $b['percentage'] <=> $a['percentage'];
            });
        } else {
            usort($childData, function($a, $b) {
                if (isset($a['last_submission']) && isset($b['last_submission'])) {
                    return $b['last_submission'] <=> $a['last_submission'];
                }
                return $b['has_votes'] <=> $a['has_votes'];
            });
        }

        return response()->json([
            'children' => $childData,
            'parent_id' => $parentId,
            'parent_type' => $parentType
        ]);
    }

    /**
     * Get users who submitted votes for a station
     */
    private function getSubmissionUsers($station, $auditLogs)
    {
        $users = [];
        
        if (isset($auditLogs[$station->id])) {
            foreach ($auditLogs[$station->id] as $log) {
                $userId = $log->submitted_by_user_id;
                $userType = $log->submitted_by_user_type;
                $submissionTime = $log->submission_time;

                if (!isset($users[$userId])) {
                    $users[$userId] = [
                        'id' => $userId,
                        'name' => $log->submittedBy->name ?? 'Unknown User',
                        'type' => $userType,
                        'submission_count' => 0,
                        'last_submission' => $submissionTime
                    ];
                }

                $users[$userId]['submission_count']++;
                if ($submissionTime > $users[$userId]['last_submission']) {
                    $users[$userId]['last_submission'] = $submissionTime;
                }
            }
        }

        return array_values($users);
    }

    /**
     * Get the last submission time for a station
     */
    private function getLastSubmissionTime($station, $auditLogs)
    {
        if (!isset($auditLogs[$station->id])) {
            return null;
        }

        return $auditLogs[$station->id]->max('submission_time');
    }

    /**
     * Aggregate user data at different hierarchy levels
     */
    private function aggregateUser(&$userArray, $user)
    {
        $userId = $user['id'];
        
        if (!isset($userArray[$userId])) {
            $userArray[$userId] = $user;
        } else {
            $userArray[$userId]['submission_count'] += $user['submission_count'];
            if ($user['last_submission'] > $userArray[$userId]['last_submission']) {
                $userArray[$userId]['last_submission'] = $user['last_submission'];
            }
        }
    }

    /**
     * Calculate percentages and sort hierarchical data
     */
    private function calculatePercentagesAndSort(&$hierarchicalData)
    {
        foreach ($hierarchicalData as &$subcounty) {
            // Calculate subcounty percentage
            $subcounty['percentage'] = $subcounty['total_stations'] > 0 
                ? round(($subcounty['submitted_stations'] / $subcounty['total_stations']) * 100, 1) 
                : 0;

            foreach ($subcounty['parishes'] as &$parish) {
                // Calculate parish percentage
                $parish['percentage'] = $parish['total_stations'] > 0 
                    ? round(($parish['submitted_stations'] / $parish['total_stations']) * 100, 1) 
                    : 0;

                foreach ($parish['villages'] as &$village) {
                    // Calculate village percentage
                    $village['percentage'] = $village['total_stations'] > 0 
                        ? round(($village['submitted_stations'] / $village['total_stations']) * 100, 1) 
                        : 0;

                    // Sort users by submission count (descending)
                    uasort($village['users'], function($a, $b) {
                        return $b['submission_count'] <=> $a['submission_count'];
                    });
                }

                // Sort villages by percentage (desc), then by submitted count (desc)
                uasort($parish['villages'], function($a, $b) {
                    if ($a['percentage'] == $b['percentage']) {
                        return $b['submitted_stations'] <=> $a['submitted_stations'];
                    }
                    return $b['percentage'] <=> $a['percentage'];
                });

                // Sort parish users by submission count (descending)
                uasort($parish['users'], function($a, $b) {
                    return $b['submission_count'] <=> $a['submission_count'];
                });
            }

            // Sort parishes by percentage (desc), then by submitted count (desc)
            uasort($subcounty['parishes'], function($a, $b) {
                if ($a['percentage'] == $b['percentage']) {
                    return $b['submitted_stations'] <=> $a['submitted_stations'];
                }
                return $b['percentage'] <=> $a['percentage'];
            });

            // Sort subcounty users by submission count (descending)
            uasort($subcounty['users'], function($a, $b) {
                return $b['submission_count'] <=> $a['submission_count'];
            });
        }

        // Sort subcounties by percentage (desc), then by submitted count (desc)
        uasort($hierarchicalData, function($a, $b) {
            if ($a['percentage'] == $b['percentage']) {
                return $b['submitted_stations'] <=> $a['submitted_stations'];
            }
            return $b['percentage'] <=> $a['percentage'];
        });
    }

    /**
     * Get filter options for dropdowns
     */
    private function getFilterOptions()
    {
        return [
            'districts' => PollingStation::distinct()->pluck('district')->filter()->sort()->values(),
            'counties' => PollingStation::distinct()->pluck('county')->filter()->sort()->values(),
            'subcounties' => PollingStation::distinct()->pluck('subcounty')->filter()->sort()->values(),
            'parishes' => PollingStation::distinct()->pluck('parish')->filter()->sort()->values(),
        ];
    }

    /**
     * Display agent submission statistics report
     */
    public function agentSubmissions(Request $request)
    {
        $districtFilter = $request->get('district');
        $countyFilter = $request->get('county');
        $subcountyFilter = $request->get('subcounty');
        $parishFilter = $request->get('parish');
        $dateFrom = $request->get('date_from');
        $dateTo = $request->get('date_to');
        $perPage = $request->get('per_page', 25);

        try {
            // Build agent submission data
            $reportData = $this->buildAgentSubmissionData(
                $districtFilter,
                $countyFilter,
                $subcountyFilter,
                $parishFilter,
                $dateFrom,
                $dateTo,
                $perPage
            );

            // Get summary statistics
            $summaryStats = $this->calculateAgentSummaryStats($reportData['data']);

            // Get filter options
            $filterOptions = $this->getFilterOptions();

            return view('reports.agent-submissions', [
                'reportData' => $reportData,
                'summaryStats' => $summaryStats,
                'filterOptions' => $filterOptions,
                'filters' => [
                    'district' => $districtFilter,
                    'county' => $countyFilter,
                    'subcounty' => $subcountyFilter,
                    'parish' => $parishFilter,
                    'date_from' => $dateFrom,
                    'date_to' => $dateTo,
                    'per_page' => $perPage
                ]
            ]);
        } catch (\Exception $e) {
            // For debugging - return error details
            return response()->json([
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ], 500);
        }
    }

    /**
     * Export agent submission report as CSV
     */
    public function exportAgentSubmissions(Request $request)
    {
        // Get hierarchical tree data and transform it for export
        $treeData = $this->buildAgentSubmissionData(
            $request->get('district'),
            $request->get('county'),
            $request->get('subcounty'),
            $request->get('parish'),
            $request->get('date_from'),
            $request->get('date_to'),
            1000 // Large number to get all data for export
        );

        // Transform tree data to export format
        $reportData = $this->transformAgentDataForExport($treeData['data']);

        $filename = 'agent_submission_report_' . date('Y-m-d_H-i-s') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function() use ($reportData) {
            $file = fopen('php://output', 'w');

            // CSV Headers
            fputcsv($file, [
                'Level',
                'Location Name',
                'Total Agents',
                'Active Agents',
                'Total Submissions',
                'Completion Rate',
                'Last Submission',
                'Top Performers'
            ]);

            foreach ($reportData as $subcounty) {
                // Subcounty row
                fputcsv($file, [
                    'Subcounty',
                    $subcounty['name'],
                    $subcounty['total_agents'],
                    $subcounty['active_agents'],
                    $subcounty['total_submissions'],
                    $subcounty['completion_rate'] . '%',
                    $subcounty['last_submission'] ? Carbon::parse($subcounty['last_submission'])->format('Y-m-d H:i:s') : '',
                    ''
                ]);

                foreach ($subcounty['parishes'] as $parish) {
                    // Parish row
                    fputcsv($file, [
                        'Parish',
                        '  ' . $parish['name'],
                        $parish['total_agents'],
                        $parish['active_agents'],
                        $parish['total_submissions'],
                        $parish['completion_rate'] . '%',
                        $parish['last_submission'] ? Carbon::parse($parish['last_submission'])->format('Y-m-d H:i:s') : '',
                        ''
                    ]);

                    foreach ($parish['villages'] as $village) {
                        // Village row
                        $topPerformers = collect($village['agents'])
                            ->sortByDesc('submission_count')
                            ->take(3)
                            ->pluck('name')
                            ->implode(', ');

                        fputcsv($file, [
                            'Village',
                            '    ' . $village['name'],
                            $village['total_agents'],
                            $village['active_agents'],
                            $village['total_submissions'],
                            $village['completion_rate'] . '%',
                            $village['last_submission'] ? Carbon::parse($village['last_submission'])->format('Y-m-d H:i:s') : '',
                            $topPerformers
                        ]);
                    }
                }
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * Build agent submission hierarchical data
     */
    private function buildAgentSubmissionData($districtFilter = null, $countyFilter = null, $subcountyFilter = null, $parishFilter = null, $dateFrom = null, $dateTo = null, $perPage = 25)
    {
        // Base query for agents with their polling stations
        $agentsQuery = Agent::with(['user', 'polling_station'])
            ->whereHas('user', function($query) {
                $query->where('is_active', true);
            })
            ->whereNotNull('polling_station_id');

        // Apply filters through polling station relationship
        if ($districtFilter || $countyFilter || $subcountyFilter || $parishFilter) {
            $agentsQuery->whereHas('polling_station', function($query) use ($districtFilter, $countyFilter, $subcountyFilter, $parishFilter) {
                if ($districtFilter) {
                    $query->where('district', $districtFilter);
                }
                if ($countyFilter) {
                    $query->where('county', $countyFilter);
                }
                if ($subcountyFilter) {
                    $query->where('subcounty', $subcountyFilter);
                }
                if ($parishFilter) {
                    $query->where('parish', $parishFilter);
                }
            });
        }

        $agents = $agentsQuery->get();

        // Get vote audit logs for submission tracking
        $auditLogsQuery = VoteAuditLog::select('polling_station_id', 'submitted_by_user_id', 'submission_time');

        if ($dateFrom) {
            $auditLogsQuery->where('submission_time', '>=', Carbon::parse($dateFrom)->startOfDay());
        }
        if ($dateTo) {
            $auditLogsQuery->where('submission_time', '<=', Carbon::parse($dateTo)->endOfDay());
        }

        $auditLogs = $auditLogsQuery->get()->groupBy('polling_station_id');

        // Build hierarchical structure
        $hierarchicalData = [];
        $rowId = 1;

        foreach ($agents as $agent) {
            if (!$agent->polling_station) continue;

            $station = $agent->polling_station;
            $subcounty = $station->subcounty ?: 'Unknown Subcounty';
            $parish = $station->parish ?: 'Unknown Parish';
            $village = $station->village ?: 'Unknown Village';

            // Create unique IDs for hierarchy levels
            $subcountyId = 'subcounty_' . md5($subcounty);
            $parishId = 'parish_' . md5($subcounty . '|' . $parish);
            $villageId = 'village_' . md5($subcounty . '|' . $parish . '|' . $village);

            // Get agent submission data
            $agentSubmissions = $this->getAgentSubmissionData($agent, $auditLogs, $dateFrom, $dateTo);

            // Initialize subcounty if not exists
            if (!isset($hierarchicalData[$subcountyId])) {
                $hierarchicalData[$subcountyId] = [
                    'id' => $subcountyId,
                    'row_id' => $rowId++,
                    'name' => $subcounty,
                    'level' => 1,
                    'type' => 'subcounty',
                    'parent_id' => null,
                    'total_agents' => 0,
                    'active_agents' => 0,
                    'total_submissions' => 0,
                    'completion_rate' => 0,
                    'last_submission' => null,
                    'agents' => [],
                    'children' => [],
                    'is_expanded' => false,
                    'has_children' => false
                ];
            }

            // Initialize parish if not exists
            if (!isset($hierarchicalData[$subcountyId]['children'][$parishId])) {
                $hierarchicalData[$subcountyId]['children'][$parishId] = [
                    'id' => $parishId,
                    'row_id' => $rowId++,
                    'name' => $parish,
                    'level' => 2,
                    'type' => 'parish',
                    'parent_id' => $subcountyId,
                    'total_agents' => 0,
                    'active_agents' => 0,
                    'total_submissions' => 0,
                    'completion_rate' => 0,
                    'last_submission' => null,
                    'agents' => [],
                    'children' => [],
                    'is_expanded' => false,
                    'has_children' => false
                ];
                $hierarchicalData[$subcountyId]['has_children'] = true;
            }

            // Initialize village if not exists
            if (!isset($hierarchicalData[$subcountyId]['children'][$parishId]['children'][$villageId])) {
                $hierarchicalData[$subcountyId]['children'][$parishId]['children'][$villageId] = [
                    'id' => $villageId,
                    'row_id' => $rowId++,
                    'name' => $village,
                    'level' => 3,
                    'type' => 'village',
                    'parent_id' => $parishId,
                    'total_agents' => 0,
                    'active_agents' => 0,
                    'total_submissions' => 0,
                    'completion_rate' => 0,
                    'last_submission' => null,
                    'agents' => [],
                    'children' => [],
                    'is_expanded' => false,
                    'has_children' => false
                ];
                $hierarchicalData[$subcountyId]['children'][$parishId]['has_children'] = true;
            }

            // Add agent data to village
            $villageData = &$hierarchicalData[$subcountyId]['children'][$parishId]['children'][$villageId];
            $villageData['total_agents']++;
            $villageData['agents'][] = $agentSubmissions;

            if ($agentSubmissions['has_submissions']) {
                $villageData['active_agents']++;
                $villageData['total_submissions'] += $agentSubmissions['submission_count'];

                if (!$villageData['last_submission'] || $agentSubmissions['last_submission'] > $villageData['last_submission']) {
                    $villageData['last_submission'] = $agentSubmissions['last_submission'];
                }
            }

            // Update parish aggregates
            $parishData = &$hierarchicalData[$subcountyId]['children'][$parishId];
            $parishData['total_agents']++;
            if ($agentSubmissions['has_submissions']) {
                $parishData['active_agents']++;
                $parishData['total_submissions'] += $agentSubmissions['submission_count'];

                if (!$parishData['last_submission'] || $agentSubmissions['last_submission'] > $parishData['last_submission']) {
                    $parishData['last_submission'] = $agentSubmissions['last_submission'];
                }
            }

            // Update subcounty aggregates
            $subcountyData = &$hierarchicalData[$subcountyId];
            $subcountyData['total_agents']++;
            if ($agentSubmissions['has_submissions']) {
                $subcountyData['active_agents']++;
                $subcountyData['total_submissions'] += $agentSubmissions['submission_count'];

                if (!$subcountyData['last_submission'] || $agentSubmissions['last_submission'] > $subcountyData['last_submission']) {
                    $subcountyData['last_submission'] = $agentSubmissions['last_submission'];
                }
            }
        }

        // Calculate completion rates
        $this->calculateAgentCompletionRates($hierarchicalData);

        // Convert to array and paginate
        $dataArray = array_values($hierarchicalData);
        $total = count($dataArray);
        $currentPage = request()->get('page', 1);
        $offset = ($currentPage - 1) * $perPage;
        $paginatedData = array_slice($dataArray, $offset, $perPage);

        $pagination = new \Illuminate\Pagination\LengthAwarePaginator(
            $paginatedData,
            $total,
            $perPage,
            $currentPage,
            ['path' => request()->url(), 'query' => request()->query()]
        );

        return [
            'data' => $paginatedData,
            'pagination' => $pagination
        ];
    }

    /**
     * Get agent submission data
     */
    private function getAgentSubmissionData($agent, $auditLogs, $dateFrom = null, $dateTo = null)
    {
        $stationId = $agent->polling_station->id;
        $stationLogs = $auditLogs->get($stationId, collect());

        // Filter logs by agent
        $agentLogs = $stationLogs->filter(function($log) use ($agent) {
            return $log->submitted_by_user_id == $agent->user_id;
        });

        $submissionCount = $agentLogs->count();
        $lastSubmission = $agentLogs->max('submission_time');

        return [
            'id' => $agent->id,
            'name' => $agent->user->name ?? 'Unknown Agent',
            'phone' => $agent->user->phone_number ?? '',
            'station_name' => $agent->polling_station->name ?? 'Unknown Station',
            'submission_count' => $submissionCount,
            'has_submissions' => $submissionCount > 0,
            'last_submission' => $lastSubmission,
            'completion_rate' => $submissionCount > 0 ? 100 : 0, // Simple completion for agents
            'performance_score' => $this->calculateAgentPerformanceScore($submissionCount, $lastSubmission)
        ];
    }

    /**
     * Calculate agent performance score
     */
    private function calculateAgentPerformanceScore($submissionCount, $lastSubmission)
    {
        $score = 0;

        // Base score from submission count
        $score += min($submissionCount * 10, 50);

        // Recency bonus
        if ($lastSubmission) {
            $daysSinceSubmission = Carbon::now()->diffInDays(Carbon::parse($lastSubmission));
            if ($daysSinceSubmission <= 1) {
                $score += 30;
            } elseif ($daysSinceSubmission <= 7) {
                $score += 20;
            } elseif ($daysSinceSubmission <= 30) {
                $score += 10;
            }
        }

        return min($score, 100);
    }

    /**
     * Calculate completion rates for agent data
     */
    private function calculateAgentCompletionRates(&$hierarchicalData)
    {
        foreach ($hierarchicalData as &$subcounty) {
            // Calculate subcounty completion rate
            $subcounty['completion_rate'] = $subcounty['total_agents'] > 0
                ? round(($subcounty['active_agents'] / $subcounty['total_agents']) * 100, 1)
                : 0;

            foreach ($subcounty['children'] as &$parish) {
                // Calculate parish completion rate
                $parish['completion_rate'] = $parish['total_agents'] > 0
                    ? round(($parish['active_agents'] / $parish['total_agents']) * 100, 1)
                    : 0;

                foreach ($parish['children'] as &$village) {
                    // Calculate village completion rate
                    $village['completion_rate'] = $village['total_agents'] > 0
                        ? round(($village['active_agents'] / $village['total_agents']) * 100, 1)
                        : 0;

                    // Sort agents by performance score (descending)
                    uasort($village['agents'], function($a, $b) {
                        return $b['performance_score'] <=> $a['performance_score'];
                    });
                }

                // Sort villages by completion rate (descending)
                uasort($parish['children'], function($a, $b) {
                    return $b['completion_rate'] <=> $a['completion_rate'];
                });
            }

            // Sort parishes by completion rate (descending)
            uasort($subcounty['children'], function($a, $b) {
                return $b['completion_rate'] <=> $a['completion_rate'];
            });
        }
    }

    /**
     * Calculate agent summary statistics
     */
    private function calculateAgentSummaryStats($data)
    {
        $totalAgents = 0;
        $activeAgents = 0;
        $totalSubmissions = 0;
        $topPerformers = [];

        foreach ($data as $subcounty) {
            $totalAgents += $subcounty['total_agents'];
            $activeAgents += $subcounty['active_agents'];
            $totalSubmissions += $subcounty['total_submissions'];

            // Collect top performers from all levels
            $this->collectTopPerformers($subcounty, $topPerformers);
        }

        // Sort and limit top performers
        usort($topPerformers, function($a, $b) {
            return $b['performance_score'] <=> $a['performance_score'];
        });

        return [
            'total_agents' => $totalAgents,
            'active_agents' => $activeAgents,
            'inactive_agents' => $totalAgents - $activeAgents,
            'total_submissions' => $totalSubmissions,
            'average_submissions' => $activeAgents > 0 ? round($totalSubmissions / $activeAgents, 1) : 0,
            'completion_rate' => $totalAgents > 0 ? round(($activeAgents / $totalAgents) * 100, 1) : 0,
            'top_performers' => array_slice($topPerformers, 0, 10)
        ];
    }

    /**
     * Collect top performers from hierarchical data
     */
    private function collectTopPerformers($node, &$topPerformers)
    {
        if (isset($node['agents'])) {
            foreach ($node['agents'] as $agent) {
                if ($agent['has_submissions']) {
                    $topPerformers[] = $agent;
                }
            }
        }

        if (isset($node['children'])) {
            foreach ($node['children'] as $child) {
                $this->collectTopPerformers($child, $topPerformers);
            }
        }
    }

    /**
     * Transform agent data for export
     */
    private function transformAgentDataForExport($treeData)
    {
        $exportData = [];

        foreach ($treeData as $subcounty) {
            $subcountyData = [
                'name' => $subcounty['name'],
                'total_agents' => $subcounty['total_agents'],
                'active_agents' => $subcounty['active_agents'],
                'total_submissions' => $subcounty['total_submissions'],
                'completion_rate' => $subcounty['completion_rate'],
                'last_submission' => $subcounty['last_submission'],
                'parishes' => []
            ];

            foreach ($subcounty['children'] as $parish) {
                $parishData = [
                    'name' => $parish['name'],
                    'total_agents' => $parish['total_agents'],
                    'active_agents' => $parish['active_agents'],
                    'total_submissions' => $parish['total_submissions'],
                    'completion_rate' => $parish['completion_rate'],
                    'last_submission' => $parish['last_submission'],
                    'villages' => []
                ];

                foreach ($parish['children'] as $village) {
                    $villageData = [
                        'name' => $village['name'],
                        'total_agents' => $village['total_agents'],
                        'active_agents' => $village['active_agents'],
                        'total_submissions' => $village['total_submissions'],
                        'completion_rate' => $village['completion_rate'],
                        'last_submission' => $village['last_submission'],
                        'agents' => $village['agents']
                    ];

                    $parishData['villages'][] = $villageData;
                }

                $subcountyData['parishes'][] = $parishData;
            }

            $exportData[] = $subcountyData;
        }

        return $exportData;
    }
}
