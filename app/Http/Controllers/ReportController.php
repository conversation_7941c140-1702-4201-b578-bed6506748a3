<?php

namespace App\Http\Controllers;

use App\Models\PollingStation;
use App\Models\Vote;
use App\Models\VoteAuditLog;
use App\Models\Agent;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class ReportController extends Controller
{
    /**
     * Generate comprehensive polling station submission report
     */
    public function pollingStationSubmissionReport(Request $request)
    {
        // Get filter parameters
        $districtFilter = $request->get('district');
        $countyFilter = $request->get('county');
        $subcountyFilter = $request->get('subcounty');
        $parishFilter = $request->get('parish');
        $dateFrom = $request->get('date_from');
        $dateTo = $request->get('date_to');

        // Build the comprehensive report data
        $reportData = $this->buildSubmissionReportData($districtFilter, $countyFilter, $subcountyFilter, $parishFilter, $dateFrom, $dateTo);

        // Get filter options for dropdowns
        $filterOptions = $this->getFilterOptions();

        return view('reports.polling-station-submission', compact('reportData', 'filterOptions', 'districtFilter', 'countyFilter', 'subcountyFilter', 'parishFilter', 'dateFrom', 'dateTo'));
    }

    /**
     * Build the hierarchical submission report data
     */
    private function buildSubmissionReportData($districtFilter = null, $countyFilter = null, $subcountyFilter = null, $parishFilter = null, $dateFrom = null, $dateTo = null)
    {
        // Base query for polling stations
        $stationsQuery = PollingStation::query();
        
        // Apply filters
        if ($districtFilter) {
            $stationsQuery->where('district', $districtFilter);
        }
        if ($countyFilter) {
            $stationsQuery->where('county', $countyFilter);
        }
        if ($subcountyFilter) {
            $stationsQuery->where('subcounty', $subcountyFilter);
        }
        if ($parishFilter) {
            $stationsQuery->where('parish', $parishFilter);
        }

        // Get all polling stations with their submission status
        $stations = $stationsQuery->with(['agents.user', 'agents.votes'])->get();

        // Get vote audit logs for user attribution and timestamps
        $auditLogsQuery = VoteAuditLog::with(['submittedBy', 'pollingStation'])
            ->select('polling_station_id', 'submitted_by_user_id', 'submitted_by_user_type', 'submission_time')
            ->distinct();

        if ($dateFrom) {
            $auditLogsQuery->where('submission_time', '>=', Carbon::parse($dateFrom)->startOfDay());
        }
        if ($dateTo) {
            $auditLogsQuery->where('submission_time', '<=', Carbon::parse($dateTo)->endOfDay());
        }

        $auditLogs = $auditLogsQuery->get()->groupBy('polling_station_id');

        // Process stations and build hierarchical data
        $hierarchicalData = [];

        foreach ($stations as $station) {
            $hasVotes = $this->stationHasVotes($station, $dateFrom, $dateTo);
            $submissionUsers = $this->getSubmissionUsers($station, $auditLogs);

            // Build hierarchy: Subcounty -> Parish -> Village
            $subcounty = $station->subcounty ?: 'Unknown Subcounty';
            $parish = $station->parish ?: 'Unknown Parish';
            $village = $station->village ?: 'Unknown Village';

            if (!isset($hierarchicalData[$subcounty])) {
                $hierarchicalData[$subcounty] = [
                    'name' => $subcounty,
                    'total_stations' => 0,
                    'submitted_stations' => 0,
                    'percentage' => 0,
                    'parishes' => [],
                    'users' => []
                ];
            }

            if (!isset($hierarchicalData[$subcounty]['parishes'][$parish])) {
                $hierarchicalData[$subcounty]['parishes'][$parish] = [
                    'name' => $parish,
                    'total_stations' => 0,
                    'submitted_stations' => 0,
                    'percentage' => 0,
                    'villages' => [],
                    'users' => []
                ];
            }

            if (!isset($hierarchicalData[$subcounty]['parishes'][$parish]['villages'][$village])) {
                $hierarchicalData[$subcounty]['parishes'][$parish]['villages'][$village] = [
                    'name' => $village,
                    'total_stations' => 0,
                    'submitted_stations' => 0,
                    'percentage' => 0,
                    'stations' => [],
                    'users' => []
                ];
            }

            // Add station data
            $stationData = [
                'id' => $station->id,
                'name' => $station->name,
                'has_votes' => $hasVotes,
                'submission_users' => $submissionUsers,
                'last_submission' => $this->getLastSubmissionTime($station, $auditLogs)
            ];

            $hierarchicalData[$subcounty]['parishes'][$parish]['villages'][$village]['stations'][] = $stationData;
            $hierarchicalData[$subcounty]['parishes'][$parish]['villages'][$village]['total_stations']++;
            $hierarchicalData[$subcounty]['parishes'][$parish]['total_stations']++;
            $hierarchicalData[$subcounty]['total_stations']++;

            if ($hasVotes) {
                $hierarchicalData[$subcounty]['parishes'][$parish]['villages'][$village]['submitted_stations']++;
                $hierarchicalData[$subcounty]['parishes'][$parish]['submitted_stations']++;
                $hierarchicalData[$subcounty]['submitted_stations']++;

                // Aggregate users at all levels
                foreach ($submissionUsers as $user) {
                    $this->aggregateUser($hierarchicalData[$subcounty]['users'], $user);
                    $this->aggregateUser($hierarchicalData[$subcounty]['parishes'][$parish]['users'], $user);
                    $this->aggregateUser($hierarchicalData[$subcounty]['parishes'][$parish]['villages'][$village]['users'], $user);
                }
            }
        }

        // Calculate percentages and sort
        $this->calculatePercentagesAndSort($hierarchicalData);

        return $hierarchicalData;
    }

    /**
     * Check if a station has submitted votes within the date range
     */
    private function stationHasVotes($station, $dateFrom = null, $dateTo = null)
    {
        $agentIds = $station->agents->pluck('id');

        if ($agentIds->isEmpty()) {
            return false;
        }

        $votesQuery = Vote::whereIn('agent_id', $agentIds);

        if ($dateFrom) {
            $votesQuery->where('created_at', '>=', Carbon::parse($dateFrom)->startOfDay());
        }
        if ($dateTo) {
            $votesQuery->where('created_at', '<=', Carbon::parse($dateTo)->endOfDay());
        }

        return $votesQuery->exists();
    }

    /**
     * Export report data as CSV
     */
    public function exportSubmissionReport(Request $request)
    {
        $reportData = $this->buildSubmissionReportData(
            $request->get('district'),
            $request->get('county'),
            $request->get('subcounty'),
            $request->get('parish'),
            $request->get('date_from'),
            $request->get('date_to')
        );

        $filename = 'polling_station_submission_report_' . date('Y-m-d_H-i-s') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function() use ($reportData) {
            $file = fopen('php://output', 'w');

            // CSV Headers
            fputcsv($file, [
                'Level',
                'Location Name',
                'Total Stations',
                'Submitted Stations',
                'Pending Stations',
                'Completion Percentage',
                'Contributors',
                'Last Submission'
            ]);

            foreach ($reportData as $subcounty) {
                // Subcounty row
                fputcsv($file, [
                    'Subcounty',
                    $subcounty['name'],
                    $subcounty['total_stations'],
                    $subcounty['submitted_stations'],
                    $subcounty['total_stations'] - $subcounty['submitted_stations'],
                    $subcounty['percentage'] . '%',
                    count($subcounty['users']) . ' users',
                    ''
                ]);

                foreach ($subcounty['parishes'] as $parish) {
                    // Parish row
                    fputcsv($file, [
                        'Parish',
                        '  ' . $parish['name'],
                        $parish['total_stations'],
                        $parish['submitted_stations'],
                        $parish['total_stations'] - $parish['submitted_stations'],
                        $parish['percentage'] . '%',
                        count($parish['users']) . ' users',
                        ''
                    ]);

                    foreach ($parish['villages'] as $village) {
                        // Village row
                        $lastSubmission = '';
                        if (!empty($village['users'])) {
                            $lastSubmission = max(array_column($village['users'], 'last_submission'));
                            $lastSubmission = Carbon::parse($lastSubmission)->format('Y-m-d H:i:s');
                        }

                        fputcsv($file, [
                            'Village',
                            '    ' . $village['name'],
                            $village['total_stations'],
                            $village['submitted_stations'],
                            $village['total_stations'] - $village['submitted_stations'],
                            $village['percentage'] . '%',
                            implode(', ', array_column($village['users'], 'name')),
                            $lastSubmission
                        ]);
                    }
                }
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * Get users who submitted votes for a station
     */
    private function getSubmissionUsers($station, $auditLogs)
    {
        $users = [];
        
        if (isset($auditLogs[$station->id])) {
            foreach ($auditLogs[$station->id] as $log) {
                $userId = $log->submitted_by_user_id;
                $userType = $log->submitted_by_user_type;
                $submissionTime = $log->submission_time;

                if (!isset($users[$userId])) {
                    $users[$userId] = [
                        'id' => $userId,
                        'name' => $log->submittedBy->name ?? 'Unknown User',
                        'type' => $userType,
                        'submission_count' => 0,
                        'last_submission' => $submissionTime
                    ];
                }

                $users[$userId]['submission_count']++;
                if ($submissionTime > $users[$userId]['last_submission']) {
                    $users[$userId]['last_submission'] = $submissionTime;
                }
            }
        }

        return array_values($users);
    }

    /**
     * Get the last submission time for a station
     */
    private function getLastSubmissionTime($station, $auditLogs)
    {
        if (!isset($auditLogs[$station->id])) {
            return null;
        }

        return $auditLogs[$station->id]->max('submission_time');
    }

    /**
     * Aggregate user data at different hierarchy levels
     */
    private function aggregateUser(&$userArray, $user)
    {
        $userId = $user['id'];
        
        if (!isset($userArray[$userId])) {
            $userArray[$userId] = $user;
        } else {
            $userArray[$userId]['submission_count'] += $user['submission_count'];
            if ($user['last_submission'] > $userArray[$userId]['last_submission']) {
                $userArray[$userId]['last_submission'] = $user['last_submission'];
            }
        }
    }

    /**
     * Calculate percentages and sort hierarchical data
     */
    private function calculatePercentagesAndSort(&$hierarchicalData)
    {
        foreach ($hierarchicalData as &$subcounty) {
            // Calculate subcounty percentage
            $subcounty['percentage'] = $subcounty['total_stations'] > 0 
                ? round(($subcounty['submitted_stations'] / $subcounty['total_stations']) * 100, 1) 
                : 0;

            foreach ($subcounty['parishes'] as &$parish) {
                // Calculate parish percentage
                $parish['percentage'] = $parish['total_stations'] > 0 
                    ? round(($parish['submitted_stations'] / $parish['total_stations']) * 100, 1) 
                    : 0;

                foreach ($parish['villages'] as &$village) {
                    // Calculate village percentage
                    $village['percentage'] = $village['total_stations'] > 0 
                        ? round(($village['submitted_stations'] / $village['total_stations']) * 100, 1) 
                        : 0;

                    // Sort users by submission count (descending)
                    uasort($village['users'], function($a, $b) {
                        return $b['submission_count'] <=> $a['submission_count'];
                    });
                }

                // Sort villages by percentage (desc), then by submitted count (desc)
                uasort($parish['villages'], function($a, $b) {
                    if ($a['percentage'] == $b['percentage']) {
                        return $b['submitted_stations'] <=> $a['submitted_stations'];
                    }
                    return $b['percentage'] <=> $a['percentage'];
                });

                // Sort parish users by submission count (descending)
                uasort($parish['users'], function($a, $b) {
                    return $b['submission_count'] <=> $a['submission_count'];
                });
            }

            // Sort parishes by percentage (desc), then by submitted count (desc)
            uasort($subcounty['parishes'], function($a, $b) {
                if ($a['percentage'] == $b['percentage']) {
                    return $b['submitted_stations'] <=> $a['submitted_stations'];
                }
                return $b['percentage'] <=> $a['percentage'];
            });

            // Sort subcounty users by submission count (descending)
            uasort($subcounty['users'], function($a, $b) {
                return $b['submission_count'] <=> $a['submission_count'];
            });
        }

        // Sort subcounties by percentage (desc), then by submitted count (desc)
        uasort($hierarchicalData, function($a, $b) {
            if ($a['percentage'] == $b['percentage']) {
                return $b['submitted_stations'] <=> $a['submitted_stations'];
            }
            return $b['percentage'] <=> $a['percentage'];
        });
    }

    /**
     * Get filter options for dropdowns
     */
    private function getFilterOptions()
    {
        return [
            'districts' => PollingStation::distinct()->pluck('district')->filter()->sort()->values(),
            'counties' => PollingStation::distinct()->pluck('county')->filter()->sort()->values(),
            'subcounties' => PollingStation::distinct()->pluck('subcounty')->filter()->sort()->values(),
            'parishes' => PollingStation::distinct()->pluck('parish')->filter()->sort()->values(),
        ];
    }
}
