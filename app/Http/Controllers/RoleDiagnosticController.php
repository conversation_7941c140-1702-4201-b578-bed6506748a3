<?php

namespace App\Http\Controllers;

use App\Models\Role;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class RoleDiagnosticController extends Controller
{
    /**
     * Diagnose and fix user role configuration issues
     */
    public function diagnoseAndFix(Request $request)
    {
        $user = Auth::user();
        $diagnostics = [];
        $fixes = [];

        // 1. Check if user is active
        $diagnostics['user_active'] = $user->is_active;
        if (!$user->is_active) {
            return response()->json([
                'status' => 'error',
                'message' => 'User account is not active',
                'diagnostics' => $diagnostics
            ]);
        }

        // 2. Check user type
        $diagnostics['user_type'] = $user->user_type;
        $diagnostics['expected_role'] = $user->getUserTypeRole();

        // 3. Check if user has a primary role assigned
        $diagnostics['has_primary_role'] = !is_null($user->role_id);
        $diagnostics['primary_role'] = $user->role ? $user->role->name : null;

        // 4. Check if the expected role exists in database
        $expectedRole = Role::where('name', $user->getUserTypeRole())->first();
        $diagnostics['expected_role_exists'] = !is_null($expectedRole);

        // 5. Auto-fix: Create missing role if it doesn't exist
        if (!$expectedRole) {
            $predefinedRoles = Role::getPredefinedRoles();
            $roleData = $predefinedRoles[$user->getUserTypeRole()] ?? null;
            
            if ($roleData) {
                $expectedRole = Role::create([
                    'name' => $roleData['name'],
                    'display_name' => $roleData['display_name'],
                    'description' => $roleData['description'],
                    'permissions' => $roleData['permissions'],
                    'is_active' => true
                ]);
                $fixes[] = "Created missing role: {$roleData['display_name']}";
                $diagnostics['expected_role_exists'] = true;
            }
        }

        // 6. Auto-fix: Assign primary role if missing
        if (!$user->role_id && $expectedRole) {
            $user->role_id = $expectedRole->id;
            $user->save();
            $fixes[] = "Assigned primary role: {$expectedRole->display_name}";
            $diagnostics['has_primary_role'] = true;
            $diagnostics['primary_role'] = $expectedRole->name;
        }

        // 7. Check permissions for reports access
        $requiredPermissions = ['view_dashboard', 'view_reports'];
        $permissionStatus = [];
        
        foreach ($requiredPermissions as $permission) {
            $hasPermission = $user->hasPermission($permission);
            $permissionStatus[$permission] = $hasPermission;
            
            if (!$hasPermission && $expectedRole) {
                // Add missing permission to role
                $rolePermissions = $expectedRole->permissions ?? [];
                if (!in_array($permission, $rolePermissions)) {
                    $rolePermissions[] = $permission;
                    $expectedRole->permissions = $rolePermissions;
                    $expectedRole->save();
                    $fixes[] = "Added permission '{$permission}' to role '{$expectedRole->display_name}'";
                    $permissionStatus[$permission] = true;
                }
            }
        }
        
        $diagnostics['permissions'] = $permissionStatus;

        // 8. Check if user can access the reports page
        $canAccessReports = $user->hasPermission('view_dashboard') && $user->hasPermission('view_reports');
        $diagnostics['can_access_reports'] = $canAccessReports;

        // 9. Get user type permissions
        $diagnostics['user_type_permissions'] = $user->getUserTypePermissions();

        return response()->json([
            'status' => 'success',
            'message' => count($fixes) > 0 ? 'Role configuration fixed' : 'Role configuration is correct',
            'diagnostics' => $diagnostics,
            'fixes_applied' => $fixes,
            'user' => [
                'id' => $user->id,
                'name' => $user->name,
                'user_type' => $user->user_type,
                'role_id' => $user->role_id,
                'role_name' => $user->role ? $user->role->name : null,
                'is_active' => $user->is_active
            ]
        ]);
    }

    /**
     * Initialize all predefined roles
     */
    public function initializeRoles()
    {
        $predefinedRoles = Role::getPredefinedRoles();
        $created = [];
        $updated = [];

        foreach ($predefinedRoles as $roleData) {
            $role = Role::updateOrCreate(
                ['name' => $roleData['name']],
                [
                    'display_name' => $roleData['display_name'],
                    'description' => $roleData['description'],
                    'permissions' => $roleData['permissions'],
                    'is_active' => true
                ]
            );

            if ($role->wasRecentlyCreated) {
                $created[] = $role->display_name;
            } else {
                $updated[] = $role->display_name;
            }
        }

        return response()->json([
            'status' => 'success',
            'message' => 'Roles initialized successfully',
            'created' => $created,
            'updated' => $updated
        ]);
    }

    /**
     * Fix all users' role assignments
     */
    public function fixAllUserRoles()
    {
        $users = User::where('is_active', true)->whereNull('role_id')->get();
        $fixed = [];

        foreach ($users as $user) {
            $expectedRoleName = $user->getUserTypeRole();
            $role = Role::where('name', $expectedRoleName)->first();
            
            if ($role) {
                $user->role_id = $role->id;
                $user->save();
                $fixed[] = [
                    'user' => $user->name,
                    'user_type' => $user->user_type,
                    'assigned_role' => $role->display_name
                ];
            }
        }

        return response()->json([
            'status' => 'success',
            'message' => 'User roles fixed',
            'fixed_users' => $fixed,
            'count' => count($fixed)
        ]);
    }

    /**
     * Get current user's complete role information
     */
    public function getCurrentUserInfo()
    {
        $user = Auth::user();
        
        return response()->json([
            'user' => [
                'id' => $user->id,
                'name' => $user->name,
                'phone_number' => $user->phone_number,
                'user_type' => $user->user_type,
                'is_active' => $user->is_active,
                'role_id' => $user->role_id,
                'primary_role' => $user->role ? [
                    'id' => $user->role->id,
                    'name' => $user->role->name,
                    'display_name' => $user->role->display_name,
                    'permissions' => $user->role->permissions
                ] : null,
                'additional_roles' => $user->roles->map(function($role) {
                    return [
                        'id' => $role->id,
                        'name' => $role->name,
                        'display_name' => $role->display_name,
                        'permissions' => $role->permissions
                    ];
                }),
                'user_type_permissions' => $user->getUserTypePermissions(),
                'expected_role' => $user->getUserTypeRole(),
                'can_access_reports' => $user->hasPermission('view_dashboard') && $user->hasPermission('view_reports'),
                'all_permissions_check' => [
                    'view_dashboard' => $user->hasPermission('view_dashboard'),
                    'view_reports' => $user->hasPermission('view_reports'),
                    'view_analytics' => $user->hasPermission('view_analytics'),
                    'view_polling_stations' => $user->hasPermission('view_polling_stations'),
                ]
            ]
        ]);
    }
}
