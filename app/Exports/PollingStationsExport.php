<?php

namespace App\Exports;

use App\Models\PollingStation;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;

class PollingStationsExport implements FromCollection, WithHeadings, WithMapping, WithStyles, ShouldAutoSize
{
    protected $search;
    protected $subcounty;
    protected $parish;
    protected $village;
    protected $status;

    public function __construct($search = null, $subcounty = null, $parish = null, $village = null, $status = null)
    {
        $this->search = $search;
        $this->subcounty = $subcounty;
        $this->parish = $parish;
        $this->village = $village;
        $this->status = $status;
    }

    /**
     * @return \Illuminate\Support\Collection
     */
    public function collection()
    {
        // Build polling stations query with the same filters as the dashboard
        $query = PollingStation::with(['agent.user', 'agent.votes', 'agent.eveidences']);

        // Apply filters
        if ($this->search) {
            $query->where(function($q) {
                $q->where('name', 'like', "%{$this->search}%")
                  ->orWhere('district', 'like', "%{$this->search}%")
                  ->orWhere('county', 'like', "%{$this->search}%")
                  ->orWhere('subcounty', 'like', "%{$this->search}%")
                  ->orWhere('parish', 'like', "%{$this->search}%")
                  ->orWhere('village', 'like', "%{$this->search}%");
            });
        }

        if ($this->subcounty) {
            $query->where('subcounty', $this->subcounty);
        }

        if ($this->parish) {
            $query->where('parish', $this->parish);
        }

        if ($this->village) {
            $query->where('village', $this->village);
        }

        // Apply status filter
        if ($this->status === 'submitted') {
            $query->whereHas('agent.votes');
        } elseif ($this->status === 'pending') {
            $query->whereDoesntHave('agent.votes');
        }

        return $query->get();
    }

    /**
     * @return array
     */
    public function headings(): array
    {
        return [
            'Station ID',
            'Station Name',
            'District',
            'County',
            'Subcounty',
            'Parish',
            'Village',
            'Agent Name',
            'Agent Phone',
            'Status',
            'Total Votes',
            'Evidence Count',
            'Last Updated'
        ];
    }

    /**
     * @param mixed $row
     * @return array
     */
    public function map($station): array
    {
        $agent = $station->agent;
        $hasVotes = $agent && $agent->votes->count() > 0;
        $totalVotes = $agent ? $agent->votes->sum('number_of_votes') : 0;
        $evidenceCount = $agent ? $agent->eveidences->count() : 0;
        $lastUpdated = $hasVotes ? $agent->votes->max('updated_at') : null;

        return [
            $station->id,
            $station->name,
            $station->district,
            $station->county,
            $station->subcounty,
            $station->parish,
            $station->village,
            $agent ? $agent->user->name : 'Not Assigned',
            $agent ? $agent->user->phone_number : 'N/A',
            $hasVotes ? 'Submitted' : 'Pending',
            $totalVotes,
            $evidenceCount,
            $lastUpdated ? $lastUpdated->format('Y-m-d H:i:s') : 'N/A'
        ];
    }

    /**
     * @param Worksheet $sheet
     */
    public function styles(Worksheet $sheet)
    {
        return [
            // Style the first row (header row)
            1 => [
                'font' => ['bold' => true],
                'fill' => [
                    'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                    'startColor' => ['rgb' => 'E2EFDA']
                ]
            ]
        ];
    }
}
