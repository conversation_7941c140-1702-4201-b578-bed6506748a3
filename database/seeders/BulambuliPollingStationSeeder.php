<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\PollingStation;

class BulambuliPollingStationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('Adding sample Bulambuli polling stations...');

        // Sample Bulambuli polling stations
        $bulambuliStations = [
            [
                'name' => 'Bulambuli Primary School',
                'constituency' => 'Bulambuli Constituency',
                'district' => 'Bulambuli',
                'county' => 'Bulambuli County',
                'subcounty' => 'Bulambuli',
                'parish' => 'Bulambuli Central',
                'village' => 'Bulambuli Trading Centre',
                'latitude' => 1.0833,
                'longitude' => 34.3667
            ],
            [
                'name' => 'Bulegeni Health Centre',
                'constituency' => 'Bulambuli Constituency',
                'district' => 'Bulambuli',
                'county' => 'Bulambuli County',
                'subcounty' => 'Bulegeni',
                'parish' => 'Bulegeni Central',
                'village' => 'Bulegeni Centre',
                'latitude' => 1.0500,
                'longitude' => 34.3500
            ],
            [
                'name' => 'Buwalasi Community Centre',
                'constituency' => 'Bulambuli Constituency',
                'district' => 'Bulambuli',
                'county' => 'Bulambuli County',
                'subcounty' => 'Buwalasi',
                'parish' => 'Buwalasi Central',
                'village' => 'Buwalasi Market',
                'latitude' => 1.1000,
                'longitude' => 34.4000
            ],
            [
                'name' => 'Lwakhakha Border Post',
                'constituency' => 'Bulambuli Constituency',
                'district' => 'Bulambuli',
                'county' => 'Bulambuli County',
                'subcounty' => 'Lwakhakha',
                'parish' => 'Lwakhakha Central',
                'village' => 'Lwakhakha Border',
                'latitude' => 1.0667,
                'longitude' => 34.3833
            ],
            [
                'name' => 'Masaba Secondary School',
                'constituency' => 'Bulambuli Constituency',
                'district' => 'Bulambuli',
                'county' => 'Bulambuli County',
                'subcounty' => 'Masaba',
                'parish' => 'Masaba Central',
                'village' => 'Masaba Town',
                'latitude' => 1.0750,
                'longitude' => 34.3750
            ]
        ];

        foreach ($bulambuliStations as $stationData) {
            // Check if station already exists
            $existingStation = PollingStation::where('name', $stationData['name'])
                ->where('district', 'Bulambuli')
                ->first();

            if (!$existingStation) {
                PollingStation::create($stationData);
                $this->command->info("Created polling station: {$stationData['name']}");
            } else {
                $this->command->info("Polling station already exists: {$stationData['name']}");
            }
        }

        $this->command->info('Bulambuli polling stations seeding completed!');
    }
}
