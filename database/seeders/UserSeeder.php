<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $saveDefaultUser = new User();
        $saveDefaultUser->name = "Vote Admin";
        $saveDefaultUser->phone_number = "0785297660";
        $saveDefaultUser->user_type = "admin";
        $saveDefaultUser->password = Hash::make('Admin123@@');
        try {
            $saveDefaultUser->save();
        } catch (\Throwable $th) {}
        
    }
}
