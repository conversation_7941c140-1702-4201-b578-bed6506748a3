<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Remove duplicate agent records, keeping only the oldest one for each user-station combination
        DB::statement("
            DELETE a1 FROM agents a1
            INNER JOIN agents a2
            WHERE a1.id > a2.id
            AND a1.user_id = a2.user_id
            AND a1.polling_station_id = a2.polling_station_id
        ");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // This migration cannot be reversed as we've deleted duplicate data
        // The duplicates would need to be recreated manually if needed
    }
};
