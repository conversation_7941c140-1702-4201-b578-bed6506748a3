<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('agents', function (Blueprint $table) {
            // Add unique constraint to prevent the same user from being assigned
            // as an agent to the same polling station multiple times
            $table->unique(['user_id', 'polling_station_id'], 'agents_user_station_unique');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('agents', function (Blueprint $table) {
            // Drop the unique constraint
            $table->dropUnique('agents_user_station_unique');
        });
    }
};
