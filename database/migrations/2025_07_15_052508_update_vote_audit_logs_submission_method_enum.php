<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement("ALTER TABLE vote_audit_logs MODIFY COLUMN submission_method ENUM('web', 'api', 'manager_portal', 'station_portal') NOT NULL DEFAULT 'web'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        DB::statement("ALTER TABLE vote_audit_logs MODIFY COLUMN submission_method ENUM('web', 'api', 'manager_portal') NOT NULL DEFAULT 'web'");
    }
};
