<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('audit_logs', function (Blueprint $table) {
            $table->id();
            $table->string('action'); // create, update, delete, login, logout, export, etc.
            $table->string('model_type')->nullable(); // User, PollingStation, Vote, etc.
            $table->unsignedBigInteger('model_id')->nullable(); // ID of the affected model
            $table->unsignedBigInteger('user_id')->nullable(); // Who performed the action
            $table->string('user_name')->nullable(); // Name of user who performed action
            $table->string('user_type')->nullable(); // Type of user (admin, manager, agent, etc.)
            $table->json('old_values')->nullable(); // Previous values (for updates)
            $table->json('new_values')->nullable(); // New values (for creates/updates)
            $table->text('description')->nullable(); // Human readable description
            $table->string('ip_address')->nullable(); // IP address of the user
            $table->string('user_agent')->nullable(); // Browser/device info
            $table->string('url')->nullable(); // URL where action was performed
            $table->string('method')->nullable(); // HTTP method (GET, POST, PUT, DELETE)
            $table->json('request_data')->nullable(); // Additional request data
            $table->timestamps();

            // Indexes for better performance
            $table->index(['user_id', 'created_at']);
            $table->index(['model_type', 'model_id']);
            $table->index(['action', 'created_at']);
            $table->index('created_at');

            // Foreign key constraint
            $table->foreign('user_id')->references('id')->on('users')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('audit_logs');
    }
};
