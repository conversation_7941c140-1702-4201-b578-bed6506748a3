<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JavaScript Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.7/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h1>JavaScript Test Page</h1>
        <p>This page tests if the JavaScript functions are working correctly.</p>
        
        <button type="button" class="btn btn-primary" onclick="testShowEvidenceModal()">
            Test showEvidenceModal Function
        </button>
        
        <button type="button" class="btn btn-secondary" onclick="testConsole()">
            Test Console
        </button>
        
        <div id="test-results" class="mt-3"></div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.7/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        function testConsole() {
            console.log('Console test - this should appear in browser console');
            document.getElementById('test-results').innerHTML = '<div class="alert alert-success">Console test executed - check browser console</div>';
        }
        
        function testShowEvidenceModal() {
            // Test if the function exists
            if (typeof showEvidenceModal === 'function') {
                document.getElementById('test-results').innerHTML = '<div class="alert alert-success">showEvidenceModal function exists and is callable</div>';
                console.log('showEvidenceModal function found');
            } else {
                document.getElementById('test-results').innerHTML = '<div class="alert alert-danger">showEvidenceModal function not found</div>';
                console.error('showEvidenceModal function not found');
            }
        }
        
        // Test for syntax errors
        try {
            console.log('JavaScript loaded successfully - no syntax errors detected');
        } catch (error) {
            console.error('JavaScript syntax error:', error);
            document.getElementById('test-results').innerHTML = '<div class="alert alert-danger">JavaScript syntax error: ' + error.message + '</div>';
        }
    </script>
</body>
</html>
