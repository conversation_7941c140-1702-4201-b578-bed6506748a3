<?php

use App\Http\Controllers\AgentController;
use App\Http\Controllers\ApiLogController;
use App\Http\Controllers\CandidateController;
use App\Http\Controllers\EveidenceController;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\PollingStationController;
use App\Http\Controllers\PositionController;
use App\Http\Controllers\RoleController;
use App\Http\Controllers\SpoiledVoteController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\UserManagementController;
use App\Http\Controllers\VoteController;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;
 

Route::get('/', function () {

    if(Auth::guest())

        return view('auth.login');

    // Redirect users based on their type
    $userType = Auth::user()->user_type;
    switch ($userType) {
        case 'agent':
            return redirect('agent_dashboard');
        case 'manager':
            return redirect('manager/dashboard');
        default:
            return redirect('home');
    }

});

Auth::routes();

Route::group(['middleware' => ['auth']], function () {

    // Dashboard routes (accessible to all authenticated users with view_dashboard permission)
    Route::middleware(['permission:view_dashboard'])->group(function () {
        Route::get('/home', [HomeController::class, 'index'])->name('home');
        Route::get('/vote-trends-data/{positionId?}', [HomeController::class, 'getVoteTrendsData'])->name('vote.trends.data');
        Route::get('/api/polling-stations-map-data', [PollingStationController::class, 'getMapData'])->name('polling.stations.map.data');
        Route::get('/api/dashboard-stats', [HomeController::class, 'getDashboardStats'])->name('api.dashboard.stats');
        Route::get('/api/monitoring-data', [\App\Http\Controllers\CandidateMonitoringController::class, 'getMonitoringData'])->name('api.monitoring.data');
        Route::get('/api/station-evidence/{stationId}', [PollingStationController::class, 'getStationEvidence'])->name('api.station.evidence');
    });

    // Position management routes
    Route::middleware(['permission:view_positions'])->group(function () {
        Route::resource('positions', PositionController::class)->except(['create', 'store', 'edit', 'update', 'destroy']);
        Route::get('/ajax_get_candidates/{postion_id}', [PositionController::class, 'ajaxGetCandidates'])->name('ajax_get_candidates');
    });
    Route::middleware(['permission:create_positions'])->group(function () {
        Route::get('positions/create', [PositionController::class, 'create'])->name('positions.create');
        Route::post('positions', [PositionController::class, 'store'])->name('positions.store');
    });
    Route::middleware(['permission:edit_positions'])->group(function () {
        Route::get('positions/{position}/edit', [PositionController::class, 'edit'])->name('positions.edit');
        Route::patch('positions/{position}', [PositionController::class, 'update'])->name('positions.update');
    });
    Route::middleware(['permission:delete_positions'])->group(function () {
        Route::delete('positions/{position}', [PositionController::class, 'destroy'])->name('positions.destroy');
    });

    // Candidate management routes
    Route::middleware(['permission:view_candidates'])->group(function () {
        Route::resource('candidates', CandidateController::class)->except(['create', 'store', 'edit', 'update', 'destroy']);
    });
    Route::middleware(['permission:create_candidates'])->group(function () {
        Route::get('candidates/create', [CandidateController::class, 'create'])->name('candidates.create');
        Route::post('candidates', [CandidateController::class, 'store'])->name('candidates.store');
    });
    Route::middleware(['permission:edit_candidates'])->group(function () {
        Route::get('candidates/{candidate}/edit', [CandidateController::class, 'edit'])->name('candidates.edit');
        Route::patch('candidates/{candidate}', [CandidateController::class, 'update'])->name('candidates.update');
    });
    Route::middleware(['permission:delete_candidates'])->group(function () {
        Route::delete('candidates/{candidate}', [CandidateController::class, 'destroy'])->name('candidates.destroy');
    });

    // Polling station management routes
    Route::middleware(['permission:view_polling_stations'])->group(function () {
        Route::resource('polling_stations', PollingStationController::class)->except(['create', 'store', 'edit', 'update', 'destroy']);
        Route::get('/admin/polling_stations_view', [PollingStationController::class, 'pollingStations'])->name('polling_stations.view');
        Route::get('/polling_villages', [PollingStationController::class, 'pollingVillages'])->name('polling_villages.index');

        // Vote form routes accessible to users with view permissions
        Route::get('/station/{station}/vote-form', [PollingStationController::class, 'showVoteForm'])->name('station.vote-form');
        Route::post('/station/{station}/submit-votes', [PollingStationController::class, 'submitVotes'])->name('station.submit-votes');

        // Import routes for polling stations and agents
        Route::get('/polling_villages/import', [PollingStationController::class, 'showImportForm'])->name('polling_villages.import');
        Route::post('/polling_villages/import', [PollingStationController::class, 'processImport'])->name('polling_villages.import.process');
    });
    Route::middleware(['permission:create_polling_stations'])->group(function () {
        Route::get('polling_stations/create', [PollingStationController::class, 'create'])->name('polling_stations.create');
        Route::post('polling_stations', [PollingStationController::class, 'store'])->name('polling_stations.store');
    });
    Route::middleware(['permission:edit_polling_stations'])->group(function () {
        Route::get('polling_stations/{polling_station}/edit', [PollingStationController::class, 'edit'])->name('polling_stations.edit');
        Route::patch('polling_stations/{polling_station}', [PollingStationController::class, 'update'])->name('polling_stations.update');
    });
    Route::middleware(['permission:delete_polling_stations'])->group(function () {
        Route::delete('polling_stations/{polling_station}', [PollingStationController::class, 'destroy'])->name('polling_stations.destroy');
    });

    // Agent management routes (for polling station managers and admins)
    Route::middleware(['permission:view_users'])->group(function () {
        Route::resource('agents', AgentController::class)->except(['create', 'store', 'edit', 'update', 'destroy']);
    });
    Route::middleware(['permission:create_users'])->group(function () {
        Route::get('agents/create', [AgentController::class, 'create'])->name('agents.create');
        Route::post('agents', [AgentController::class, 'store'])->name('agents.store');
    });
    Route::middleware(['permission:edit_users'])->group(function () {
        Route::get('agents/{agent}/edit', [AgentController::class, 'edit'])->name('agents.edit');
        Route::patch('agents/{agent}', [AgentController::class, 'update'])->name('agents.update');
    });
    Route::middleware(['permission:delete_users'])->group(function () {
        Route::delete('agents/{agent}', [AgentController::class, 'destroy'])->name('agents.destroy');
    });

    // Vote management routes
    Route::middleware(['permission:view_votes'])->group(function () {
        Route::resource('votes', VoteController::class)->except(['create', 'store', 'edit', 'update', 'destroy']);
    });
    Route::middleware(['permission:submit_votes'])->group(function () {
        Route::get('votes/create', [VoteController::class, 'create'])->name('votes.create');
        Route::post('votes', [VoteController::class, 'store'])->name('votes.store');
        Route::post('votes/submit-all', [VoteController::class, 'submitAllVotes'])->name('votes.submit-all');
    });

    // Temporary route without permission check for debugging
    Route::middleware(['auth'])->group(function () {
        Route::get('votes/submit-all-form', [VoteController::class, 'showSubmitAllForm'])->name('votes.submit-all-form');
    });
    Route::middleware(['permission:edit_votes'])->group(function () {
        Route::get('votes/{vote}/edit', [VoteController::class, 'edit'])->name('votes.edit');
        Route::patch('votes/{vote}', [VoteController::class, 'update'])->name('votes.update');
    });
    Route::middleware(['permission:delete_votes'])->group(function () {
        Route::delete('votes/{vote}', [VoteController::class, 'destroy'])->name('votes.destroy');
    });

    // Evidence management routes
    Route::middleware(['permission:upload_evidence'])->group(function () {
        Route::resource('evedence', EveidenceController::class);
    });

    // Spoiled votes management
    Route::middleware(['permission:manage_votes'])->group(function () {
        Route::resource('spoiled_votes', SpoiledVoteController::class);
    });

    // Legacy user routes (keep for backward compatibility)
    Route::middleware(['permission:view_users'])->group(function () {
        Route::resource('users', UserController::class);
    });
    
    // Agent-specific routes
    Route::middleware(['role:agent'])->group(function () {
        Route::get('/agent_dashboard', [HomeController::class, 'agentDashboard'])->name('agent_dashboard');
    });

    // Role diagnostic page and API routes (accessible to all authenticated users)
    Route::get('role-diagnostic-page', function() {
        return view('role-diagnostic');
    })->name('role.diagnostic.page');

    Route::get('role-diagnostic', [App\Http\Controllers\RoleDiagnosticController::class, 'diagnoseAndFix'])->name('role.diagnostic');
    Route::get('role-info', [App\Http\Controllers\RoleDiagnosticController::class, 'getCurrentUserInfo'])->name('role.info');
    Route::post('initialize-roles', [App\Http\Controllers\RoleDiagnosticController::class, 'initializeRoles'])->name('role.initialize');
    Route::post('fix-user-roles', [App\Http\Controllers\RoleDiagnosticController::class, 'fixAllUserRoles'])->name('role.fix-users');

    // Debug route to check current user permissions (accessible to all authenticated users)
    Route::get('debug-permissions', function () {
        $user = auth()->user();
        if (!$user) {
            return response()->json(['error' => 'Not authenticated']);
        }

        return response()->json([
            'user' => [
                'name' => $user->name,
                'phone' => $user->phone_number,
                'user_type' => $user->user_type,
                'is_active' => $user->is_active,
            ],
            'permissions' => [
                'user_type_permissions' => $user->getUserTypePermissions(),
                'has_create_polling_stations' => $user->hasPermission('create_polling_stations'),
                'has_view_polling_stations' => $user->hasPermission('view_polling_stations'),
                'has_view_dashboard' => $user->hasPermission('view_dashboard'),
            ],
            'role' => $user->role ? [
                'name' => $user->role->name,
                'permissions' => $user->role->permissions ?? []
            ] : null
        ]);
    })->name('debug.permissions');


    Route::get('/ajax_get_candidates/{postion_id}', [PositionController::class, 'ajaxGetCandidates'])->name('ajax_get_candidates');

    // Test route for flash messages (can be removed in production)
    Route::get('/test-flash/{type}', function($type) {
        $messages = [
            'error' => 'This is a test error message!',
            'success' => 'This is a test success message!',
            'warning' => 'This is a test warning message!',
            'info' => 'This is a test info message!'
        ];

        $message = $messages[$type] ?? 'Unknown message type';
        return redirect()->back()->with($type, $message);
    })->name('test.flash');

    // Polling Manager Routes
    Route::middleware(['role:polling_station_manager'])->prefix('manager')->name('manager.')->group(function () {
        Route::get('/dashboard', [App\Http\Controllers\PollingManagerController::class, 'dashboard'])->name('dashboard');
        Route::get('/dashboard/export', [App\Http\Controllers\PollingManagerController::class, 'exportToExcel'])->name('dashboard.export');
        Route::get('/station/{station}/vote-form', [App\Http\Controllers\PollingManagerController::class, 'showVoteForm'])->name('vote-form');
        Route::post('/station/{station}/submit-votes', [App\Http\Controllers\PollingManagerController::class, 'submitVotes'])->name('submit-votes');
        Route::get('/station/{station}/evidence-form', [App\Http\Controllers\PollingManagerController::class, 'showEvidenceForm'])->name('evidence-form');
        Route::post('/station/{station}/upload-evidence', [App\Http\Controllers\PollingManagerController::class, 'uploadEvidence'])->name('upload-evidence');
        Route::post('/station/{station}/submit-spoiled-votes', [App\Http\Controllers\PollingManagerController::class, 'submitSpoiledVotes'])->name('submit-spoiled-votes');
        Route::get('/station/{station}/details', [App\Http\Controllers\PollingManagerController::class, 'getStationDetails'])->name('station-details');

        // Cascading filter API endpoints
        Route::get('/api/parishes-by-subcounty', [App\Http\Controllers\PollingManagerController::class, 'getParishesBySubcounty'])->name('api.parishes-by-subcounty');
        Route::get('/api/villages-by-parish', [App\Http\Controllers\PollingManagerController::class, 'getVillagesByParish'])->name('api.villages-by-parish');
    });
    
    // Candidate Monitoring Routes
    Route::get('/monitoring', [\App\Http\Controllers\CandidateMonitoringController::class, 'index'])->name('monitoring.index');
    Route::post('/monitoring/add/{candidate}', [\App\Http\Controllers\CandidateMonitoringController::class, 'addPreferred'])->name('monitoring.add');
    Route::delete('/monitoring/remove/{candidate}', [\App\Http\Controllers\CandidateMonitoringController::class, 'removePreferred'])->name('monitoring.remove');
    Route::post('/monitoring/toggle/{candidate}', [\App\Http\Controllers\CandidateMonitoringController::class, 'togglePreferred'])->name('monitoring.toggle');
    Route::post('/monitoring/settings/{candidate}', [\App\Http\Controllers\CandidateMonitoringController::class, 'updateSettings'])->name('monitoring.settings');
    Route::get('/monitoring/compare/{candidate}', [\App\Http\Controllers\CandidateMonitoringController::class, 'compare'])->name('monitoring.compare');
    Route::get('/monitoring/guide', function() { return view('monitoring.guide'); })->name('monitoring.guide');
    
    // Notification routes
    Route::get('/notifications', [App\Http\Controllers\NotificationController::class, 'index'])->name('notifications.index');
    Route::get('/notifications/count', [App\Http\Controllers\NotificationController::class, 'getCount'])->name('notifications.count');
    Route::post('/notifications/{id}/read', [App\Http\Controllers\NotificationController::class, 'markAsRead'])->name('notifications.read');
    Route::post('/notifications/read-all', [App\Http\Controllers\NotificationController::class, 'markAllAsRead'])->name('notifications.read-all');

    // API routes for real-time updates
    Route::prefix('api')->name('api.')->group(function () {
        Route::get('/vote-updates', [App\Http\Controllers\HomeController::class, 'getVoteUpdates'])->name('vote-updates');
    });

    // User Management Routes (Admin only)
    Route::prefix('admin')->name('admin.')->middleware(['permission:view_users'])->group(function () {
        Route::resource('users', UserManagementController::class);
        Route::get('users/export', [UserManagementController::class, 'export'])->name('users.export');
        Route::patch('users/{user}/toggle-status', [UserManagementController::class, 'toggleStatus'])->name('users.toggle-status');
        Route::post('users/{user}/assign-roles', [UserManagementController::class, 'assignRoles'])->name('users.assign-roles');
        Route::post('users/bulk-action', [UserManagementController::class, 'bulkAction'])->name('users.bulk-action');

        // Role Management Routes
        Route::resource('roles', RoleController::class);

        // System Audit Log Routes
        Route::prefix('audit')->name('audit.')->group(function () {
            Route::get('/', [App\Http\Controllers\Admin\AuditController::class, 'index'])->name('index');
            Route::get('/system/{auditLog}', [App\Http\Controllers\Admin\AuditController::class, 'show'])->name('show');

            // Vote Audit Routes
            Route::get('/activity', [App\Http\Controllers\VoteAuditController::class, 'agentActivity'])->name('activity');
            Route::get('/flagged', [App\Http\Controllers\VoteAuditController::class, 'flagged'])->name('flagged');
            Route::get('/unverified', [App\Http\Controllers\VoteAuditController::class, 'unverified'])->name('unverified');
            Route::get('/statistics', [App\Http\Controllers\VoteAuditController::class, 'statistics'])->name('statistics');
            Route::get('/agent/{agent}', [App\Http\Controllers\VoteAuditController::class, 'agentAudit'])->name('agent');
            Route::get('/agent/{agent}/performance', [App\Http\Controllers\VoteAuditController::class, 'agentPerformance'])->name('agent.performance');
            Route::get('/station/{station}', [App\Http\Controllers\VoteAuditController::class, 'stationAudit'])->name('station');
            Route::post('/verify/{auditLog}', [App\Http\Controllers\VoteAuditController::class, 'verify'])->name('verify');
            Route::post('/flag/{auditLog}', [App\Http\Controllers\VoteAuditController::class, 'flag'])->name('flag');
            Route::post('/unflag/{auditLog}', [App\Http\Controllers\VoteAuditController::class, 'unflag'])->name('unflag');
            Route::get('/export', [App\Http\Controllers\VoteAuditController::class, 'export'])->name('export');
            Route::get('/api/data', [App\Http\Controllers\VoteAuditController::class, 'apiData'])->name('api.data');
            Route::get('/api/activity', [App\Http\Controllers\VoteAuditController::class, 'apiActivity'])->name('api.activity');
        });

        // Permissions Info Route
        Route::get('permissions-info', function () {
            return view('admin.permissions-info');
        })->name('permissions.info');

        // API Logs routes
        Route::prefix('api-logs')->name('api-logs.')->group(function () {
            Route::get('/', [ApiLogController::class, 'index'])->name('index');
            Route::get('/{apiLog}', [ApiLogController::class, 'show'])->name('show');
            Route::post('/{apiLog}/flag', [ApiLogController::class, 'flag'])->name('flag');
            Route::post('/{apiLog}/unflag', [ApiLogController::class, 'unflag'])->name('unflag');
            Route::post('/bulk-action', [ApiLogController::class, 'bulkAction'])->name('bulk-action');
            Route::get('/export/json', [ApiLogController::class, 'export'])->name('export');
            Route::get('/api/stats', [ApiLogController::class, 'stats'])->name('api.stats');
            Route::get('/api/activity', [ApiLogController::class, 'activity'])->name('api.activity');
        });
    });

    // Reports routes (accessible to managers and admins)
    Route::middleware(['permission:view_dashboard'])->group(function () {
        Route::get('/reports/polling-station-submission', [App\Http\Controllers\ReportController::class, 'pollingStationSubmissionReport'])->name('reports.polling-station-submission');
        Route::get('/reports/polling-station-submission/export', [App\Http\Controllers\ReportController::class, 'exportSubmissionReport'])->name('reports.polling-station-submission.export');
        Route::get('/api/reports/location-details', [App\Http\Controllers\ReportController::class, 'getLocationDetails'])->name('api.reports.location-details');
        Route::get('/api/reports/tree-child-data', [App\Http\Controllers\ReportController::class, 'getTreeChildData'])->name('api.reports.tree-child-data');
    });

});

// Test route for debugging (remove in production)
Route::get('/test-vote-form/{station}', function($station) {
    return "Vote form test for station: $station. User: " . (auth()->check() ? auth()->user()->name : 'Not authenticated');
})->name('test.vote-form');
