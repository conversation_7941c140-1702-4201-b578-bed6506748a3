@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Header Section -->
            <div class="report-header">
                <div class="header-content">
                    <div class="header-text">
                        <div class="header-icon">
                            <i class="bi bi-clipboard-data"></i>
                        </div>
                        <div>
                            <h1 class="header-title">Polling Station Submission Report</h1>
                            <p class="header-subtitle">Comprehensive analysis of vote submission status across all administrative levels</p>
                        </div>
                    </div>
                    <div class="header-actions">
                        <button class="btn btn-outline-light btn-action" onclick="exportReport()">
                            <i class="bi bi-download"></i>
                            <span>Export</span>
                        </button>
                        <button class="btn btn-light btn-action" onclick="refreshReport()">
                            <i class="bi bi-arrow-clockwise"></i>
                            <span>Refresh</span>
                        </button>
                    </div>
                </div>

                <!-- Filters Section -->
                <div class="filters-card">
                    <div class="filters-header">
                        <h5 class="filters-title">
                            <i class="bi bi-funnel-fill"></i>
                            Filter Options
                        </h5>
                        <small class="text-muted">Customize your report view</small>
                    </div>
                    <div class="filters-body">
                        <form method="GET" action="{{ route('reports.polling-station-submission') }}" class="filters-form">
                            <div class="filter-row">
                                <div class="filter-group">
                                    <label class="filter-label">
                                        <i class="bi bi-geo-alt"></i>
                                        District
                                    </label>
                                    <select name="district" class="filter-select">
                                        <option value="">All Districts</option>
                                        @foreach($filterOptions['districts'] as $district)
                                            <option value="{{ $district }}" {{ $districtFilter == $district ? 'selected' : '' }}>
                                                {{ $district }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                                <div class="filter-group">
                                    <label class="filter-label">
                                        <i class="bi bi-geo"></i>
                                        County
                                    </label>
                                    <select name="county" class="filter-select">
                                        <option value="">All Counties</option>
                                        @foreach($filterOptions['counties'] as $county)
                                            <option value="{{ $county }}" {{ $countyFilter == $county ? 'selected' : '' }}>
                                                {{ $county }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                                <div class="filter-group">
                                    <label class="filter-label">
                                        <i class="bi bi-house-door"></i>
                                        Subcounty
                                    </label>
                                    <select name="subcounty" class="filter-select">
                                        <option value="">All Subcounties</option>
                                        @foreach($filterOptions['subcounties'] as $subcounty)
                                            <option value="{{ $subcounty }}" {{ $subcountyFilter == $subcounty ? 'selected' : '' }}>
                                                {{ $subcounty }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                                <div class="filter-group">
                                    <label class="filter-label">
                                        <i class="bi bi-building"></i>
                                        Parish
                                    </label>
                                    <select name="parish" class="filter-select">
                                        <option value="">All Parishes</option>
                                        @foreach($filterOptions['parishes'] as $parish)
                                            <option value="{{ $parish }}" {{ $parishFilter == $parish ? 'selected' : '' }}>
                                                {{ $parish }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>

                            <div class="filter-row">
                                <div class="filter-group">
                                    <label class="filter-label">
                                        <i class="bi bi-calendar-event"></i>
                                        Date From
                                    </label>
                                    <input type="date" name="date_from" class="filter-input" value="{{ $dateFrom }}">
                                </div>
                                <div class="filter-group">
                                    <label class="filter-label">
                                        <i class="bi bi-calendar-check"></i>
                                        Date To
                                    </label>
                                    <input type="date" name="date_to" class="filter-input" value="{{ $dateTo }}">
                                </div>
                                <div class="filter-group">
                                    <label class="filter-label">
                                        <i class="bi bi-list-ol"></i>
                                        Items Per Page
                                    </label>
                                    <select name="per_page" class="filter-select">
                                        <option value="10" {{ $perPage == 10 ? 'selected' : '' }}>10</option>
                                        <option value="25" {{ $perPage == 25 ? 'selected' : '' }}>25</option>
                                        <option value="50" {{ $perPage == 50 ? 'selected' : '' }}>50</option>
                                        <option value="100" {{ $perPage == 100 ? 'selected' : '' }}>100</option>
                                    </select>
                                </div>
                                <div class="filter-group filter-actions">
                                    <button type="submit" class="btn btn-primary btn-filter">
                                        <i class="bi bi-search"></i>
                                        <span>Apply Filters</span>
                                    </button>
                                    <a href="{{ route('reports.polling-station-submission') }}" class="btn btn-outline-secondary btn-filter">
                                        <i class="bi bi-arrow-clockwise"></i>
                                        <span>Reset</span>
                                    </a>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Summary Statistics -->
                <div class="stats-grid">
                    <div class="stat-card stat-primary">
                        <div class="stat-icon">
                            <i class="bi bi-building-fill"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-number">{{ number_format($summaryStats['total_stations']) }}</div>
                            <div class="stat-label">Total Stations</div>
                        </div>
                        <div class="stat-trend">
                            <i class="bi bi-arrow-up"></i>
                        </div>
                    </div>

                    <div class="stat-card stat-success">
                        <div class="stat-icon">
                            <i class="bi bi-check-circle-fill"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-number">{{ number_format($summaryStats['submitted_stations']) }}</div>
                            <div class="stat-label">Submitted</div>
                        </div>
                        <div class="stat-trend">
                            <i class="bi bi-check-lg"></i>
                        </div>
                    </div>

                    <div class="stat-card stat-warning">
                        <div class="stat-icon">
                            <i class="bi bi-clock-fill"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-number">{{ number_format($summaryStats['pending_stations']) }}</div>
                            <div class="stat-label">Pending</div>
                        </div>
                        <div class="stat-trend">
                            <i class="bi bi-hourglass-split"></i>
                        </div>
                    </div>

                    <div class="stat-card stat-info">
                        <div class="stat-icon">
                            <i class="bi bi-graph-up-arrow"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-number">{{ $summaryStats['completion_percentage'] }}%</div>
                            <div class="stat-label">Completion Rate</div>
                        </div>
                        <div class="stat-progress">
                            <div class="progress-circle" data-percentage="{{ $summaryStats['completion_percentage'] }}">
                                <svg viewBox="0 0 36 36">
                                    <path class="circle-bg" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"/>
                                    <path class="circle" stroke-dasharray="{{ $summaryStats['completion_percentage'] }}, 100" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"/>
                                </svg>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Additional Summary Info -->
                <div class="summary-cards">
                    <div class="summary-card">
                        <div class="summary-header">
                            <div class="summary-icon bg-gradient-primary">
                                <i class="bi bi-people-fill"></i>
                            </div>
                            <h6 class="summary-title">Top Contributors</h6>
                        </div>
                        <div class="summary-content">
                            @if(!empty($summaryStats['top_contributors']))
                                @foreach($summaryStats['top_contributors'] as $index => $contributor)
                                    <div class="contributor-item">
                                        <div class="contributor-rank">{{ $index + 1 }}</div>
                                        <div class="contributor-info">
                                            <div class="contributor-name">{{ $contributor['name'] }}</div>
                                            <div class="contributor-meta">
                                                <span class="contributor-type">{{ ucfirst($contributor['type']) }}</span>
                                                <span class="contributor-count">{{ $contributor['submission_count'] }} submissions</span>
                                            </div>
                                        </div>
                                        <div class="contributor-badge">
                                            <i class="bi bi-trophy-fill"></i>
                                        </div>
                                    </div>
                                @endforeach
                            @else
                                <div class="empty-state">
                                    <i class="bi bi-inbox"></i>
                                    <p>No submissions yet</p>
                                </div>
                            @endif
                        </div>
                    </div>

                    <div class="summary-card">
                        <div class="summary-header">
                            <div class="summary-icon bg-gradient-success">
                                <i class="bi bi-clock-history"></i>
                            </div>
                            <h6 class="summary-title">Recent Activity</h6>
                        </div>
                        <div class="summary-content">
                            @if(!empty($summaryStats['recent_submissions']))
                                @foreach($summaryStats['recent_submissions'] as $submission)
                                    <div class="activity-item">
                                        <div class="activity-icon">
                                            <i class="bi bi-check-circle-fill"></i>
                                        </div>
                                        <div class="activity-info">
                                            <div class="activity-title">{{ $submission['station_name'] }}</div>
                                            <div class="activity-meta">
                                                <span class="activity-user">by {{ $submission['user_name'] }}</span>
                                                <span class="activity-time">{{ \Carbon\Carbon::parse($submission['submission_time'])->diffForHumans() }}</span>
                                            </div>
                                        </div>
                                        <div class="activity-timestamp">
                                            {{ \Carbon\Carbon::parse($submission['submission_time'])->format('H:i') }}
                                        </div>
                                    </div>
                                @endforeach
                            @else
                                <div class="empty-state">
                                    <i class="bi bi-clock"></i>
                                    <p>No recent activity</p>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>

            <!-- Report Content -->
            <div class="report-content">
                @if(empty($reportData['data']))
                    <div class="card">
                        <div class="card-body text-center py-5">
                            <i class="bi bi-inbox display-1 text-muted"></i>
                            <h4 class="mt-3">No Data Available</h4>
                            <p class="text-muted">No polling stations found matching the selected criteria.</p>
                        </div>
                    </div>
                @else
                    <div class="report-table-card">
                        <div class="table-header">
                            <div class="table-title-section">
                                <div class="table-icon">
                                    <i class="bi bi-diagram-3-fill"></i>
                                </div>
                                <div class="table-title-content">
                                    <h5 class="table-title">Hierarchical Submission Report</h5>
                                    <p class="table-subtitle">Interactive tree view of submission status</p>
                                </div>
                            </div>
                            <div class="table-controls">
                                <div class="control-group">
                                    <button class="btn btn-control btn-expand" onclick="expandAllRows()" title="Expand All (Ctrl+E)">
                                        <i class="bi bi-arrows-expand"></i>
                                        <span>Expand All</span>
                                    </button>
                                    <button class="btn btn-control btn-collapse" onclick="collapseAllRows()" title="Collapse All (Ctrl+C)">
                                        <i class="bi bi-arrows-collapse"></i>
                                        <span>Collapse All</span>
                                    </button>
                                </div>
                                <div class="control-group">
                                    <button class="btn btn-control btn-test" onclick="testModal()" title="Test Modal">
                                        <i class="bi bi-gear"></i>
                                        <span>Test</span>
                                    </button>
                                    <button class="btn btn-control btn-debug" onclick="debugTree()" title="Debug Info">
                                        <i class="bi bi-bug"></i>
                                        <span>Debug</span>
                                    </button>
                                </div>
                                <div class="table-stats">
                                    <span class="stats-badge">
                                        <i class="bi bi-geo-alt"></i>
                                        {{ $reportData['pagination']->total() }} subcounties
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="table-container">
                            <div class="table-wrapper">
                                <table class="enhanced-tree-table" id="hierarchicalTable">
                                    <thead class="table-header-enhanced">
                                        <tr>
                                            <th class="col-location">
                                                <div class="header-content">
                                                    <i class="bi bi-geo-alt-fill"></i>
                                                    <span>Location Name</span>
                                                </div>
                                            </th>
                                            <th class="col-total">
                                                <div class="header-content">
                                                    <i class="bi bi-building"></i>
                                                    <span>Total</span>
                                                </div>
                                            </th>
                                            <th class="col-submitted">
                                                <div class="header-content">
                                                    <i class="bi bi-check-circle"></i>
                                                    <span>Submitted</span>
                                                </div>
                                            </th>
                                            <th class="col-pending">
                                                <div class="header-content">
                                                    <i class="bi bi-clock"></i>
                                                    <span>Pending</span>
                                                </div>
                                            </th>
                                            <th class="col-completion">
                                                <div class="header-content">
                                                    <i class="bi bi-graph-up"></i>
                                                    <span>Completion</span>
                                                </div>
                                            </th>
                                            <th class="col-actions">
                                                <div class="header-content">
                                                    <i class="bi bi-gear"></i>
                                                    <span>Actions</span>
                                                </div>
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody class="tree-table-body" id="treeTableBody">
                                        @foreach($reportData['data'] as $subcounty)
                                            <tr class="tree-row enhanced-row level-1"
                                                data-id="{{ $subcounty['id'] }}"
                                                data-level="1"
                                                data-type="{{ $subcounty['type'] }}"
                                                data-name="{{ $subcounty['name'] }}"
                                                data-has-children="{{ $subcounty['has_children'] ? 'true' : 'false' }}"
                                                data-expanded="false">
                                                <td class="tree-cell enhanced-cell">
                                                    <div class="tree-content level-1">
                                                        @if($subcounty['has_children'])
                                                            <button class="tree-toggle enhanced-toggle" onclick="toggleTreeRow('{{ $subcounty['id'] }}')">
                                                                <i class="bi bi-chevron-right"></i>
                                                            </button>
                                                        @else
                                                            <span class="tree-spacer"></span>
                                                        @endif
                                                        <div class="location-info">
                                                            <div class="location-icon subcounty-icon">
                                                                <i class="bi bi-geo-alt-fill"></i>
                                                            </div>
                                                            <div class="location-details">
                                                                <div class="location-name">{{ $subcounty['name'] }}</div>
                                                                <div class="location-type">Subcounty</div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td class="stat-cell">
                                                    <div class="stat-value total-stat">
                                                        <span class="stat-number">{{ $subcounty['total_stations'] }}</span>
                                                        <span class="stat-label">stations</span>
                                                    </div>
                                                </td>
                                                <td class="stat-cell">
                                                    <div class="stat-value success-stat">
                                                        <span class="stat-number">{{ $subcounty['submitted_stations'] }}</span>
                                                        <span class="stat-label">done</span>
                                                    </div>
                                                </td>
                                                <td class="stat-cell">
                                                    <div class="stat-value warning-stat">
                                                        <span class="stat-number">{{ $subcounty['total_stations'] - $subcounty['submitted_stations'] }}</span>
                                                        <span class="stat-label">pending</span>
                                                    </div>
                                                </td>
                                                <td class="completion-cell">
                                                    <div class="completion-display">
                                                        <div class="progress-ring">
                                                            <svg class="progress-ring-svg" width="40" height="40">
                                                                <circle class="progress-ring-circle-bg" cx="20" cy="20" r="15"></circle>
                                                                <circle class="progress-ring-circle progress-{{ $subcounty['percentage'] >= 75 ? 'high' : ($subcounty['percentage'] >= 50 ? 'medium' : 'low') }}"
                                                                        cx="20" cy="20" r="15"
                                                                        stroke-dasharray="{{ 94.2 * $subcounty['percentage'] / 100 }} 94.2"></circle>
                                                            </svg>
                                                            <div class="progress-text">{{ $subcounty['percentage'] }}%</div>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td class="actions-cell">
                                                    <button class="btn btn-action btn-details"
                                                            onclick="showLocationDetails('subcounty', '{{ $subcounty['name'] }}', '', '')"
                                                            title="View Details">
                                                        <i class="bi bi-eye-fill"></i>
                                                        <span>Details</span>
                                                    </button>
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div class="table-footer">
                            <div class="footer-content">
                                <div class="pagination-info">
                                    <i class="bi bi-info-circle"></i>
                                    <span>
                                        Showing {{ $reportData['pagination']->firstItem() ?? 0 }} to {{ $reportData['pagination']->lastItem() ?? 0 }}
                                        of {{ $reportData['pagination']->total() }} subcounties
                                    </span>
                                </div>
                                <div class="pagination-controls">
                                    {{ $reportData['pagination']->appends(request()->query())->links() }}
                                </div>
                            </div>
                        </div>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>

<!-- Enhanced Details Modal -->
<div class="modal fade enhanced-modal" id="detailsModal" tabindex="-1" aria-labelledby="detailsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content enhanced-modal-content">
            <div class="modal-header enhanced-modal-header">
                <div class="modal-title-section">
                    <div class="modal-icon">
                        <i class="bi bi-info-circle-fill"></i>
                    </div>
                    <div class="modal-title-content">
                        <h5 class="modal-title" id="detailsModalLabel">Details</h5>
                        <p class="modal-subtitle">Detailed information and statistics</p>
                    </div>
                </div>
                <button type="button" class="btn-close enhanced-close" data-bs-dismiss="modal" aria-label="Close">
                    <i class="bi bi-x-lg"></i>
                </button>
            </div>
            <div class="modal-body enhanced-modal-body" id="modalContent">
                <div class="loading-state">
                    <div class="loading-spinner">
                        <div class="spinner"></div>
                    </div>
                    <p class="loading-text">Loading details...</p>
                </div>
            </div>
            <div class="modal-footer enhanced-modal-footer">
                <button type="button" class="btn btn-modal-close" data-bs-dismiss="modal">
                    <i class="bi bi-x-circle"></i>
                    <span>Close</span>
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Inline JavaScript to ensure functions are available immediately -->
<script>
// Global variables for tree state management
window.expandedRows = new Set();
window.loadedChildren = new Map();

// Make functions globally accessible
window.exportReport = function() {
    const urlParams = new URLSearchParams(window.location.search);
    const exportUrl = '{{ route("reports.polling-station-submission.export") }}?' + urlParams.toString();
    window.open(exportUrl, '_blank');
};

window.refreshReport = function() {
    window.location.reload();
};

window.expandAllRows = function() {
    console.log('Expanding all rows');
    const level1Rows = document.querySelectorAll('.tree-row.level-1[data-has-children="true"]');
    console.log('Found level 1 rows with children:', level1Rows.length);

    level1Rows.forEach((row, index) => {
        console.log(`Processing row ${index + 1}:`, row.dataset.id, row.dataset.expanded);
        if (row.dataset.expanded === 'false') {
            setTimeout(() => {
                window.toggleTreeRow(row.dataset.id);
            }, index * 100);
        }
    });
};

window.collapseAllRows = function() {
    console.log('Collapsing all rows');
    const expandedRows = document.querySelectorAll('.tree-row[data-expanded="true"]');
    console.log('Found expanded rows:', expandedRows.length);

    expandedRows.forEach((row, index) => {
        console.log(`Collapsing row ${index + 1}:`, row.dataset.id);
        setTimeout(() => {
            window.toggleTreeRow(row.dataset.id);
        }, index * 50);
    });
};

window.testModal = function() {
    console.log('Testing modal functionality');
    window.showLocationDetails('subcounty', 'Test Subcounty', '', '');
};

window.debugTree = function() {
    console.log('=== TREE DEBUG INFO ===');
    console.log('Expanded rows:', Array.from(window.expandedRows));
    console.log('Loaded children:', Array.from(window.loadedChildren.keys()));

    const allRows = document.querySelectorAll('.tree-row');
    console.log('Total rows:', allRows.length);

    const level1Rows = document.querySelectorAll('.tree-row.level-1');
    console.log('Level 1 rows:', level1Rows.length);

    level1Rows.forEach((row, index) => {
        console.log(`Level 1 Row ${index + 1}:`, {
            id: row.dataset.id,
            name: row.dataset.name,
            hasChildren: row.dataset.hasChildren,
            expanded: row.dataset.expanded
        });
    });

    const firstToggle = document.querySelector('.tree-toggle');
    if (firstToggle) {
        console.log('First toggle button found:', firstToggle);
        console.log('First toggle onclick:', firstToggle.getAttribute('onclick'));
    } else {
        console.log('No toggle buttons found');
    }

    const modal = document.getElementById('detailsModal');
    const modalTitle = document.getElementById('detailsModalLabel');
    const modalContent = document.getElementById('modalContent');

    console.log('Modal elements:', {
        modal: !!modal,
        modalTitle: !!modalTitle,
        modalContent: !!modalContent
    });

    console.log('Bootstrap available:', typeof bootstrap !== 'undefined');
    if (typeof bootstrap !== 'undefined') {
        console.log('Bootstrap Modal class:', typeof bootstrap.Modal);
    }

    console.log('=== END DEBUG INFO ===');
};

window.toggleTreeRow = function(rowId) {
    console.log('Toggling row:', rowId);

    const row = document.querySelector(`tr[data-id="${rowId}"]`);
    if (!row) {
        console.error('Row not found:', rowId);
        return;
    }

    const toggleBtn = row.querySelector('.tree-toggle');
    if (!toggleBtn) {
        console.error('Toggle button not found for row:', rowId);
        return;
    }

    const icon = toggleBtn.querySelector('i');
    if (!icon) {
        console.error('Icon not found for row:', rowId);
        return;
    }

    const isExpanded = row.dataset.expanded === 'true';
    console.log('Current expanded state:', isExpanded);

    if (isExpanded) {
        window.collapseRow(rowId);
        row.dataset.expanded = 'false';
        icon.className = 'bi bi-chevron-right';
        toggleBtn.classList.remove('expanded');
        window.expandedRows.delete(rowId);
        console.log('Row collapsed:', rowId);
    } else {
        window.expandRow(rowId);
        row.dataset.expanded = 'true';
        icon.className = 'bi bi-chevron-down';
        toggleBtn.classList.add('expanded');
        window.expandedRows.add(rowId);
        console.log('Row expanded:', rowId);
    }
};

window.showLocationDetails = function(level, name, parentName = '', grandparentName = '') {
    console.log('Showing location details for:', level, name, parentName, grandparentName);

    const modalElement = document.getElementById('detailsModal');
    const modalTitle = document.getElementById('detailsModalLabel');
    const modalContent = document.getElementById('modalContent');

    if (!modalElement || !modalTitle || !modalContent) {
        console.error('Modal elements not found');
        return;
    }

    modalTitle.innerHTML = `<i class="bi bi-info-circle"></i> ${level.charAt(0).toUpperCase() + level.slice(1)} Details: ${name}`;

    modalContent.innerHTML = `
        <div class="text-center py-4">
            <div class="spinner-border" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-2">Loading details...</p>
        </div>
    `;

    try {
        if (typeof bootstrap !== 'undefined' && bootstrap.Modal) {
            const modal = new bootstrap.Modal(modalElement);
            modal.show();
            console.log('Modal shown using Bootstrap 5 Modal class');
        } else if (typeof $ !== 'undefined') {
            $(modalElement).modal('show');
            console.log('Modal shown using jQuery');
        } else {
            window.showModalManually(modalElement);
        }
    } catch (error) {
        console.error('Error showing modal:', error);
        return;
    }

    const urlParams = new URLSearchParams(window.location.search);
    urlParams.set('level', level);
    urlParams.set('name', name);
    if (parentName) urlParams.set('parent_name', parentName);
    if (grandparentName) urlParams.set('grandparent_name', grandparentName);

    const apiUrl = '{{ route("api.reports.location-details") }}?' + urlParams.toString();
    console.log('Fetching from:', apiUrl);

    fetch(apiUrl)
        .then(response => {
            console.log('Response status:', response.status);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('Received data:', data);
            modalContent.innerHTML = window.buildLocationDetailsHTML(data);
        })
        .catch(error => {
            console.error('Error fetching location details:', error);
            modalContent.innerHTML = `
                <div class="alert alert-danger">
                    <i class="bi bi-exclamation-triangle"></i>
                    Error loading details: ${error.message}
                    <br><small>Please check the console for more details.</small>
                </div>
            `;
        });
};

window.showModalManually = function(modalElement) {
    modalElement.classList.add('show');
    modalElement.style.display = 'block';
    modalElement.setAttribute('aria-hidden', 'false');
    document.body.classList.add('modal-open');

    let backdrop = document.getElementById('modal-backdrop');
    if (!backdrop) {
        backdrop = document.createElement('div');
        backdrop.className = 'modal-backdrop fade show';
        backdrop.id = 'modal-backdrop';
        document.body.appendChild(backdrop);
    }

    console.log('Modal shown manually');
};

window.hideModalManually = function() {
    const modalElement = document.getElementById('detailsModal');
    const backdrop = document.getElementById('modal-backdrop');

    if (modalElement) {
        modalElement.classList.remove('show');
        modalElement.style.display = 'none';
        modalElement.setAttribute('aria-hidden', 'true');
    }

    if (backdrop) {
        backdrop.remove();
    }

    document.body.classList.remove('modal-open');
    console.log('Modal hidden manually');
};

window.expandRow = function(rowId) {
    console.log('Expanding row:', rowId);

    const row = document.querySelector(`tr[data-id="${rowId}"]`);
    if (!row) {
        console.error('Row not found for expansion:', rowId);
        return;
    }

    const rowType = row.dataset.type;
    const rowName = row.dataset.name;

    console.log('Row details:', { type: rowType, name: rowName });

    if (window.loadedChildren.has(rowId)) {
        console.log('Children already loaded, showing existing children');
        window.showChildRows(rowId);
        return;
    }

    window.showLoadingState(row);

    const urlParams = new URLSearchParams(window.location.search);
    urlParams.set('parent_id', rowId);
    urlParams.set('parent_type', rowType);
    urlParams.set('parent_name', rowName);

    if (rowType === 'parish') {
        const subcountyRow = window.findParentRow(row, 'subcounty');
        if (subcountyRow) {
            urlParams.set('grandparent_name', subcountyRow.dataset.name);
            console.log('Added grandparent (subcounty):', subcountyRow.dataset.name);
        }
    } else if (rowType === 'village') {
        const parishRow = window.findParentRow(row, 'parish');
        const subcountyRow = window.findParentRow(row, 'subcounty');
        if (parishRow) {
            urlParams.set('grandparent_name', parishRow.dataset.name);
            console.log('Added grandparent (parish):', parishRow.dataset.name);
        }
        if (subcountyRow) {
            urlParams.set('great_grandparent_name', subcountyRow.dataset.name);
            console.log('Added great-grandparent (subcounty):', subcountyRow.dataset.name);
        }
    }

    const apiUrl = '{{ route("api.reports.tree-child-data") }}?' + urlParams.toString();
    console.log('Fetching child data from:', apiUrl);

    fetch(apiUrl)
        .then(response => {
            console.log('API response status:', response.status);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('Received child data:', data);
            window.hideLoadingState(row);

            if (data.children && data.children.length > 0) {
                window.insertChildRows(rowId, data.children);
                window.loadedChildren.set(rowId, data.children);
                console.log('Children inserted and cached for row:', rowId);
            } else {
                console.log('No children found for row:', rowId);
                row.dataset.hasChildren = 'false';
                const toggleBtn = row.querySelector('.tree-toggle');
                if (toggleBtn) {
                    toggleBtn.style.display = 'none';
                }
            }
        })
        .catch(error => {
            console.error('Error loading child data:', error);
            window.hideLoadingState(row);
            window.showErrorState(row);
        });
};

window.collapseRow = function(rowId) {
    window.hideChildRows(rowId);
};

window.showChildRows = function(parentId) {
    const children = window.loadedChildren.get(parentId);
    if (!children) return;

    children.forEach(child => {
        const childRow = document.querySelector(`tr[data-id="${child.id}"]`);
        if (childRow) {
            childRow.classList.remove('hidden');
            childRow.classList.add('expanding');

            if (window.expandedRows.has(child.id)) {
                window.showChildRows(child.id);
            }
        }
    });
};

window.hideChildRows = function(parentId) {
    const children = window.loadedChildren.get(parentId);
    if (!children) return;

    children.forEach(child => {
        const childRow = document.querySelector(`tr[data-id="${child.id}"]`);
        if (childRow) {
            childRow.classList.add('hidden');
            childRow.classList.remove('expanding');

            window.hideChildRows(child.id);
        }
    });
};

window.insertChildRows = function(parentId, children) {
    console.log('Inserting child rows for parent:', parentId, 'Children count:', children.length);

    const parentRow = document.querySelector(`tr[data-id="${parentId}"]`);
    if (!parentRow) {
        console.error('Parent row not found:', parentId);
        return;
    }

    const tbody = parentRow.parentNode;
    let insertAfter = parentRow;

    children.forEach((child, index) => {
        console.log(`Processing child ${index + 1}:`, child.id, child.name);

        let existingRow = document.querySelector(`tr[data-id="${child.id}"]`);
        if (existingRow) {
            console.log('Row already exists, showing it:', child.id);
            existingRow.classList.remove('hidden');
            insertAfter = existingRow;
            return;
        }

        try {
            const childRow = window.createTreeRow(child);
            insertAfter.insertAdjacentElement('afterend', childRow);
            insertAfter = childRow;
            console.log('Child row inserted:', child.id);
        } catch (error) {
            console.error('Error creating child row:', error, child);
        }
    });

    console.log('Finished inserting child rows for parent:', parentId);
};

window.createTreeRow = function(data) {
    const row = document.createElement('tr');
    row.className = `tree-row level-${data.level} expanding`;
    row.dataset.id = data.id;
    row.dataset.level = data.level;
    row.dataset.type = data.type;
    row.dataset.name = data.name;
    row.dataset.hasChildren = data.has_children ? 'true' : 'false';
    row.dataset.expanded = 'false';

    let html = '';

    if (data.type === 'station') {
        const statusBadge = data.has_votes ?
            '<span class="badge bg-success">Submitted</span>' :
            '<span class="badge bg-secondary">Pending</span>';

        const lastSubmission = data.last_submission ?
            new Date(data.last_submission).toLocaleDateString() + ' ' + new Date(data.last_submission).toLocaleTimeString() :
            '<span class="text-muted">-</span>';

        const contributors = data.submission_users && data.submission_users.length > 0 ?
            data.submission_users.slice(0, 2).map(user => `<span class="badge bg-light text-dark me-1">${user.name}</span>`).join('') +
            (data.submission_users.length > 2 ? `<span class="text-muted">+${data.submission_users.length - 2}</span>` : '') :
            '<span class="text-muted">None</span>';

        html = `
            <td class="tree-cell">
                <div class="tree-content level-${data.level}">
                    <span class="tree-spacer me-2"></span>
                    <i class="bi bi-building text-muted me-2"></i>
                    ${data.name}
                    <small class="text-muted ms-2">(Station)</small>
                </div>
            </td>
            <td colspan="3">${statusBadge}</td>
            <td><small>${lastSubmission}</small></td>
            <td>
                <button class="btn btn-sm btn-outline-primary" onclick="window.showStationDetails('${data.id}', '${data.name}')">
                    <i class="bi bi-eye"></i>
                </button>
            </td>
        `;
    } else {
        const toggleButton = data.has_children ?
            `<button class="tree-toggle btn btn-sm p-0 me-2" onclick="window.toggleTreeRow('${data.id}')">
                <i class="bi bi-chevron-right"></i>
            </button>` :
            '<span class="tree-spacer me-2"></span>';

        const icon = data.type === 'parish' ? 'bi-geo' :
                    data.type === 'village' ? 'bi-house-door' : 'bi-geo-alt';

        const iconColor = data.type === 'parish' ? 'text-success' :
                         data.type === 'village' ? 'text-warning' : 'text-primary';

        const progressBarColor = data.percentage == 100 ? 'success' :
                               data.percentage >= 75 ? 'warning' : 'danger';

        const badgeColor = data.percentage == 100 ? 'success' :
                          data.percentage >= 75 ? 'warning' : 'danger';

        html = `
            <td class="tree-cell">
                <div class="tree-content level-${data.level}">
                    ${toggleButton}
                    <i class="bi ${icon} ${iconColor} me-2"></i>
                    <strong>${data.name}</strong>
                    <small class="text-muted ms-2">(${data.type.charAt(0).toUpperCase() + data.type.slice(1)})</small>
                </div>
            </td>
            <td>
                <span class="badge bg-primary">${data.total_stations}</span>
            </td>
            <td>
                <span class="badge bg-success">${data.submitted_stations}</span>
            </td>
            <td>
                <span class="badge bg-warning">${data.total_stations - data.submitted_stations}</span>
            </td>
            <td>
                <div class="d-flex align-items-center">
                    <div class="progress me-2" style="width: 60px; height: 8px;">
                        <div class="progress-bar bg-${progressBarColor}" style="width: ${data.percentage}%"></div>
                    </div>
                    <span class="badge bg-${badgeColor}">${data.percentage}%</span>
                </div>
            </td>
            <td>
                <button class="btn btn-sm btn-outline-primary" onclick="window.showLocationDetails('${data.type}', '${data.name}', '', '')">
                    <i class="bi bi-eye"></i>
                </button>
            </td>
        `;
    }

    row.innerHTML = html;
    return row;
};

// Helper functions
window.findParentRow = function(row, parentType) {
    let currentRow = row.previousElementSibling;
    while (currentRow) {
        if (currentRow.dataset.type === parentType) {
            return currentRow;
        }
        currentRow = currentRow.previousElementSibling;
    }
    return null;
};

window.showLoadingState = function(row) {
    const toggleBtn = row.querySelector('.tree-toggle');
    if (toggleBtn) {
        toggleBtn.innerHTML = '<div class="tree-loading-spinner"></div>';
        toggleBtn.disabled = true;
    }
};

window.hideLoadingState = function(row) {
    const toggleBtn = row.querySelector('.tree-toggle');
    if (toggleBtn) {
        toggleBtn.innerHTML = '<i class="bi bi-chevron-down"></i>';
        toggleBtn.disabled = false;
        toggleBtn.classList.add('expanded');
    }
};

window.showErrorState = function(row) {
    const toggleBtn = row.querySelector('.tree-toggle');
    if (toggleBtn) {
        toggleBtn.innerHTML = '<i class="bi bi-exclamation-triangle text-danger"></i>';
        toggleBtn.disabled = false;
    }
};

window.showStationDetails = function(stationId, stationName) {
    console.log('Showing station details for:', stationId, stationName);

    const modalElement = document.getElementById('detailsModal');
    const modalTitle = document.getElementById('detailsModalLabel');
    const modalContent = document.getElementById('modalContent');

    if (!modalElement || !modalTitle || !modalContent) {
        console.error('Modal elements not found');
        return;
    }

    const numericId = stationId.replace('station_', '');

    modalTitle.innerHTML = `<i class="bi bi-building"></i> Station Details: ${stationName}`;

    modalContent.innerHTML = `
        <div class="text-center py-4">
            <div class="spinner-border" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-2">Loading station details...</p>
        </div>
    `;

    try {
        if (typeof bootstrap !== 'undefined' && bootstrap.Modal) {
            const modal = new bootstrap.Modal(modalElement);
            modal.show();
        } else if (typeof $ !== 'undefined') {
            $(modalElement).modal('show');
        } else {
            window.showModalManually(modalElement);
        }
    } catch (error) {
        console.error('Error showing modal:', error);
        return;
    }

    setTimeout(() => {
        modalContent.innerHTML = `
            <div class="alert alert-info">
                <i class="bi bi-info-circle"></i>
                Detailed station information will be available in the next update.
                <br><strong>Station ID:</strong> ${numericId}
                <br><strong>Station Name:</strong> ${stationName}
                <br><br>
                <small class="text-muted">This feature will include vote counts, submission history, and contributor details.</small>
            </div>
        `;
    }, 500);
};

window.buildLocationDetailsHTML = function(data) {
    return `
        <div class="alert alert-success">
            <i class="bi bi-check-circle"></i>
            <strong>Location Details Loaded Successfully</strong>
            <br>Location: ${data.location ? data.location.name : 'Unknown'}
            <br>Total Stations: ${data.location ? data.location.total_stations : 0}
            <br>Submitted: ${data.location ? data.location.submitted_stations : 0}
            <br>Completion: ${data.location ? data.location.percentage : 0}%
            <br><br>
            <small class="text-muted">Full details implementation coming soon...</small>
        </div>
    `;
};

// Initialize modal close handlers when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM Content Loaded - Setting up modal handlers');

    const modalElement = document.getElementById('detailsModal');
    if (modalElement) {
        // Handle close button clicks
        const closeButtons = modalElement.querySelectorAll('[data-bs-dismiss="modal"]');
        closeButtons.forEach(button => {
            button.addEventListener('click', function() {
                console.log('Close button clicked');
                window.hideModalManually();
            });
        });

        // Handle backdrop clicks
        modalElement.addEventListener('click', function(e) {
            if (e.target === modalElement) {
                console.log('Backdrop clicked');
                window.hideModalManually();
            }
        });

        // Handle escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && modalElement.classList.contains('show')) {
                console.log('Escape key pressed');
                window.hideModalManually();
            }
        });
    }

    // Check tree table elements
    const treeTable = document.getElementById('hierarchicalTable');
    if (treeTable) {
        console.log('Tree table found');
        const rows = treeTable.querySelectorAll('.tree-row');
        console.log('Tree rows found:', rows.length);
    }
});

console.log('All inline functions loaded successfully');
</script>

@endsection

@section('styles')
<style>
/* Enhanced Report Header */
.report-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 2.5rem;
    border-radius: 20px;
    margin-bottom: 2rem;
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
    position: relative;
    overflow: hidden;
}

.report-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="10" cy="90" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    pointer-events: none;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    z-index: 1;
}

.header-text {
    display: flex;
    align-items: center;
    gap: 1.5rem;
}

.header-icon {
    width: 60px;
    height: 60px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.header-icon i {
    font-size: 1.8rem;
    color: white;
}

.header-title {
    color: white;
    font-size: 2rem;
    font-weight: 700;
    margin: 0;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-subtitle {
    color: rgba(255, 255, 255, 0.9);
    margin: 0.5rem 0 0 0;
    font-size: 1rem;
    font-weight: 400;
}

.header-actions {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.btn-action {
    padding: 0.75rem 1.5rem;
    border-radius: 12px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.btn-action:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.btn-outline-light {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border-color: rgba(255, 255, 255, 0.3);
}

.btn-light {
    background: rgba(255, 255, 255, 0.9);
    color: #667eea;
    border-color: transparent;
}

/* Enhanced Filters */
.filters-card {
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    margin-bottom: 2rem;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.filters-header {
    padding: 1.5rem 2rem 1rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.filters-title {
    color: #2d3748;
    font-size: 1.25rem;
    font-weight: 600;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.filters-title i {
    color: #667eea;
    font-size: 1.1rem;
}

.filters-body {
    padding: 1.5rem 2rem 2rem;
}

.filters-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.filter-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    align-items: end;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.filter-label {
    font-weight: 600;
    color: #4a5568;
    font-size: 0.875rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.filter-label i {
    color: #667eea;
    font-size: 0.875rem;
}

.filter-select,
.filter-input {
    padding: 0.75rem 1rem;
    border: 2px solid #e2e8f0;
    border-radius: 10px;
    font-size: 0.875rem;
    transition: all 0.3s ease;
    background: white;
}

.filter-select:focus,
.filter-input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.filter-actions {
    display: flex;
    gap: 1rem;
    align-items: end;
}

.btn-filter {
    padding: 0.75rem 1.5rem;
    border-radius: 10px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    border: none;
}

.btn-filter:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Enhanced Statistics Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: white;
    border-radius: 16px;
    padding: 1.5rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(0, 0, 0, 0.05);
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--stat-color);
}

.stat-primary {
    --stat-color: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-success {
    --stat-color: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
}

.stat-warning {
    --stat-color: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);
}

.stat-info {
    --stat-color: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
}

.stat-card {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    background: var(--stat-color);
}

.stat-content {
    flex: 1;
}

.stat-number {
    font-size: 1.75rem;
    font-weight: 700;
    color: #2d3748;
    line-height: 1;
}

.stat-label {
    color: #718096;
    font-size: 0.875rem;
    font-weight: 500;
    margin-top: 0.25rem;
}

.stat-trend {
    color: #48bb78;
    font-size: 1.25rem;
}

.progress-circle {
    width: 40px;
    height: 40px;
    position: relative;
}

.progress-circle svg {
    width: 100%;
    height: 100%;
    transform: rotate(-90deg);
}

.circle-bg {
    fill: none;
    stroke: #e2e8f0;
    stroke-width: 3;
}

.circle {
    fill: none;
    stroke: #48bb78;
    stroke-width: 3;
    stroke-linecap: round;
    transition: stroke-dasharray 0.3s ease;
}

/* Tree Table Styles */
.enhanced-tree-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    background: white;
}

/* Enhanced Summary Cards */
.summary-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.summary-card {
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(0, 0, 0, 0.05);
    overflow: hidden;
}

.summary-header {
    padding: 1.5rem 1.5rem 1rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.summary-icon {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.25rem;
}

.bg-gradient-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.bg-gradient-success {
    background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
}

.summary-title {
    color: #2d3748;
    font-size: 1.1rem;
    font-weight: 600;
    margin: 0;
}

.summary-content {
    padding: 1rem 1.5rem 1.5rem;
    max-height: 300px;
    overflow-y: auto;
}

.contributor-item,
.activity-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.75rem 0;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.contributor-item:last-child,
.activity-item:last-child {
    border-bottom: none;
}

.contributor-rank {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 0.875rem;
}

.contributor-info,
.activity-info {
    flex: 1;
}

.contributor-name,
.activity-title {
    font-weight: 600;
    color: #2d3748;
    font-size: 0.875rem;
}

.contributor-meta,
.activity-meta {
    display: flex;
    gap: 1rem;
    margin-top: 0.25rem;
}

.contributor-type,
.activity-user {
    color: #718096;
    font-size: 0.75rem;
    background: #f7fafc;
    padding: 0.25rem 0.5rem;
    border-radius: 6px;
}

.contributor-count,
.activity-time {
    color: #667eea;
    font-size: 0.75rem;
    font-weight: 500;
}

.contributor-badge {
    color: #ed8936;
    font-size: 1.25rem;
}

.activity-icon {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.875rem;
}

.activity-timestamp {
    color: #718096;
    font-size: 0.75rem;
    font-weight: 500;
}

.empty-state {
    text-align: center;
    padding: 2rem;
    color: #a0aec0;
}

.empty-state i {
    font-size: 2rem;
    margin-bottom: 0.5rem;
    display: block;
}

/* Enhanced Table Styles */
.report-table-card {
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(0, 0, 0, 0.05);
    overflow: hidden;
}

.table-header {
    padding: 1.5rem 2rem;
    background: linear-gradient(135deg, #f8fafc 0%, #edf2f7 100%);
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.table-title-section {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.table-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.25rem;
}

.table-title {
    color: #2d3748;
    font-size: 1.25rem;
    font-weight: 600;
    margin: 0;
}

.table-subtitle {
    color: #718096;
    font-size: 0.875rem;
    margin: 0.25rem 0 0 0;
}

.table-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex-wrap: wrap;
}

.control-group {
    display: flex;
    gap: 0.5rem;
}

.btn-control {
    padding: 0.5rem 1rem;
    border-radius: 8px;
    font-size: 0.875rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    border: 1px solid #e2e8f0;
    background: white;
    color: #4a5568;
}

.btn-control:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border-color: #667eea;
    color: #667eea;
}

.stats-badge {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.table-container {
    position: relative;
}

.table-wrapper {
    overflow-x: auto;
    max-height: 70vh;
    overflow-y: auto;
}

.table-header-enhanced {
    background: linear-gradient(135deg, #f8fafc 0%, #edf2f7 100%);
    position: sticky;
    top: 0;
    z-index: 10;
}

.table-header-enhanced th {
    padding: 1rem;
    border: none;
    font-weight: 600;
    font-size: 0.875rem;
    color: #4a5568;
    text-align: left;
    border-bottom: 2px solid #e2e8f0;
}

.header-content {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.header-content i {
    color: #667eea;
    font-size: 0.875rem;
}

/* Enhanced Row Styles */
.enhanced-row {
    transition: all 0.3s ease;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.enhanced-row:hover {
    background: linear-gradient(135deg, #f8fafc 0%, #edf2f7 100%);
    transform: scale(1.001);
}

.enhanced-cell {
    padding: 1rem;
    vertical-align: middle;
    border: none;
}

.tree-content {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.enhanced-toggle {
    width: 24px;
    height: 24px;
    border: none;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    font-size: 0.75rem;
}

.enhanced-toggle:hover {
    transform: scale(1.1);
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.enhanced-toggle.expanded {
    transform: rotate(90deg);
}

.tree-spacer {
    width: 24px;
    height: 24px;
}

.location-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.location-icon {
    width: 36px;
    height: 36px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    color: white;
}

.subcounty-icon {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.parish-icon {
    background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
}

.village-icon {
    background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);
}

.station-icon {
    background: linear-gradient(135deg, #718096 0%, #4a5568 100%);
}

.location-details {
    display: flex;
    flex-direction: column;
}

.location-name {
    font-weight: 600;
    color: #2d3748;
    font-size: 0.875rem;
}

.location-type {
    color: #718096;
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stat-cell {
    padding: 1rem;
    text-align: center;
}

.stat-value {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.25rem;
}

.stat-number {
    font-size: 1.25rem;
    font-weight: 700;
    line-height: 1;
}

.stat-label {
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    opacity: 0.7;
}

.total-stat .stat-number {
    color: #667eea;
}

.success-stat .stat-number {
    color: #48bb78;
}

.warning-stat .stat-number {
    color: #ed8936;
}

.completion-cell {
    padding: 1rem;
    text-align: center;
}

.completion-display {
    display: flex;
    justify-content: center;
    align-items: center;
}

.progress-ring {
    position: relative;
    width: 40px;
    height: 40px;
}

.progress-ring-svg {
    transform: rotate(-90deg);
}

.progress-ring-circle-bg {
    fill: none;
    stroke: #e2e8f0;
    stroke-width: 3;
}

.progress-ring-circle {
    fill: none;
    stroke-width: 3;
    stroke-linecap: round;
    transition: stroke-dasharray 0.3s ease;
}

.progress-high {
    stroke: #48bb78;
}

.progress-medium {
    stroke: #ed8936;
}

.progress-low {
    stroke: #e53e3e;
}

.progress-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 0.75rem;
    font-weight: 600;
    color: #2d3748;
}

.actions-cell {
    padding: 1rem;
    text-align: center;
}

.btn-details {
    padding: 0.5rem 1rem;
    border-radius: 8px;
    font-size: 0.875rem;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    border: none;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-details:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
    color: white;
}

.table-footer {
    padding: 1rem 2rem;
    background: #f8fafc;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
}

.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.pagination-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #718096;
    font-size: 0.875rem;
}

.pagination-info i {
    color: #667eea;
}

.tree-row {
    transition: background-color 0.2s ease;
}

/* Enhanced Modal Styles */
.enhanced-modal .modal-dialog {
    margin: 2rem auto;
}

.enhanced-modal-content {
    border: none;
    border-radius: 16px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
    overflow: hidden;
}

.enhanced-modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1.5rem 2rem;
    border-bottom: none;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.modal-title-section {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.modal-icon {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
}

.modal-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin: 0;
}

.modal-subtitle {
    font-size: 0.875rem;
    opacity: 0.9;
    margin: 0.25rem 0 0 0;
}

.enhanced-close {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    width: 32px;
    height: 32px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.enhanced-close:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

.enhanced-modal-body {
    padding: 2rem;
    min-height: 200px;
}

.loading-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem;
}

.loading-spinner {
    margin-bottom: 1rem;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 3px solid #e2e8f0;
    border-top: 3px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-text {
    color: #718096;
    font-size: 0.875rem;
    margin: 0;
}

.enhanced-modal-footer {
    padding: 1rem 2rem;
    background: #f8fafc;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    display: flex;
    justify-content: flex-end;
}

.btn-modal-close {
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    border: 1px solid #e2e8f0;
    background: white;
    color: #4a5568;
}

.btn-modal-close:hover {
    background: #f7fafc;
    border-color: #cbd5e0;
    color: #2d3748;
}

.tree-row.hidden {
    display: none;
}

/* Hierarchy Level Styles */
.tree-content.level-1 {
    padding-left: 0;
    font-weight: 600;
}

.tree-content.level-2 {
    padding-left: 20px;
    font-weight: 500;
}

.tree-content.level-3 {
    padding-left: 40px;
    font-weight: 400;
}

.tree-content.level-4 {
    padding-left: 60px;
    font-weight: 400;
    font-size: 0.85rem;
}

/* Level-specific row styling */
.tree-row.level-1 {
    background-color: #ffffff;
    border-left: 4px solid #007bff;
}

.tree-row.level-2 {
    background-color: #f8f9fa;
    border-left: 4px solid #28a745;
}

.tree-row.level-3 {
    background-color: #ffffff;
    border-left: 4px solid #ffc107;
}

.tree-row.level-4 {
    background-color: #f8f9fa;
    border-left: 4px solid #6c757d;
}

/* Tree Toggle Button */
.tree-toggle {
    border: none;
    background: none;
    color: #6c757d;
    font-size: 0.875rem;
    width: 20px;
    height: 20px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    transition: transform 0.2s ease, color 0.2s ease;
}

.tree-toggle:hover {
    color: #007bff;
    background-color: #e9ecef;
    border-radius: 3px;
}

.tree-toggle.expanded {
    transform: rotate(90deg);
}

.tree-spacer {
    display: inline-block;
    width: 20px;
    height: 20px;
}

/* Loading States */
.tree-loading {
    opacity: 0.6;
    pointer-events: none;
}

.tree-loading-spinner {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-left: 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Progress Bars */
.progress {
    background-color: #e9ecef;
    height: 8px;
}

.progress-bar {
    transition: width 0.3s ease;
}

/* Badges */
.badge {
    font-size: 0.75rem;
    font-weight: 500;
}

/* Modal Styles */
.modal-xl {
    max-width: 1200px;
}

.nav-tabs .nav-link {
    color: #495057;
    border: 1px solid transparent;
    border-top-left-radius: 0.375rem;
    border-top-right-radius: 0.375rem;
}

.nav-tabs .nav-link:hover {
    border-color: #e9ecef #e9ecef #dee2e6;
    isolation: isolate;
}

.nav-tabs .nav-link.active {
    color: #495057;
    background-color: #fff;
    border-color: #dee2e6 #dee2e6 #fff;
}

/* Card Styles */
.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
}

/* List Group Styles */
.list-group-item {
    border-left: none;
    border-right: none;
}

.list-group-item:first-child {
    border-top: none;
}

.list-group-item:last-child {
    border-bottom: none;
}

/* Spinner */
.spinner-border {
    width: 3rem;
    height: 3rem;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .summary-cards {
        grid-template-columns: 1fr;
    }

    .table-controls {
        flex-direction: column;
        align-items: stretch;
    }

    .control-group {
        justify-content: center;
    }
}

@media (max-width: 768px) {
    .report-header {
        padding: 1.5rem;
    }

    .header-content {
        flex-direction: column;
        gap: 1.5rem;
        text-align: center;
    }

    .header-actions {
        width: 100%;
        justify-content: center;
    }

    .stats-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .stat-card {
        padding: 1rem;
    }

    .filters-body {
        padding: 1rem;
    }

    .filter-row {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .filter-actions {
        flex-direction: column;
        align-items: stretch;
    }

    .table-header {
        padding: 1rem;
        flex-direction: column;
        align-items: stretch;
        gap: 1rem;
    }

    .table-title-section {
        justify-content: center;
        text-align: center;
    }

    .table-controls {
        justify-content: center;
    }

    .enhanced-cell {
        padding: 0.75rem 0.5rem;
    }

    .location-icon {
        width: 28px;
        height: 28px;
        font-size: 0.875rem;
    }

    .location-name {
        font-size: 0.8rem;
    }

    .stat-number {
        font-size: 1rem;
    }

    .progress-ring {
        width: 32px;
        height: 32px;
    }

    .progress-text {
        font-size: 0.65rem;
    }

    .btn-details {
        padding: 0.375rem 0.75rem;
        font-size: 0.8rem;
    }

    .footer-content {
        flex-direction: column;
        text-align: center;
    }

    .enhanced-modal .modal-dialog {
        margin: 1rem;
    }

    .enhanced-modal-header {
        padding: 1rem;
    }

    .enhanced-modal-body {
        padding: 1rem;
    }
}

@media (max-width: 576px) {
    .header-title {
        font-size: 1.5rem;
    }

    .header-subtitle {
        font-size: 0.875rem;
    }

    .btn-action span {
        display: none;
    }

    .stat-card {
        flex-direction: column;
        text-align: center;
        gap: 0.75rem;
    }

    .table-wrapper {
        font-size: 0.8rem;
    }

    .enhanced-toggle {
        width: 20px;
        height: 20px;
        font-size: 0.65rem;
    }

    .tree-spacer {
        width: 20px;
        height: 20px;
    }

    .location-info {
        gap: 0.5rem;
    }

    .location-icon {
        width: 24px;
        height: 24px;
        font-size: 0.75rem;
    }

    .btn-control span,
    .btn-details span {
        display: none;
    }

    .stats-badge span {
        display: none;
    }
}

/* Animation for row expansion */
.tree-row.expanding {
    animation: fadeInUp 0.3s ease-in-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Custom scrollbar */
.table-wrapper::-webkit-scrollbar,
.summary-content::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

.table-wrapper::-webkit-scrollbar-track,
.summary-content::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.table-wrapper::-webkit-scrollbar-thumb,
.summary-content::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.table-wrapper::-webkit-scrollbar-thumb:hover,
.summary-content::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Focus states for accessibility */
.enhanced-toggle:focus,
.btn-control:focus,
.btn-details:focus,
.filter-select:focus,
.filter-input:focus {
    outline: 2px solid #667eea;
    outline-offset: 2px;
}

/* Print styles */
@media print {
    .header-actions,
    .table-controls,
    .table-footer,
    .enhanced-modal {
        display: none !important;
    }

    .report-header {
        background: white !important;
        color: black !important;
        box-shadow: none !important;
    }

    .enhanced-row:hover {
        background: white !important;
        transform: none !important;
    }
}

@media (max-width: 576px) {
    .tree-table th,
    .tree-table td {
        padding: 0.5rem 0.25rem;
        font-size: 0.75rem;
    }

    .tree-content.level-2 {
        padding-left: 10px;
    }

    .tree-content.level-3 {
        padding-left: 20px;
    }

    .tree-content.level-4 {
        padding-left: 30px;
    }

    .badge {
        font-size: 0.65rem;
    }

    .btn-sm {
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
    }

    .progress {
        width: 40px !important;
    }
}

/* Animation for row expansion */
.tree-row.expanding {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
</style>
@endsection

@section('scripts')
<script>
// Global variables for tree state management
window.expandedRows = new Set();
window.loadedChildren = new Map();

// Make functions globally accessible
window.exportReport = function() {
    // Get current filter parameters
    const urlParams = new URLSearchParams(window.location.search);
    const exportUrl = '{{ route("reports.polling-station-submission.export") }}?' + urlParams.toString();

    // Open export URL in new window
    window.open(exportUrl, '_blank');
};

window.refreshReport = function() {
    window.location.reload();
};

// Tree Table Functions
window.toggleTreeRow = function(rowId) {
    console.log('Toggling row:', rowId);

    const row = document.querySelector(`tr[data-id="${rowId}"]`);
    if (!row) {
        console.error('Row not found:', rowId);
        return;
    }

    const toggleBtn = row.querySelector('.tree-toggle');
    if (!toggleBtn) {
        console.error('Toggle button not found for row:', rowId);
        return;
    }

    const icon = toggleBtn.querySelector('i');
    if (!icon) {
        console.error('Icon not found for row:', rowId);
        return;
    }

    const isExpanded = row.dataset.expanded === 'true';
    console.log('Current expanded state:', isExpanded);

    if (isExpanded) {
        // Collapse
        collapseRow(rowId);
        row.dataset.expanded = 'false';
        icon.className = 'bi bi-chevron-right';
        toggleBtn.classList.remove('expanded');
        window.expandedRows.delete(rowId);
        console.log('Row collapsed:', rowId);
    } else {
        // Expand
        expandRow(rowId);
        row.dataset.expanded = 'true';
        icon.className = 'bi bi-chevron-down';
        toggleBtn.classList.add('expanded');
        window.expandedRows.add(rowId);
        console.log('Row expanded:', rowId);
    }
};

window.expandRow = function(rowId) {
    console.log('Expanding row:', rowId);

    const row = document.querySelector(`tr[data-id="${rowId}"]`);
    if (!row) {
        console.error('Row not found for expansion:', rowId);
        return;
    }

    const rowType = row.dataset.type;
    const rowName = row.dataset.name;

    console.log('Row details:', { type: rowType, name: rowName });

    // Check if children are already loaded
    if (window.loadedChildren.has(rowId)) {
        console.log('Children already loaded, showing existing children');
        showChildRows(rowId);
        return;
    }

    // Show loading state
    showLoadingState(row);

    // Get current filter parameters
    const urlParams = new URLSearchParams(window.location.search);
    urlParams.set('parent_id', rowId);
    urlParams.set('parent_type', rowType);
    urlParams.set('parent_name', rowName);

    // Add grandparent and great-grandparent names for deeper levels
    if (rowType === 'parish') {
        const subcountyRow = findParentRow(row, 'subcounty');
        if (subcountyRow) {
            urlParams.set('grandparent_name', subcountyRow.dataset.name);
            console.log('Added grandparent (subcounty):', subcountyRow.dataset.name);
        }
    } else if (rowType === 'village') {
        const parishRow = findParentRow(row, 'parish');
        const subcountyRow = findParentRow(row, 'subcounty');
        if (parishRow) {
            urlParams.set('grandparent_name', parishRow.dataset.name);
            console.log('Added grandparent (parish):', parishRow.dataset.name);
        }
        if (subcountyRow) {
            urlParams.set('great_grandparent_name', subcountyRow.dataset.name);
            console.log('Added great-grandparent (subcounty):', subcountyRow.dataset.name);
        }
    }

    // Fetch child data
    const apiUrl = '{{ route("api.reports.tree-child-data") }}?' + urlParams.toString();
    console.log('Fetching child data from:', apiUrl);

    fetch(apiUrl)
        .then(response => {
            console.log('API response status:', response.status);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('Received child data:', data);
            hideLoadingState(row);

            if (data.children && data.children.length > 0) {
                insertChildRows(rowId, data.children);
                window.loadedChildren.set(rowId, data.children);
                console.log('Children inserted and cached for row:', rowId);
            } else {
                console.log('No children found for row:', rowId);
                // Update the row to indicate it has no children
                row.dataset.hasChildren = 'false';
                const toggleBtn = row.querySelector('.tree-toggle');
                if (toggleBtn) {
                    toggleBtn.style.display = 'none';
                }
            }
        })
        .catch(error => {
            console.error('Error loading child data:', error);
            hideLoadingState(row);
            showErrorState(row);
        });
}

window.collapseRow = function(rowId) {
    hideChildRows(rowId);
};

window.showChildRows = function(parentId) {
    const children = window.loadedChildren.get(parentId);
    if (!children) return;

    children.forEach(child => {
        const childRow = document.querySelector(`tr[data-id="${child.id}"]`);
        if (childRow) {
            childRow.classList.remove('hidden');
            childRow.classList.add('expanding');

            // If this child is also expanded, show its children
            if (window.expandedRows.has(child.id)) {
                window.showChildRows(child.id);
            }
        }
    });
};

window.hideChildRows = function(parentId) {
    const children = window.loadedChildren.get(parentId);
    if (!children) return;

    children.forEach(child => {
        const childRow = document.querySelector(`tr[data-id="${child.id}"]`);
        if (childRow) {
            childRow.classList.add('hidden');
            childRow.classList.remove('expanding');

            // Recursively hide grandchildren
            window.hideChildRows(child.id);
        }
    });
};

window.insertChildRows = function(parentId, children) {
    console.log('Inserting child rows for parent:', parentId, 'Children count:', children.length);

    const parentRow = document.querySelector(`tr[data-id="${parentId}"]`);
    if (!parentRow) {
        console.error('Parent row not found:', parentId);
        return;
    }

    const tbody = parentRow.parentNode;
    let insertAfter = parentRow;

    children.forEach((child, index) => {
        console.log(`Processing child ${index + 1}:`, child.id, child.name);

        // Check if row already exists
        let existingRow = document.querySelector(`tr[data-id="${child.id}"]`);
        if (existingRow) {
            console.log('Row already exists, showing it:', child.id);
            existingRow.classList.remove('hidden');
            insertAfter = existingRow;
            return;
        }

        try {
            const childRow = createTreeRow(child);
            insertAfter.insertAdjacentElement('afterend', childRow);
            insertAfter = childRow;
            console.log('Child row inserted:', child.id);
        } catch (error) {
            console.error('Error creating child row:', error, child);
        }
    });

    console.log('Finished inserting child rows for parent:', parentId);
};

window.createTreeRow = function(data) {
    const row = document.createElement('tr');
    row.className = `tree-row level-${data.level} expanding`;
    row.dataset.id = data.id;
    row.dataset.level = data.level;
    row.dataset.type = data.type;
    row.dataset.name = data.name;
    row.dataset.hasChildren = data.has_children ? 'true' : 'false';
    row.dataset.expanded = 'false';

    let html = '';

    if (data.type === 'station') {
        // Station row
        const statusBadge = data.has_votes ?
            '<span class="badge bg-success">Submitted</span>' :
            '<span class="badge bg-secondary">Pending</span>';

        const lastSubmission = data.last_submission ?
            new Date(data.last_submission).toLocaleDateString() + ' ' + new Date(data.last_submission).toLocaleTimeString() :
            '<span class="text-muted">-</span>';

        const contributors = data.submission_users && data.submission_users.length > 0 ?
            data.submission_users.slice(0, 2).map(user => `<span class="badge bg-light text-dark me-1">${user.name}</span>`).join('') +
            (data.submission_users.length > 2 ? `<span class="text-muted">+${data.submission_users.length - 2}</span>` : '') :
            '<span class="text-muted">None</span>';

        html = `
            <td class="tree-cell">
                <div class="tree-content level-${data.level}">
                    <span class="tree-spacer me-2"></span>
                    <i class="bi bi-building text-muted me-2"></i>
                    ${data.name}
                    <small class="text-muted ms-2">(Station)</small>
                </div>
            </td>
            <td colspan="3">${statusBadge}</td>
            <td><small>${lastSubmission}</small></td>
            <td>
                <button class="btn btn-sm btn-outline-primary" onclick="showStationDetails('${data.id}', '${data.name}')">
                    <i class="bi bi-eye"></i>
                </button>
            </td>
        `;
    } else {
        // Location row (subcounty, parish, village)
        const toggleButton = data.has_children ?
            `<button class="tree-toggle btn btn-sm p-0 me-2" onclick="toggleTreeRow('${data.id}')">
                <i class="bi bi-chevron-right"></i>
            </button>` :
            '<span class="tree-spacer me-2"></span>';

        const icon = data.type === 'parish' ? 'bi-geo' :
                    data.type === 'village' ? 'bi-house-door' : 'bi-geo-alt';

        const iconColor = data.type === 'parish' ? 'text-success' :
                         data.type === 'village' ? 'text-warning' : 'text-primary';

        const progressBarColor = data.percentage == 100 ? 'success' :
                               data.percentage >= 75 ? 'warning' : 'danger';

        const badgeColor = data.percentage == 100 ? 'success' :
                          data.percentage >= 75 ? 'warning' : 'danger';

        html = `
            <td class="tree-cell">
                <div class="tree-content level-${data.level}">
                    ${toggleButton}
                    <i class="bi ${icon} ${iconColor} me-2"></i>
                    <strong>${data.name}</strong>
                    <small class="text-muted ms-2">(${data.type.charAt(0).toUpperCase() + data.type.slice(1)})</small>
                </div>
            </td>
            <td>
                <span class="badge bg-primary">${data.total_stations}</span>
            </td>
            <td>
                <span class="badge bg-success">${data.submitted_stations}</span>
            </td>
            <td>
                <span class="badge bg-warning">${data.total_stations - data.submitted_stations}</span>
            </td>
            <td>
                <div class="d-flex align-items-center">
                    <div class="progress me-2" style="width: 60px; height: 8px;">
                        <div class="progress-bar bg-${progressBarColor}" style="width: ${data.percentage}%"></div>
                    </div>
                    <span class="badge bg-${badgeColor}">${data.percentage}%</span>
                </div>
            </td>
            <td>
                <button class="btn btn-sm btn-outline-primary" onclick="showLocationDetails('${data.type}', '${data.name}', '', '')">
                    <i class="bi bi-eye"></i>
                </button>
            </td>
        `;
    }

    row.innerHTML = html;
    return row;
};

window.findParentRow = function(row, parentType) {
    let currentRow = row.previousElementSibling;
    while (currentRow) {
        if (currentRow.dataset.type === parentType) {
            return currentRow;
        }
        currentRow = currentRow.previousElementSibling;
    }
    return null;
};

window.showLoadingState = function(row) {
    const toggleBtn = row.querySelector('.tree-toggle');
    if (toggleBtn) {
        toggleBtn.innerHTML = '<div class="tree-loading-spinner"></div>';
        toggleBtn.disabled = true;
    }
};

window.hideLoadingState = function(row) {
    const toggleBtn = row.querySelector('.tree-toggle');
    if (toggleBtn) {
        toggleBtn.innerHTML = '<i class="bi bi-chevron-down"></i>';
        toggleBtn.disabled = false;
        toggleBtn.classList.add('expanded');
    }
};

window.showErrorState = function(row) {
    const toggleBtn = row.querySelector('.tree-toggle');
    if (toggleBtn) {
        toggleBtn.innerHTML = '<i class="bi bi-exclamation-triangle text-danger"></i>';
        toggleBtn.disabled = false;
    }
};

window.expandAllRows = function() {
    console.log('Expanding all rows');
    const level1Rows = document.querySelectorAll('.tree-row.level-1[data-has-children="true"]');
    console.log('Found level 1 rows with children:', level1Rows.length);

    level1Rows.forEach((row, index) => {
        console.log(`Processing row ${index + 1}:`, row.dataset.id, row.dataset.expanded);
        if (row.dataset.expanded === 'false') {
            // Add a small delay between expansions to prevent overwhelming the API
            setTimeout(() => {
                window.toggleTreeRow(row.dataset.id);
            }, index * 100);
        }
    });
};

window.collapseAllRows = function() {
    console.log('Collapsing all rows');
    const expandedRows = document.querySelectorAll('.tree-row[data-expanded="true"]');
    console.log('Found expanded rows:', expandedRows.length);

    expandedRows.forEach((row, index) => {
        console.log(`Collapsing row ${index + 1}:`, row.dataset.id);
        // Add a small delay between collapses for smooth animation
        setTimeout(() => {
            window.toggleTreeRow(row.dataset.id);
        }, index * 50);
    });
};

window.showLocationDetails = function(level, name, parentName = '', grandparentName = '') {
    console.log('Showing location details for:', level, name, parentName, grandparentName);

    const modalElement = document.getElementById('detailsModal');
    const modalTitle = document.getElementById('detailsModalLabel');
    const modalContent = document.getElementById('modalContent');

    if (!modalElement) {
        console.error('Modal element not found');
        return;
    }

    if (!modalTitle) {
        console.error('Modal title element not found');
        return;
    }

    if (!modalContent) {
        console.error('Modal content element not found');
        return;
    }

    // Update modal title
    modalTitle.innerHTML = `<i class="bi bi-info-circle"></i> ${level.charAt(0).toUpperCase() + level.slice(1)} Details: ${name}`;

    // Show loading state
    modalContent.innerHTML = `
        <div class="text-center py-4">
            <div class="spinner-border" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-2">Loading details...</p>
        </div>
    `;

    // Try different ways to show modal for compatibility
    try {
        // Method 1: Using Bootstrap 5 Modal class
        if (typeof bootstrap !== 'undefined' && bootstrap.Modal) {
            const modal = new bootstrap.Modal(modalElement);
            modal.show();
            console.log('Modal shown using Bootstrap 5 Modal class');
        } else {
            // Method 2: Using jQuery if available (fallback)
            if (typeof $ !== 'undefined') {
                $(modalElement).modal('show');
                console.log('Modal shown using jQuery');
            } else {
                // Method 3: Manual show using Bootstrap classes
                modalElement.classList.add('show');
                modalElement.style.display = 'block';
                modalElement.setAttribute('aria-hidden', 'false');
                document.body.classList.add('modal-open');

                // Add backdrop
                const backdrop = document.createElement('div');
                backdrop.className = 'modal-backdrop fade show';
                backdrop.id = 'modal-backdrop';
                document.body.appendChild(backdrop);

                console.log('Modal shown manually');
            }
        }
    } catch (error) {
        console.error('Error showing modal:', error);
        return;
    }

    // Get current filter parameters
    const urlParams = new URLSearchParams(window.location.search);
    urlParams.set('level', level);
    urlParams.set('name', name);
    if (parentName) urlParams.set('parent_name', parentName);
    if (grandparentName) urlParams.set('grandparent_name', grandparentName);

    // Fetch details
    const apiUrl = '{{ route("api.reports.location-details") }}?' + urlParams.toString();
    console.log('Fetching from:', apiUrl);

    fetch(apiUrl)
        .then(response => {
            console.log('Response status:', response.status);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('Received data:', data);
            modalContent.innerHTML = window.buildLocationDetailsHTML(data);
        })
        .catch(error => {
            console.error('Error fetching location details:', error);
            modalContent.innerHTML = `
                <div class="alert alert-danger">
                    <i class="bi bi-exclamation-triangle"></i>
                    Error loading details: ${error.message}
                    <br><small>Please check the console for more details.</small>
                </div>
            `;
        });
};

window.showStationDetails = function(stationId, stationName) {
    console.log('Showing station details for:', stationId, stationName);

    const modalElement = document.getElementById('detailsModal');
    const modalTitle = document.getElementById('detailsModalLabel');
    const modalContent = document.getElementById('modalContent');

    if (!modalElement || !modalTitle || !modalContent) {
        console.error('Modal elements not found');
        return;
    }

    // Extract numeric ID from station ID
    const numericId = stationId.replace('station_', '');

    // Update modal title
    modalTitle.innerHTML = `<i class="bi bi-building"></i> Station Details: ${stationName}`;

    // Show loading state
    modalContent.innerHTML = `
        <div class="text-center py-4">
            <div class="spinner-border" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-2">Loading station details...</p>
        </div>
    `;

    // Show modal using the same method as location details
    try {
        if (typeof bootstrap !== 'undefined' && bootstrap.Modal) {
            const modal = new bootstrap.Modal(modalElement);
            modal.show();
        } else if (typeof $ !== 'undefined') {
            $(modalElement).modal('show');
        } else {
            window.showModalManually(modalElement);
        }
    } catch (error) {
        console.error('Error showing modal:', error);
        return;
    }

    // For now, show basic station info (can be enhanced later)
    setTimeout(() => {
        modalContent.innerHTML = `
            <div class="alert alert-info">
                <i class="bi bi-info-circle"></i>
                Detailed station information will be available in the next update.
                <br><strong>Station ID:</strong> ${numericId}
                <br><strong>Station Name:</strong> ${stationName}
                <br><br>
                <small class="text-muted">This feature will include vote counts, submission history, and contributor details.</small>
            </div>
        `;
    }, 500);
};

// Helper function to manually show modal
window.showModalManually = function(modalElement) {
    modalElement.classList.add('show');
    modalElement.style.display = 'block';
    modalElement.setAttribute('aria-hidden', 'false');
    document.body.classList.add('modal-open');

    // Add backdrop
    let backdrop = document.getElementById('modal-backdrop');
    if (!backdrop) {
        backdrop = document.createElement('div');
        backdrop.className = 'modal-backdrop fade show';
        backdrop.id = 'modal-backdrop';
        document.body.appendChild(backdrop);
    }

    console.log('Modal shown manually');
};

// Helper function to manually hide modal
window.hideModalManually = function() {
    const modalElement = document.getElementById('detailsModal');
    const backdrop = document.getElementById('modal-backdrop');

    if (modalElement) {
        modalElement.classList.remove('show');
        modalElement.style.display = 'none';
        modalElement.setAttribute('aria-hidden', 'true');
    }

    if (backdrop) {
        backdrop.remove();
    }

    document.body.classList.remove('modal-open');
    console.log('Modal hidden manually');
};

window.buildLocationDetailsHTML = function(data) {
    const location = data.location;
    const stations = data.stations;
    const users = data.users;
    const recentSubmissions = data.recent_submissions;

    let html = `
        <!-- Location Summary -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card bg-primary text-white">
                    <div class="card-body text-center">
                        <h4>${location.total_stations}</h4>
                        <small>Total Stations</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-success text-white">
                    <div class="card-body text-center">
                        <h4>${location.submitted_stations}</h4>
                        <small>Submitted</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-warning text-white">
                    <div class="card-body text-center">
                        <h4>${location.pending_stations}</h4>
                        <small>Pending</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-info text-white">
                    <div class="card-body text-center">
                        <h4>${location.percentage}%</h4>
                        <small>Completion</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Tabs -->
        <ul class="nav nav-tabs" id="detailsTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="stations-tab" data-bs-toggle="tab" data-bs-target="#stations" type="button" role="tab">
                    <i class="bi bi-building"></i> Stations (${stations.length})
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="contributors-tab" data-bs-toggle="tab" data-bs-target="#contributors" type="button" role="tab">
                    <i class="bi bi-people"></i> Contributors (${users.length})
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="activity-tab" data-bs-toggle="tab" data-bs-target="#activity" type="button" role="tab">
                    <i class="bi bi-clock-history"></i> Recent Activity
                </button>
            </li>
        </ul>

        <div class="tab-content mt-3" id="detailsTabContent">
            <!-- Stations Tab -->
            <div class="tab-pane fade show active" id="stations" role="tabpanel">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Station Name</th>
                                <th>Status</th>
                                <th>Last Submission</th>
                                <th>Vote Count</th>
                            </tr>
                        </thead>
                        <tbody>
    `;

    stations.forEach(station => {
        const statusBadge = station.has_votes ?
            '<span class="badge bg-success">Submitted</span>' :
            '<span class="badge bg-secondary">Pending</span>';

        const lastSubmission = station.last_submission ?
            new Date(station.last_submission).toLocaleDateString() + ' ' + new Date(station.last_submission).toLocaleTimeString() :
            '-';

        html += `
            <tr>
                <td><strong>${station.name}</strong></td>
                <td>${statusBadge}</td>
                <td><small>${lastSubmission}</small></td>
                <td><span class="badge bg-primary">${station.vote_count || 0}</span></td>
            </tr>
        `;
    });

    html += `
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Contributors Tab -->
            <div class="tab-pane fade" id="contributors" role="tabpanel">
                <div class="row">
    `;

    users.forEach(user => {
        html += `
            <div class="col-md-6 mb-3">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-1">${user.name}</h6>
                                <span class="badge bg-secondary">${user.type.charAt(0).toUpperCase() + user.type.slice(1)}</span>
                            </div>
                            <div class="text-end">
                                <div class="badge bg-primary">${user.submission_count} submissions</div>
                                <div><small class="text-muted">Last: ${new Date(user.last_submission).toLocaleDateString()}</small></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    });

    html += `
                </div>
            </div>

            <!-- Activity Tab -->
            <div class="tab-pane fade" id="activity" role="tabpanel">
                <div class="list-group list-group-flush">
    `;

    recentSubmissions.forEach(submission => {
        html += `
            <div class="list-group-item">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="mb-1">${submission.station_name}</h6>
                        <small class="text-muted">Submitted by ${submission.user_name} (${submission.user_type})</small>
                    </div>
                    <small>${new Date(submission.submission_time).toLocaleDateString()} ${new Date(submission.submission_time).toLocaleTimeString()}</small>
                </div>
            </div>
        `;
    });

    html += `
                </div>
            </div>
        </div>
    `;

    return html;
};

// Preserve tree state during pagination
window.preserveTreeState = function() {
    const state = {
        expanded: Array.from(window.expandedRows),
        loaded: Array.from(window.loadedChildren.keys())
    };
    sessionStorage.setItem('treeState', JSON.stringify(state));
};

window.restoreTreeState = function() {
    const stateStr = sessionStorage.getItem('treeState');
    if (stateStr) {
        try {
            const state = JSON.parse(stateStr);
            window.expandedRows = new Set(state.expanded || []);
            // Note: We don't restore loaded children as they need to be re-fetched
        } catch (e) {
            console.warn('Failed to restore tree state:', e);
        }
    }
};

// Initialize tree state on page load
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM Content Loaded - Initializing tree table');

    // Check if Bootstrap is available
    if (typeof bootstrap !== 'undefined') {
        console.log('Bootstrap is available:', bootstrap.Modal ? 'Modal class found' : 'Modal class not found');
    } else {
        console.log('Bootstrap is not available');
    }

    // Check if jQuery is available
    if (typeof $ !== 'undefined') {
        console.log('jQuery is available');
    } else {
        console.log('jQuery is not available');
    }

    // Set up modal close handlers
    const modalElement = document.getElementById('detailsModal');
    if (modalElement) {
        console.log('Modal element found');

        // Handle close button clicks
        const closeButtons = modalElement.querySelectorAll('[data-bs-dismiss="modal"]');
        closeButtons.forEach(button => {
            button.addEventListener('click', function() {
                console.log('Close button clicked');
                window.hideModalManually();
            });
        });

        // Handle backdrop clicks
        modalElement.addEventListener('click', function(e) {
            if (e.target === modalElement) {
                console.log('Backdrop clicked');
                window.hideModalManually();
            }
        });

        // Handle escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && modalElement.classList.contains('show')) {
                console.log('Escape key pressed');
                window.hideModalManually();
            }
        });
    } else {
        console.error('Modal element not found');
    }

    // Check tree table elements
    const treeTable = document.getElementById('hierarchicalTable');
    if (treeTable) {
        console.log('Tree table found');
        const rows = treeTable.querySelectorAll('.tree-row');
        console.log('Tree rows found:', rows.length);

        // Log first few rows for debugging
        rows.forEach((row, index) => {
            if (index < 3) {
                console.log(`Row ${index}:`, {
                    id: row.dataset.id,
                    level: row.dataset.level,
                    type: row.dataset.type,
                    hasChildren: row.dataset.hasChildren,
                    expanded: row.dataset.expanded
                });
            }
        });
    } else {
        console.error('Tree table not found');
    }

    window.restoreTreeState();
});

// Save tree state before page unload
window.addEventListener('beforeunload', function() {
    window.preserveTreeState();
});

// Auto-refresh every 5 minutes
setInterval(function() {
    const lastRefresh = localStorage.getItem('lastReportRefresh');
    const now = Date.now();

    if (!lastRefresh || (now - parseInt(lastRefresh)) > 300000) { // 5 minutes
        localStorage.setItem('lastReportRefresh', now.toString());
        // Optionally auto-refresh
        // window.location.reload();
    }
}, 60000); // Check every minute

// Keyboard shortcuts
document.addEventListener('keydown', function(e) {
    // Ctrl/Cmd + E: Expand all
    if ((e.ctrlKey || e.metaKey) && e.key === 'e') {
        e.preventDefault();
        window.expandAllRows();
    }

    // Ctrl/Cmd + C: Collapse all
    if ((e.ctrlKey || e.metaKey) && e.key === 'c') {
        e.preventDefault();
        window.collapseAllRows();
    }

    // Ctrl/Cmd + R: Refresh
    if ((e.ctrlKey || e.metaKey) && e.key === 'r') {
        e.preventDefault();
        window.refreshReport();
    }
});

// Add tooltips for keyboard shortcuts
document.addEventListener('DOMContentLoaded', function() {
    const expandBtn = document.querySelector('button[onclick="expandAllRows()"]');
    const collapseBtn = document.querySelector('button[onclick="collapseAllRows()"]');

    if (expandBtn) {
        expandBtn.title = 'Expand All (Ctrl+E)';
    }
    if (collapseBtn) {
        collapseBtn.title = 'Collapse All (Ctrl+C)';
    }
});

// Debug and test functions
window.testModal = function() {
    console.log('Testing modal functionality');
    window.showLocationDetails('subcounty', 'Test Subcounty', '', '');
};

window.debugTree = function() {
    console.log('=== TREE DEBUG INFO ===');
    console.log('Expanded rows:', Array.from(window.expandedRows));
    console.log('Loaded children:', Array.from(window.loadedChildren.keys()));

    const allRows = document.querySelectorAll('.tree-row');
    console.log('Total rows:', allRows.length);

    const level1Rows = document.querySelectorAll('.tree-row.level-1');
    console.log('Level 1 rows:', level1Rows.length);

    level1Rows.forEach((row, index) => {
        console.log(`Level 1 Row ${index + 1}:`, {
            id: row.dataset.id,
            name: row.dataset.name,
            hasChildren: row.dataset.hasChildren,
            expanded: row.dataset.expanded
        });
    });

    // Test first toggle button
    const firstToggle = document.querySelector('.tree-toggle');
    if (firstToggle) {
        console.log('First toggle button found:', firstToggle);
        console.log('First toggle onclick:', firstToggle.getAttribute('onclick'));
    } else {
        console.log('No toggle buttons found');
    }

    // Test modal elements
    const modal = document.getElementById('detailsModal');
    const modalTitle = document.getElementById('detailsModalLabel');
    const modalContent = document.getElementById('modalContent');

    console.log('Modal elements:', {
        modal: !!modal,
        modalTitle: !!modalTitle,
        modalContent: !!modalContent
    });

    // Test Bootstrap
    console.log('Bootstrap available:', typeof bootstrap !== 'undefined');
    if (typeof bootstrap !== 'undefined') {
        console.log('Bootstrap Modal class:', typeof bootstrap.Modal);
    }

    console.log('=== END DEBUG INFO ===');
};

// Add a simple click test for the first row
window.testFirstRowToggle = function() {
    const firstRow = document.querySelector('.tree-row.level-1[data-has-children="true"]');
    if (firstRow) {
        console.log('Testing first row toggle:', firstRow.dataset.id);
        window.toggleTreeRow(firstRow.dataset.id);
    } else {
        console.log('No expandable first row found');
    }
};

// Expose functions to global scope for debugging
window.debugTree = debugTree;
window.testModal = testModal;
window.testFirstRowToggle = testFirstRowToggle;
</script>
@endsection
