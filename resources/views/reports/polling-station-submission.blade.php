@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Header Section -->
            <div class="report-header">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <h1 class="h3 mb-1">Polling Station Submission Report</h1>
                        <p class="text-muted mb-0">Comprehensive analysis of vote submission status across all administrative levels</p>
                    </div>
                    <div class="d-flex gap-2">
                        <button class="btn btn-outline-primary" onclick="exportReport()">
                            <i class="bi bi-download"></i> Export
                        </button>
                        <button class="btn btn-primary" onclick="refreshReport()">
                            <i class="bi bi-arrow-clockwise"></i> Refresh
                        </button>
                    </div>
                </div>

                <!-- Filters Section -->
                <div class="card mb-4">
                    <div class="card-body">
                        <form method="GET" action="{{ route('reports.polling-station-submission') }}" class="row g-3">
                            <div class="col-md-2">
                                <label class="form-label small fw-bold">District</label>
                                <select name="district" class="form-select form-select-sm">
                                    <option value="">All Districts</option>
                                    @foreach($filterOptions['districts'] as $district)
                                        <option value="{{ $district }}" {{ $districtFilter == $district ? 'selected' : '' }}>
                                            {{ $district }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label small fw-bold">County</label>
                                <select name="county" class="form-select form-select-sm">
                                    <option value="">All Counties</option>
                                    @foreach($filterOptions['counties'] as $county)
                                        <option value="{{ $county }}" {{ $countyFilter == $county ? 'selected' : '' }}>
                                            {{ $county }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label small fw-bold">Subcounty</label>
                                <select name="subcounty" class="form-select form-select-sm">
                                    <option value="">All Subcounties</option>
                                    @foreach($filterOptions['subcounties'] as $subcounty)
                                        <option value="{{ $subcounty }}" {{ $subcountyFilter == $subcounty ? 'selected' : '' }}>
                                            {{ $subcounty }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label small fw-bold">Parish</label>
                                <select name="parish" class="form-select form-select-sm">
                                    <option value="">All Parishes</option>
                                    @foreach($filterOptions['parishes'] as $parish)
                                        <option value="{{ $parish }}" {{ $parishFilter == $parish ? 'selected' : '' }}>
                                            {{ $parish }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label small fw-bold">Date From</label>
                                <input type="date" name="date_from" class="form-control form-control-sm" value="{{ $dateFrom }}">
                            </div>
                            <div class="col-md-2">
                                <label class="form-label small fw-bold">Date To</label>
                                <input type="date" name="date_to" class="form-control form-control-sm" value="{{ $dateTo }}">
                            </div>
                            <div class="col-md-2">
                                <label class="form-label small fw-bold">View Level</label>
                                <select name="level" class="form-select form-select-sm">
                                    <option value="subcounty" {{ $level == 'subcounty' ? 'selected' : '' }}>Subcounty</option>
                                    <option value="parish" {{ $level == 'parish' ? 'selected' : '' }}>Parish</option>
                                    <option value="village" {{ $level == 'village' ? 'selected' : '' }}>Village</option>
                                    <option value="station" {{ $level == 'station' ? 'selected' : '' }}>Station</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label small fw-bold">Items Per Page</label>
                                <select name="per_page" class="form-select form-select-sm">
                                    <option value="10" {{ $perPage == 10 ? 'selected' : '' }}>10</option>
                                    <option value="25" {{ $perPage == 25 ? 'selected' : '' }}>25</option>
                                    <option value="50" {{ $perPage == 50 ? 'selected' : '' }}>50</option>
                                    <option value="100" {{ $perPage == 100 ? 'selected' : '' }}>100</option>
                                </select>
                            </div>
                            <div class="col-12">
                                <button type="submit" class="btn btn-primary btn-sm">
                                    <i class="bi bi-funnel"></i> Apply Filters
                                </button>
                                <a href="{{ route('reports.polling-station-submission') }}" class="btn btn-outline-secondary btn-sm">
                                    <i class="bi bi-x-circle"></i> Clear Filters
                                </a>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Summary Statistics -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4 class="mb-0">{{ number_format($summaryStats['total_stations']) }}</h4>
                                        <p class="mb-0">Total Stations</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="bi bi-building fs-2"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-success text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4 class="mb-0">{{ number_format($summaryStats['submitted_stations']) }}</h4>
                                        <p class="mb-0">Submitted</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="bi bi-check-circle fs-2"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-warning text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4 class="mb-0">{{ number_format($summaryStats['pending_stations']) }}</h4>
                                        <p class="mb-0">Pending</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="bi bi-clock fs-2"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-info text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4 class="mb-0">{{ $summaryStats['completion_percentage'] }}%</h4>
                                        <p class="mb-0">Completion Rate</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="bi bi-graph-up fs-2"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Additional Summary Info -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0"><i class="bi bi-people-fill"></i> Top Contributors</h6>
                            </div>
                            <div class="card-body">
                                @if(!empty($summaryStats['top_contributors']))
                                    @foreach($summaryStats['top_contributors'] as $contributor)
                                        <div class="d-flex justify-content-between align-items-center mb-2">
                                            <div>
                                                <strong>{{ $contributor['name'] }}</strong>
                                                <span class="badge bg-secondary ms-1">{{ ucfirst($contributor['type']) }}</span>
                                            </div>
                                            <div>
                                                <span class="badge bg-primary">{{ $contributor['submission_count'] }} submissions</span>
                                            </div>
                                        </div>
                                    @endforeach
                                @else
                                    <p class="text-muted mb-0">No submissions yet</p>
                                @endif
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0"><i class="bi bi-clock-history"></i> Recent Submissions</h6>
                            </div>
                            <div class="card-body">
                                @if(!empty($summaryStats['recent_submissions']))
                                    @foreach($summaryStats['recent_submissions'] as $submission)
                                        <div class="d-flex justify-content-between align-items-center mb-2">
                                            <div>
                                                <strong>{{ $submission['station_name'] }}</strong><br>
                                                <small class="text-muted">by {{ $submission['user_name'] }}</small>
                                            </div>
                                            <div class="text-end">
                                                <small class="text-muted">{{ \Carbon\Carbon::parse($submission['submission_time'])->format('M j, H:i') }}</small>
                                            </div>
                                        </div>
                                    @endforeach
                                @else
                                    <p class="text-muted mb-0">No recent submissions</p>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Report Content -->
            <div class="report-content">
                @if(empty($reportData['data']))
                    <div class="card">
                        <div class="card-body text-center py-5">
                            <i class="bi bi-inbox display-1 text-muted"></i>
                            <h4 class="mt-3">No Data Available</h4>
                            <p class="text-muted">No polling stations found matching the selected criteria.</p>
                        </div>
                    </div>
                @else
                    <div class="card">
                        <div class="card-header">
                            <div class="d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">
                                    <i class="bi bi-table"></i>
                                    {{ ucfirst($reportData['level']) }} Level Report
                                </h5>
                                <div>
                                    <span class="badge bg-info">{{ $reportData['pagination']->total() }} total items</span>
                                </div>
                            </div>
                        </div>
                        <div class="card-body p-0">
                            <div class="table-responsive">
                                <table class="table table-hover mb-0">
                                    <thead class="table-light">
                                        <tr>
                                            @if($reportData['level'] == 'station')
                                                <th>Station Name</th>
                                                <th>Location</th>
                                                <th>Status</th>
                                                <th>Last Submission</th>
                                                <th>Contributors</th>
                                                <th>Actions</th>
                                            @else
                                                <th>{{ ucfirst($reportData['level']) }} Name</th>
                                                @if($reportData['level'] != 'subcounty')
                                                    <th>Parent Location</th>
                                                @endif
                                                <th>Total Stations</th>
                                                <th>Submitted</th>
                                                <th>Pending</th>
                                                <th>Completion %</th>
                                                <th>Contributors</th>
                                                <th>Actions</th>
                                            @endif
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($reportData['data'] as $item)
                                            <tr>
                                                @if($reportData['level'] == 'station')
                                                    <td>
                                                        <strong>{{ $item['name'] }}</strong>
                                                    </td>
                                                    <td>
                                                        <small class="text-muted">
                                                            {{ $item['village'] }}, {{ $item['parish'] }}<br>
                                                            {{ $item['subcounty'] }}, {{ $item['county'] }}
                                                        </small>
                                                    </td>
                                                    <td>
                                                        <span class="badge bg-{{ $item['has_votes'] ? 'success' : 'secondary' }}">
                                                            {{ $item['submission_status'] }}
                                                        </span>
                                                    </td>
                                                    <td>
                                                        @if($item['last_submission'])
                                                            <small>{{ \Carbon\Carbon::parse($item['last_submission'])->format('M j, Y H:i') }}</small>
                                                        @else
                                                            <span class="text-muted">-</span>
                                                        @endif
                                                    </td>
                                                    <td>
                                                        @if(!empty($item['submission_users']))
                                                            @foreach(array_slice($item['submission_users'], 0, 2) as $user)
                                                                <span class="badge bg-light text-dark me-1">{{ $user['name'] }}</span>
                                                            @endforeach
                                                            @if(count($item['submission_users']) > 2)
                                                                <span class="text-muted">+{{ count($item['submission_users']) - 2 }}</span>
                                                            @endif
                                                        @else
                                                            <span class="text-muted">None</span>
                                                        @endif
                                                    </td>
                                                    <td>
                                                        <button class="btn btn-sm btn-outline-primary"
                                                                onclick="showStationDetails({{ $item['id'] }}, '{{ $item['name'] }}')">
                                                            <i class="bi bi-eye"></i> Details
                                                        </button>
                                                    </td>
                                                @else
                                                    <td>
                                                        <strong>{{ $item['name'] }}</strong>
                                                    </td>
                                                    @if($reportData['level'] != 'subcounty')
                                                        <td>
                                                            <small class="text-muted">
                                                                @if($item['grandparent_name'])
                                                                    {{ $item['grandparent_name'] }} →
                                                                @endif
                                                                {{ $item['parent_name'] }}
                                                            </small>
                                                        </td>
                                                    @endif
                                                    <td>
                                                        <span class="badge bg-primary">{{ $item['total_stations'] }}</span>
                                                    </td>
                                                    <td>
                                                        <span class="badge bg-success">{{ $item['submitted_stations'] }}</span>
                                                    </td>
                                                    <td>
                                                        <span class="badge bg-warning">{{ $item['total_stations'] - $item['submitted_stations'] }}</span>
                                                    </td>
                                                    <td>
                                                        <div class="d-flex align-items-center">
                                                            <div class="progress me-2" style="width: 60px; height: 8px;">
                                                                <div class="progress-bar bg-{{ $item['percentage'] == 100 ? 'success' : ($item['percentage'] >= 75 ? 'warning' : 'danger') }}"
                                                                     style="width: {{ $item['percentage'] }}%"></div>
                                                            </div>
                                                            <span class="badge bg-{{ $item['percentage'] == 100 ? 'success' : ($item['percentage'] >= 75 ? 'warning' : 'danger') }}">
                                                                {{ $item['percentage'] }}%
                                                            </span>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        @if(!empty($item['users']))
                                                            @foreach(array_slice($item['users'], 0, 2) as $user)
                                                                <span class="badge bg-light text-dark me-1">{{ $user['name'] }} ({{ $user['submission_count'] }})</span>
                                                            @endforeach
                                                            @if(count($item['users']) > 2)
                                                                <span class="text-muted">+{{ count($item['users']) - 2 }}</span>
                                                            @endif
                                                        @else
                                                            <span class="text-muted">None</span>
                                                        @endif
                                                    </td>
                                                    <td>
                                                        <button class="btn btn-sm btn-outline-primary"
                                                                onclick="showLocationDetails('{{ $reportData['level'] }}', '{{ $item['name'] }}', '{{ $item['parent_name'] ?? '' }}', '{{ $item['grandparent_name'] ?? '' }}')">
                                                            <i class="bi bi-eye"></i> Details
                                                        </button>
                                                    </td>
                                                @endif
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div class="card-footer">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <small class="text-muted">
                                        Showing {{ $reportData['pagination']->firstItem() ?? 0 }} to {{ $reportData['pagination']->lastItem() ?? 0 }}
                                        of {{ $reportData['pagination']->total() }} results
                                    </small>
                                </div>
                                <div>
                                    {{ $reportData['pagination']->appends(request()->query())->links() }}
                                </div>
                            </div>
                        </div>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>

<!-- Details Modal -->
<div class="modal fade" id="detailsModal" tabindex="-1" aria-labelledby="detailsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="detailsModalLabel">
                    <i class="bi bi-info-circle"></i> Details
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="modalContent">
                <div class="text-center py-4">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2">Loading details...</p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
@endsection

@section('styles')
<style>
.report-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 2rem;
    border-radius: 10px;
    margin-bottom: 2rem;
}

.table-responsive {
    border-radius: 8px;
}

.table th {
    background-color: #f8f9fa;
    border-top: none;
    font-weight: 600;
    font-size: 0.875rem;
    color: #495057;
}

.table td {
    vertical-align: middle;
    font-size: 0.875rem;
}

.table tbody tr:hover {
    background-color: #f8f9fa;
}

.progress {
    background-color: #e9ecef;
}

.badge {
    font-size: 0.75rem;
}

.modal-xl {
    max-width: 1200px;
}

.nav-tabs .nav-link {
    color: #495057;
    border: 1px solid transparent;
    border-top-left-radius: 0.375rem;
    border-top-right-radius: 0.375rem;
}

.nav-tabs .nav-link:hover {
    border-color: #e9ecef #e9ecef #dee2e6;
    isolation: isolate;
}

.nav-tabs .nav-link.active {
    color: #495057;
    background-color: #fff;
    border-color: #dee2e6 #dee2e6 #fff;
}

.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
}

.list-group-item {
    border-left: none;
    border-right: none;
}

.list-group-item:first-child {
    border-top: none;
}

.list-group-item:last-child {
    border-bottom: none;
}

.spinner-border {
    width: 3rem;
    height: 3rem;
}

@media (max-width: 768px) {
    .report-header {
        padding: 1rem;
    }

    .table-responsive {
        font-size: 0.8rem;
    }

    .modal-xl {
        max-width: 95%;
    }

    .card-body {
        padding: 1rem;
    }
}

@media (max-width: 576px) {
    .table th,
    .table td {
        padding: 0.5rem 0.25rem;
        font-size: 0.75rem;
    }

    .badge {
        font-size: 0.65rem;
    }

    .btn-sm {
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
    }
}
</style>
@endsection

@section('scripts')
<script>
function exportReport() {
    // Get current filter parameters
    const urlParams = new URLSearchParams(window.location.search);
    const exportUrl = '{{ route("reports.polling-station-submission.export") }}?' + urlParams.toString();

    // Open export URL in new window
    window.open(exportUrl, '_blank');
}

function refreshReport() {
    window.location.reload();
}

function showLocationDetails(level, name, parentName = '', grandparentName = '') {
    const modal = new bootstrap.Modal(document.getElementById('detailsModal'));
    const modalTitle = document.getElementById('detailsModalLabel');
    const modalContent = document.getElementById('modalContent');

    // Update modal title
    modalTitle.innerHTML = `<i class="bi bi-info-circle"></i> ${level.charAt(0).toUpperCase() + level.slice(1)} Details: ${name}`;

    // Show loading state
    modalContent.innerHTML = `
        <div class="text-center py-4">
            <div class="spinner-border" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-2">Loading details...</p>
        </div>
    `;

    // Show modal
    modal.show();

    // Get current filter parameters
    const urlParams = new URLSearchParams(window.location.search);
    urlParams.set('level', level);
    urlParams.set('name', name);
    if (parentName) urlParams.set('parent_name', parentName);
    if (grandparentName) urlParams.set('grandparent_name', grandparentName);

    // Fetch details
    fetch('{{ route("api.reports.location-details") }}?' + urlParams.toString())
        .then(response => response.json())
        .then(data => {
            modalContent.innerHTML = buildLocationDetailsHTML(data);
        })
        .catch(error => {
            console.error('Error fetching location details:', error);
            modalContent.innerHTML = `
                <div class="alert alert-danger">
                    <i class="bi bi-exclamation-triangle"></i>
                    Error loading details. Please try again.
                </div>
            `;
        });
}

function showStationDetails(stationId, stationName) {
    const modal = new bootstrap.Modal(document.getElementById('detailsModal'));
    const modalTitle = document.getElementById('detailsModalLabel');
    const modalContent = document.getElementById('modalContent');

    // Update modal title
    modalTitle.innerHTML = `<i class="bi bi-building"></i> Station Details: ${stationName}`;

    // Show loading state
    modalContent.innerHTML = `
        <div class="text-center py-4">
            <div class="spinner-border" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-2">Loading station details...</p>
        </div>
    `;

    // Show modal
    modal.show();

    // For now, show basic station info (can be enhanced later)
    modalContent.innerHTML = `
        <div class="alert alert-info">
            <i class="bi bi-info-circle"></i>
            Station-specific details will be implemented in the next phase.
            <br>Station ID: ${stationId}
        </div>
    `;
}

function buildLocationDetailsHTML(data) {
    const location = data.location;
    const stations = data.stations;
    const users = data.users;
    const recentSubmissions = data.recent_submissions;

    let html = `
        <!-- Location Summary -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card bg-primary text-white">
                    <div class="card-body text-center">
                        <h4>${location.total_stations}</h4>
                        <small>Total Stations</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-success text-white">
                    <div class="card-body text-center">
                        <h4>${location.submitted_stations}</h4>
                        <small>Submitted</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-warning text-white">
                    <div class="card-body text-center">
                        <h4>${location.pending_stations}</h4>
                        <small>Pending</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-info text-white">
                    <div class="card-body text-center">
                        <h4>${location.percentage}%</h4>
                        <small>Completion</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Tabs -->
        <ul class="nav nav-tabs" id="detailsTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="stations-tab" data-bs-toggle="tab" data-bs-target="#stations" type="button" role="tab">
                    <i class="bi bi-building"></i> Stations (${stations.length})
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="contributors-tab" data-bs-toggle="tab" data-bs-target="#contributors" type="button" role="tab">
                    <i class="bi bi-people"></i> Contributors (${users.length})
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="activity-tab" data-bs-toggle="tab" data-bs-target="#activity" type="button" role="tab">
                    <i class="bi bi-clock-history"></i> Recent Activity
                </button>
            </li>
        </ul>

        <div class="tab-content mt-3" id="detailsTabContent">
            <!-- Stations Tab -->
            <div class="tab-pane fade show active" id="stations" role="tabpanel">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Station Name</th>
                                <th>Status</th>
                                <th>Last Submission</th>
                                <th>Vote Count</th>
                            </tr>
                        </thead>
                        <tbody>
    `;

    stations.forEach(station => {
        const statusBadge = station.has_votes ?
            '<span class="badge bg-success">Submitted</span>' :
            '<span class="badge bg-secondary">Pending</span>';

        const lastSubmission = station.last_submission ?
            new Date(station.last_submission).toLocaleDateString() + ' ' + new Date(station.last_submission).toLocaleTimeString() :
            '-';

        html += `
            <tr>
                <td><strong>${station.name}</strong></td>
                <td>${statusBadge}</td>
                <td><small>${lastSubmission}</small></td>
                <td><span class="badge bg-primary">${station.vote_count || 0}</span></td>
            </tr>
        `;
    });

    html += `
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Contributors Tab -->
            <div class="tab-pane fade" id="contributors" role="tabpanel">
                <div class="row">
    `;

    users.forEach(user => {
        html += `
            <div class="col-md-6 mb-3">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-1">${user.name}</h6>
                                <span class="badge bg-secondary">${user.type.charAt(0).toUpperCase() + user.type.slice(1)}</span>
                            </div>
                            <div class="text-end">
                                <div class="badge bg-primary">${user.submission_count} submissions</div>
                                <div><small class="text-muted">Last: ${new Date(user.last_submission).toLocaleDateString()}</small></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    });

    html += `
                </div>
            </div>

            <!-- Activity Tab -->
            <div class="tab-pane fade" id="activity" role="tabpanel">
                <div class="list-group list-group-flush">
    `;

    recentSubmissions.forEach(submission => {
        html += `
            <div class="list-group-item">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="mb-1">${submission.station_name}</h6>
                        <small class="text-muted">Submitted by ${submission.user_name} (${submission.user_type})</small>
                    </div>
                    <small>${new Date(submission.submission_time).toLocaleDateString()} ${new Date(submission.submission_time).toLocaleTimeString()}</small>
                </div>
            </div>
        `;
    });

    html += `
                </div>
            </div>
        </div>
    `;

    return html;
}

// Auto-refresh every 5 minutes
setInterval(function() {
    const lastRefresh = localStorage.getItem('lastReportRefresh');
    const now = Date.now();

    if (!lastRefresh || (now - parseInt(lastRefresh)) > 300000) { // 5 minutes
        localStorage.setItem('lastReportRefresh', now.toString());
        // Optionally auto-refresh
        // window.location.reload();
    }
}, 60000); // Check every minute
</script>
@endsection
