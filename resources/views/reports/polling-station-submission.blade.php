@extends('layouts.app')

@section('content')
<div class="modern-dashboard">
    <!-- Hero Header -->
    <div class="hero-header">
        <div class="hero-background">
            <div class="hero-pattern"></div>
            <div class="hero-gradient"></div>
        </div>
        <div class="hero-content">
            <div class="hero-main">
                <div class="hero-badge">
                    <i class="bi bi-shield-check"></i>
                    <span>Live Report</span>
                </div>
                <h1 class="hero-title">
                    Polling Station
                    <span class="hero-highlight">Submission Analytics</span>
                </h1>
                <p class="hero-description">
                    Real-time monitoring and comprehensive analysis of vote submission status across all administrative levels
                </p>
                <div class="hero-stats">
                    <div class="hero-stat">
                        <div class="hero-stat-number">{{ number_format($summaryStats['total_stations']) }}</div>
                        <div class="hero-stat-label">Total Stations</div>
                    </div>
                    <div class="hero-stat">
                        <div class="hero-stat-number">{{ $summaryStats['completion_percentage'] }}%</div>
                        <div class="hero-stat-label">Completion Rate</div>
                    </div>
                    <div class="hero-stat">
                        <div class="hero-stat-number">{{ number_format($summaryStats['submitted_stations']) }}</div>
                        <div class="hero-stat-label">Submitted</div>
                    </div>
                </div>
            </div>
            <div class="hero-actions">
                <button class="hero-btn hero-btn-primary" onclick="exportReport()">
                    <i class="bi bi-download"></i>
                    <span>Export Report</span>
                </button>
                <button class="hero-btn hero-btn-secondary" onclick="refreshReport()">
                    <i class="bi bi-arrow-clockwise"></i>
                    <span>Refresh Data</span>
                </button>
                <button class="hero-btn hero-btn-outline" onclick="exportReport()">
                    <i class="bi bi-download"></i>
                    <span>Export</span>
                </button>
            </div>
        </div>
    </div>

    <!-- Dashboard Content -->
    <div class="dashboard-content">
        <!-- Smart Filters Panel -->
        <div class="smart-filters">
            <div class="filters-container">
                <div class="filters-header">
                    <div class="filters-title">
                        <i class="bi bi-sliders"></i>
                        <span>Smart Filters</span>
                    </div>
                    <div class="filters-toggle">
                        <button class="toggle-btn" onclick="toggleFilters()">
                            <i class="bi bi-chevron-down"></i>
                        </button>
                    </div>
                </div>
                <div class="filters-content" id="filtersContent">
                    <form method="GET" action="{{ route('reports.polling-station-submission') }}" class="modern-form">
                        <div class="form-grid">
                            <!-- Location Filters -->
                            <div class="form-section">
                                <h6 class="section-title">
                                    <i class="bi bi-geo-alt-fill"></i>
                                    Location Filters
                                </h6>
                                <div class="form-row">
                                    <div class="form-field">
                                        <label class="field-label">District</label>
                                        <div class="field-wrapper">
                                            <select name="district" class="modern-select">
                                                <option value="">All Districts</option>
                                                @foreach($filterOptions['districts'] as $district)
                                                    <option value="{{ $district }}" {{ $districtFilter == $district ? 'selected' : '' }}>
                                                        {{ $district }}
                                                    </option>
                                                @endforeach
                                            </select>
                                            <i class="bi bi-chevron-down field-icon"></i>
                                        </div>
                                    </div>
                                    <div class="form-field">
                                        <label class="field-label">County</label>
                                        <div class="field-wrapper">
                                            <select name="county" class="modern-select">
                                                <option value="">All Counties</option>
                                                @foreach($filterOptions['counties'] as $county)
                                                    <option value="{{ $county }}" {{ $countyFilter == $county ? 'selected' : '' }}>
                                                        {{ $county }}
                                                    </option>
                                                @endforeach
                                            </select>
                                            <i class="bi bi-chevron-down field-icon"></i>
                                        </div>
                                    </div>
                                    <div class="form-field">
                                        <label class="field-label">Subcounty</label>
                                        <div class="field-wrapper">
                                            <select name="subcounty" class="modern-select">
                                                <option value="">All Subcounties</option>
                                                @foreach($filterOptions['subcounties'] as $subcounty)
                                                    <option value="{{ $subcounty }}" {{ $subcountyFilter == $subcounty ? 'selected' : '' }}>
                                                        {{ $subcounty }}
                                                    </option>
                                                @endforeach
                                            </select>
                                            <i class="bi bi-chevron-down field-icon"></i>
                                        </div>
                                    </div>
                                    <div class="form-field">
                                        <label class="field-label">Parish</label>
                                        <div class="field-wrapper">
                                            <select name="parish" class="modern-select">
                                                <option value="">All Parishes</option>
                                                @foreach($filterOptions['parishes'] as $parish)
                                                    <option value="{{ $parish }}" {{ $parishFilter == $parish ? 'selected' : '' }}>
                                                        {{ $parish }}
                                                    </option>
                                                @endforeach
                                            </select>
                                            <i class="bi bi-chevron-down field-icon"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Date & Settings -->
                            <div class="form-section">
                                <h6 class="section-title">
                                    <i class="bi bi-calendar-range"></i>
                                    Date Range & Settings
                                </h6>
                                <div class="form-row">
                                    <div class="form-field">
                                        <label class="field-label">From Date</label>
                                        <div class="field-wrapper">
                                            <input type="date" name="date_from" class="modern-input" value="{{ $dateFrom }}">
                                            <i class="bi bi-calendar-event field-icon"></i>
                                        </div>
                                    </div>
                                    <div class="form-field">
                                        <label class="field-label">To Date</label>
                                        <div class="field-wrapper">
                                            <input type="date" name="date_to" class="modern-input" value="{{ $dateTo }}">
                                            <i class="bi bi-calendar-check field-icon"></i>
                                        </div>
                                    </div>
                                    <div class="form-field">
                                        <label class="field-label">Items Per Page</label>
                                        <div class="field-wrapper">
                                            <select name="per_page" class="modern-select">
                                                <option value="10" {{ $perPage == 10 ? 'selected' : '' }}>10 items</option>
                                                <option value="25" {{ $perPage == 25 ? 'selected' : '' }}>25 items</option>
                                                <option value="50" {{ $perPage == 50 ? 'selected' : '' }}>50 items</option>
                                                <option value="100" {{ $perPage == 100 ? 'selected' : '' }}>100 items</option>
                                            </select>
                                            <i class="bi bi-chevron-down field-icon"></i>
                                        </div>
                                    </div>
                                    <div class="form-field form-actions">
                                        <div class="action-buttons">
                                            <button type="submit" class="modern-btn modern-btn-primary">
                                                <i class="bi bi-funnel"></i>
                                                <span>Apply Filters</span>
                                            </button>
                                            <a href="{{ route('reports.polling-station-submission') }}" class="modern-btn modern-btn-outline">
                                                <i class="bi bi-arrow-counterclockwise"></i>
                                                <span>Reset All</span>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Analytics Dashboard -->
        <div class="analytics-dashboard">
            <!-- Key Metrics -->
            <div class="metrics-grid">
                <div class="metric-card metric-primary">
                    <div class="metric-header">
                        <div class="metric-icon">
                            <i class="bi bi-building-fill"></i>
                        </div>
                        <div class="metric-trend positive">
                            <i class="bi bi-trending-up"></i>
                            <span>+12%</span>
                        </div>
                    </div>
                    <div class="metric-body">
                        <div class="metric-value">{{ number_format($summaryStats['total_stations']) }}</div>
                        <div class="metric-label">Total Polling Stations</div>
                        <div class="metric-subtitle">Across all regions</div>
                    </div>
                    <div class="metric-footer">
                        <div class="metric-progress">
                            <div class="progress-bar" style="width: 100%"></div>
                        </div>
                    </div>
                </div>

                <div class="metric-card metric-success">
                    <div class="metric-header">
                        <div class="metric-icon">
                            <i class="bi bi-check-circle-fill"></i>
                        </div>
                        <div class="metric-trend positive">
                            <i class="bi bi-trending-up"></i>
                            <span>+{{ $summaryStats['completion_percentage'] }}%</span>
                        </div>
                    </div>
                    <div class="metric-body">
                        <div class="metric-value">{{ number_format($summaryStats['submitted_stations']) }}</div>
                        <div class="metric-label">Submissions Completed</div>
                        <div class="metric-subtitle">Successfully submitted</div>
                    </div>
                    <div class="metric-footer">
                        <div class="metric-progress">
                            <div class="progress-bar" style="width: {{ $summaryStats['completion_percentage'] }}%"></div>
                        </div>
                    </div>
                </div>

                <div class="metric-card metric-warning">
                    <div class="metric-header">
                        <div class="metric-icon">
                            <i class="bi bi-clock-fill"></i>
                        </div>
                        <div class="metric-trend neutral">
                            <i class="bi bi-dash"></i>
                            <span>{{ 100 - $summaryStats['completion_percentage'] }}%</span>
                        </div>
                    </div>
                    <div class="metric-body">
                        <div class="metric-value">{{ number_format($summaryStats['pending_stations']) }}</div>
                        <div class="metric-label">Pending Submissions</div>
                        <div class="metric-subtitle">Awaiting completion</div>
                    </div>
                    <div class="metric-footer">
                        <div class="metric-progress">
                            <div class="progress-bar" style="width: {{ 100 - $summaryStats['completion_percentage'] }}%"></div>
                        </div>
                    </div>
                </div>

                <div class="metric-card metric-info">
                    <div class="metric-header">
                        <div class="metric-icon">
                            <i class="bi bi-graph-up-arrow"></i>
                        </div>
                        <div class="metric-trend {{ $summaryStats['completion_percentage'] >= 75 ? 'positive' : 'neutral' }}">
                            <i class="bi bi-{{ $summaryStats['completion_percentage'] >= 75 ? 'trending-up' : 'dash' }}"></i>
                            <span>{{ $summaryStats['completion_percentage'] }}%</span>
                        </div>
                    </div>
                    <div class="metric-body">
                        <div class="metric-value">{{ $summaryStats['completion_percentage'] }}%</div>
                        <div class="metric-label">Completion Rate</div>
                        <div class="metric-subtitle">Overall progress</div>
                    </div>
                    <div class="metric-footer">
                        <div class="circular-progress" data-percentage="{{ $summaryStats['completion_percentage'] }}">
                            <svg class="circular-chart" viewBox="0 0 36 36">
                                <path class="circle-bg" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"/>
                                <path class="circle" stroke-dasharray="{{ $summaryStats['completion_percentage'] }}, 100" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"/>
                            </svg>
                            <div class="percentage">{{ $summaryStats['completion_percentage'] }}%</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Insights Panel Removed for Maximum Compactness -->
        </div>
            </div>

        <!-- Data Explorer -->
        <div class="data-explorer">
            @if(empty($reportData['data']))
                <div class="empty-state-modern">
                    <div class="empty-illustration">
                        <i class="bi bi-database-x"></i>
                    </div>
                    <div class="empty-content">
                        <h3 class="empty-title">No Data Available</h3>
                        <p class="empty-description">No polling stations found matching the selected criteria. Try adjusting your filters or check back later.</p>
                        <button class="empty-action" onclick="location.reload()">
                            <i class="bi bi-arrow-clockwise"></i>
                            <span>Refresh Data</span>
                        </button>
                    </div>
                </div>
            @else
                <div class="explorer-container">
                    <div class="explorer-header-compact">
                        <div class="compact-title">
                            <div class="compact-icon">
                                <i class="bi bi-table"></i>
                            </div>
                            <div class="compact-content">
                                <h3 class="compact-main">Data Explorer</h3>
                                <span class="compact-count">{{ count($reportData['data']) }} subcounties</span>
                            </div>
                        </div>
                        <div class="compact-controls">
                            <div class="modern-view-controls">
                                <div class="controls-header">
                                    <div class="controls-title">
                                        <i class="bi bi-sliders"></i>
                                        <span>View Controls</span>
                                    </div>
                                    <div class="controls-badge">
                                        <span class="badge-text">Interactive</span>
                                    </div>
                                </div>
                                <div class="controls-actions">
                                    <div class="action-group">
                                        <button class="modern-control-btn expand-btn" onclick="expandAllRows()" title="Expand All (Ctrl+E)">
                                            <div class="btn-icon">
                                                <i class="bi bi-arrows-expand"></i>
                                            </div>
                                            <div class="btn-content">
                                                <span class="btn-label">Expand All</span>
                                                <span class="btn-shortcut">Ctrl+E</span>
                                            </div>
                                        </button>
                                        <button class="modern-control-btn collapse-btn" onclick="collapseAllRows()" title="Collapse All (Ctrl+C)">
                                            <div class="btn-icon">
                                                <i class="bi bi-arrows-collapse"></i>
                                            </div>
                                            <div class="btn-content">
                                                <span class="btn-label">Collapse All</span>
                                                <span class="btn-shortcut">Ctrl+C</span>
                                            </div>
                                        </button>
                                    </div>
                                    <div class="view-options">
                                        <div class="option-item">
                                            <div class="option-icon">
                                                <i class="bi bi-eye"></i>
                                            </div>
                                            <div class="option-text">
                                                <span class="option-value">{{ count($reportData['data']) }}</span>
                                                <span class="option-label">Visible</span>
                                            </div>
                                        </div>
                                        <div class="option-divider"></div>
                                        <div class="option-item">
                                            <div class="option-icon">
                                                <i class="bi bi-layers"></i>
                                            </div>
                                            <div class="option-text">
                                                <span class="option-value">3</span>
                                                <span class="option-label">Levels</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="explorer-summary">
                                <div class="summary-card">
                                    <div class="summary-icon">
                                        <i class="bi bi-map"></i>
                                    </div>
                                    <div class="summary-content">
                                        <div class="summary-value">{{ $reportData['pagination']->total() }}</div>
                                        <div class="summary-label">Subcounties</div>
                                        <div class="summary-subtitle">Administrative areas</div>
                                    </div>
                                    <div class="summary-progress">
                                        @php
                                            $totalStations = collect($reportData['data'])->sum('total_stations');
                                            $submittedStations = collect($reportData['data'])->sum('submitted_stations');
                                            $progressPercentage = $totalStations > 0 ? ($submittedStations / $totalStations) * 100 : 0;
                                        @endphp
                                        <div class="progress-ring">
                                            <svg width="48" height="48" viewBox="0 0 48 48">
                                                <circle cx="24" cy="24" r="20" fill="none" stroke="#e2e8f0" stroke-width="4"/>
                                                <circle cx="24" cy="24" r="20" fill="none" stroke="#667eea" stroke-width="4"
                                                        stroke-dasharray="{{ 2 * pi() * 20 }}"
                                                        stroke-dashoffset="{{ 2 * pi() * 20 * (1 - $progressPercentage / 100) }}"
                                                        stroke-linecap="round"
                                                        transform="rotate(-90 24 24)"/>
                                            </svg>
                                            <div class="progress-text">{{ number_format($progressPercentage, 0) }}%</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="explorer-content">
                        <div class="data-table-modern">
                            <table class="modern-tree-table" id="hierarchicalTable">
                                <thead class="table-head-modern">
                                    <tr class="header-row">
                                        <th class="col-location">
                                            <div class="column-header">
                                                <div class="header-icon">
                                                    <i class="bi bi-geo-alt-fill"></i>
                                                </div>
                                                <div class="header-text">
                                                    <span class="header-title">Location</span>
                                                    <span class="header-subtitle">Administrative area</span>
                                                </div>
                                            </div>
                                        </th>
                                        <th class="col-metrics">
                                            <div class="column-header">
                                                <div class="header-icon">
                                                    <i class="bi bi-bar-chart-fill"></i>
                                                </div>
                                                <div class="header-text">
                                                    <span class="header-title">Metrics</span>
                                                    <span class="header-subtitle">Station counts</span>
                                                </div>
                                            </div>
                                        </th>
                                        <th class="col-progress">
                                            <div class="column-header">
                                                <div class="header-icon">
                                                    <i class="bi bi-graph-up-arrow"></i>
                                                </div>
                                                <div class="header-text">
                                                    <span class="header-title">Progress</span>
                                                    <span class="header-subtitle">Completion status</span>
                                                </div>
                                            </div>
                                        </th>
                                        <th class="col-actions">
                                            <div class="column-header">
                                                <div class="header-icon">
                                                    <i class="bi bi-three-dots"></i>
                                                </div>
                                                <div class="header-text">
                                                    <span class="header-title">Actions</span>
                                                    <span class="header-subtitle">View details</span>
                                                </div>
                                            </div>
                                        </th>
                                    </tr>
                                </thead>
                                <tbody class="table-body-modern" id="treeTableBody">
                                    @foreach($reportData['data'] as $subcounty)
                                        <tr class="data-row modern-row level-1"
                                            data-id="{{ $subcounty['id'] }}"
                                            data-level="1"
                                            data-type="{{ $subcounty['type'] }}"
                                            data-name="{{ $subcounty['name'] }}"
                                            data-has-children="{{ $subcounty['has_children'] ? 'true' : 'false' }}"
                                            data-expanded="false">

                                            <!-- Location Column -->
                                            <td class="location-column">
                                                <div class="location-container">
                                                    <div class="location-controls">
                                                        @if($subcounty['has_children'])
                                                            <button class="expand-btn" onclick="toggleTreeRow('{{ $subcounty['id'] }}')">
                                                                <i class="bi bi-chevron-right"></i>
                                                            </button>
                                                        @else
                                                            <div class="expand-spacer"></div>
                                                        @endif
                                                    </div>
                                                    <div class="location-info-modern">
                                                        <div class="location-avatar">
                                                            <i class="bi bi-geo-alt-fill"></i>
                                                        </div>
                                                        <div class="location-details-modern">
                                                            <div class="location-name-modern">{{ $subcounty['name'] }}</div>
                                                            <div class="location-meta">
                                                                <span class="location-type-modern">Subcounty</span>
                                                                <span class="location-separator">•</span>
                                                                <span class="location-level">Level 1</span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </td>

                                            <!-- Metrics Column -->
                                            <td class="metrics-column">
                                                <div class="metrics-container">
                                                    <div class="metric-item metric-total">
                                                        <div class="metric-icon">
                                                            <i class="bi bi-building"></i>
                                                        </div>
                                                        <div class="metric-content">
                                                            <div class="metric-value">{{ $subcounty['total_stations'] }}</div>
                                                            <div class="metric-label">Total</div>
                                                        </div>
                                                    </div>
                                                    <div class="metric-item metric-submitted">
                                                        <div class="metric-icon">
                                                            <i class="bi bi-check-circle"></i>
                                                        </div>
                                                        <div class="metric-content">
                                                            <div class="metric-value">{{ $subcounty['submitted_stations'] }}</div>
                                                            <div class="metric-label">Submitted</div>
                                                        </div>
                                                    </div>
                                                    <div class="metric-item metric-pending">
                                                        <div class="metric-icon">
                                                            <i class="bi bi-clock"></i>
                                                        </div>
                                                        <div class="metric-content">
                                                            <div class="metric-value">{{ $subcounty['total_stations'] - $subcounty['submitted_stations'] }}</div>
                                                            <div class="metric-label">Pending</div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </td>

                                            <!-- Progress Column -->
                                            <td class="progress-column">
                                                <div class="progress-container">
                                                    <div class="progress-visual">
                                                        <div class="progress-circle-modern">
                                                            <svg class="circle-chart" viewBox="0 0 36 36">
                                                                <path class="circle-bg" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"/>
                                                                <path class="circle-progress circle-{{ $subcounty['percentage'] >= 75 ? 'high' : ($subcounty['percentage'] >= 50 ? 'medium' : 'low') }}"
                                                                      stroke-dasharray="{{ $subcounty['percentage'] }}, 100"
                                                                      d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"/>
                                                            </svg>
                                                            <div class="circle-text">{{ $subcounty['percentage'] }}%</div>
                                                        </div>
                                                    </div>
                                                    <div class="progress-details">
                                                        <div class="progress-percentage">{{ $subcounty['percentage'] }}%</div>
                                                        <div class="progress-status">
                                                            @if($subcounty['percentage'] == 100)
                                                                <span class="status-complete">Complete</span>
                                                            @elseif($subcounty['percentage'] >= 75)
                                                                <span class="status-good">Good Progress</span>
                                                            @elseif($subcounty['percentage'] >= 50)
                                                                <span class="status-moderate">Moderate</span>
                                                            @else
                                                                <span class="status-low">Needs Attention</span>
                                                            @endif
                                                        </div>
                                                    </div>
                                                </div>
                                            </td>

                                            <!-- Actions Column -->
                                            <td class="actions-column">
                                                <div class="actions-container">
                                                    <button class="action-btn action-primary"
                                                            onclick="showLocationDetails('subcounty', '{{ $subcounty['name'] }}', '', '')"
                                                            title="View Details">
                                                        <i class="bi bi-eye"></i>
                                                        <span>View Details</span>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Modern Pagination -->
                    <div class="pagination-modern">
                        <div class="pagination-container">
                            <div class="pagination-info-modern">
                                <div class="info-stats">
                                    <div class="stats-icon">
                                        <i class="bi bi-list-ul"></i>
                                    </div>
                                    <div class="stats-content">
                                        <div class="stats-primary">
                                            Showing {{ $reportData['pagination']->firstItem() ?? 0 }} to {{ $reportData['pagination']->lastItem() ?? 0 }}
                                            of {{ $reportData['pagination']->total() }} subcounties
                                        </div>
                                        <div class="stats-secondary">
                                            Page {{ $reportData['pagination']->currentPage() }} of {{ $reportData['pagination']->lastPage() }}
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="pagination-controls-modern">
                                <div class="page-size-control">
                                    <label class="control-label">Items per page</label>
                                    <select class="page-size-select" onchange="changePageSize(this.value)">
                                        <option value="10" {{ $perPage == 10 ? 'selected' : '' }}>10</option>
                                        <option value="25" {{ $perPage == 25 ? 'selected' : '' }}>25</option>
                                        <option value="50" {{ $perPage == 50 ? 'selected' : '' }}>50</option>
                                        <option value="100" {{ $perPage == 100 ? 'selected' : '' }}>100</option>
                                    </select>
                                </div>

                                <div class="pagination-nav">
                                    {{ $reportData['pagination']->appends(request()->query())->links() }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            @endif
        </div>
    </div>
</div>

<!-- Modern Details Modal -->
<div class="modal fade modern-modal" id="detailsModal" tabindex="-1" aria-labelledby="detailsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content modern-modal-content">
            <div class="modal-header modern-modal-header">
                <div class="modal-header-content">
                    <div class="modal-icon-modern">
                        <i class="bi bi-info-circle-fill"></i>
                    </div>
                    <div class="modal-title-modern">
                        <h4 class="modal-title" id="detailsModalLabel">Location Details</h4>
                        <p class="modal-subtitle">Comprehensive analysis and statistics</p>
                    </div>
                </div>
                <button type="button" class="modal-close-modern" data-bs-dismiss="modal" aria-label="Close">
                    <i class="bi bi-x"></i>
                </button>
            </div>
            <div class="modal-body modern-modal-body" id="modalContent">
                <div class="loading-state-modern">
                    <div class="loading-animation">
                        <div class="loading-dots">
                            <div class="dot"></div>
                            <div class="dot"></div>
                            <div class="dot"></div>
                        </div>
                    </div>
                    <div class="loading-content">
                        <h5 class="loading-title">Loading Details</h5>
                        <p class="loading-description">Fetching comprehensive data and analytics...</p>
                    </div>
                </div>
            </div>
            <div class="modal-footer modern-modal-footer">
                <button type="button" class="modal-btn modal-btn-secondary" data-bs-dismiss="modal">
                    <i class="bi bi-x-circle"></i>
                    <span>Close</span>
                </button>
                <button type="button" class="modal-btn modal-btn-primary" onclick="exportLocationData()">
                    <i class="bi bi-download"></i>
                    <span>Export Data</span>
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Inline JavaScript to ensure functions are available immediately -->
<script>
// Global variables for tree state management
window.expandedRows = new Set();
window.loadedChildren = new Map();

// Make functions globally accessible
window.exportReport = function() {
    const urlParams = new URLSearchParams(window.location.search);
    const exportUrl = '{{ route("reports.polling-station-submission.export") }}?' + urlParams.toString();
    window.open(exportUrl, '_blank');
};

window.refreshReport = function() {
    window.location.reload();
};

// Initialize tree table functionality
window.initializeTreeTable = function() {
    console.log('Initializing tree table...');

    // Initialize expanded rows set
    if (!window.expandedRows) {
        window.expandedRows = new Set();
    }

    // Add click event listeners to all expand buttons
    const expandButtons = document.querySelectorAll('.expand-btn, .tree-toggle, .enhanced-toggle');
    console.log('Found expand buttons:', expandButtons.length);

    expandButtons.forEach(button => {
        // Remove existing listeners to prevent duplicates
        button.removeEventListener('click', handleExpandClick);
        // Add new listener
        button.addEventListener('click', handleExpandClick);
    });

    console.log('Tree table initialized successfully');
};

// Handle expand button clicks
function handleExpandClick(event) {
    event.preventDefault();
    event.stopPropagation();

    const button = event.currentTarget;
    const row = button.closest('tr');

    if (row && row.dataset.id) {
        console.log('Expand button clicked for row:', row.dataset.id);
        window.toggleTreeRow(row.dataset.id);
    } else {
        console.error('Could not find row data for expand button');
    }
}

window.expandAllRows = function() {
    console.log('Expanding all rows');
    // Look for both old and new row classes for compatibility
    const level1Rows = document.querySelectorAll('.data-row.level-1[data-has-children="true"], .tree-row.level-1[data-has-children="true"], .modern-row.level-1[data-has-children="true"]');
    console.log('Found level 1 rows with children:', level1Rows.length);

    level1Rows.forEach((row, index) => {
        console.log(`Processing row ${index + 1}:`, row.dataset.id, row.dataset.expanded);
        if (row.dataset.expanded === 'false') {
            setTimeout(() => {
                window.toggleTreeRow(row.dataset.id);
            }, index * 100);
        }
    });
};

window.collapseAllRows = function() {
    console.log('Collapsing all rows');
    // Look for both old and new row classes for compatibility
    const expandedRows = document.querySelectorAll('.data-row[data-expanded="true"], .tree-row[data-expanded="true"], .modern-row[data-expanded="true"]');
    console.log('Found expanded rows:', expandedRows.length);

    expandedRows.forEach((row, index) => {
        console.log(`Collapsing row ${index + 1}:`, row.dataset.id);
        setTimeout(() => {
            window.toggleTreeRow(row.dataset.id);
        }, index * 50);
    });
};

// Debug functions removed - tools section eliminated

window.toggleTreeRow = function(rowId) {
    console.log('Toggling modern tree row:', rowId);

    const row = document.querySelector(`tr[data-id="${rowId}"]`);
    if (!row) {
        console.error('Row not found:', rowId);
        return;
    }

    // Look for both old and new button classes for compatibility
    const toggleBtn = row.querySelector('.expand-btn, .tree-toggle, .enhanced-toggle');
    if (!toggleBtn) {
        console.error('Toggle button not found for row:', rowId);
        return;
    }

    const icon = toggleBtn.querySelector('i');
    if (!icon) {
        console.error('Icon not found for row:', rowId);
        return;
    }

    const isExpanded = row.dataset.expanded === 'true';
    console.log('Current expanded state:', isExpanded);

    if (isExpanded) {
        // Collapse the row
        window.collapseRow(rowId);
        row.dataset.expanded = 'false';
        icon.className = 'bi bi-chevron-right';
        toggleBtn.classList.remove('expanded');
        if (window.expandedRows) {
            window.expandedRows.delete(rowId);
        }
        console.log('Row collapsed:', rowId);
    } else {
        // Expand the row
        window.expandRow(rowId);
        row.dataset.expanded = 'true';
        icon.className = 'bi bi-chevron-down';
        toggleBtn.classList.add('expanded');
        if (!window.expandedRows) {
            window.expandedRows = new Set();
        }
        window.expandedRows.add(rowId);
        console.log('Row expanded:', rowId);
    }
};

window.showLocationDetails = function(level, name, parentName = '', grandparentName = '') {
    console.log('Showing location details for:', level, name, parentName, grandparentName);

    const modalElement = document.getElementById('detailsModal');
    const modalTitle = document.getElementById('detailsModalLabel');
    const modalContent = document.getElementById('modalContent');

    if (!modalElement || !modalTitle || !modalContent) {
        console.error('Modal elements not found');
        return;
    }

    modalTitle.innerHTML = `<i class="bi bi-info-circle"></i> ${level.charAt(0).toUpperCase() + level.slice(1)} Details: ${name}`;

    modalContent.innerHTML = `
        <div class="text-center py-4">
            <div class="spinner-border" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-2">Loading details...</p>
        </div>
    `;

    try {
        if (typeof bootstrap !== 'undefined' && bootstrap.Modal) {
            const modal = new bootstrap.Modal(modalElement);
            modal.show();
            console.log('Modal shown using Bootstrap 5 Modal class');
        } else if (typeof $ !== 'undefined') {
            $(modalElement).modal('show');
            console.log('Modal shown using jQuery');
        } else {
            window.showModalManually(modalElement);
        }
    } catch (error) {
        console.error('Error showing modal:', error);
        return;
    }

    const urlParams = new URLSearchParams(window.location.search);
    urlParams.set('level', level);
    urlParams.set('name', name);
    if (parentName) urlParams.set('parent_name', parentName);
    if (grandparentName) urlParams.set('grandparent_name', grandparentName);

    const apiUrl = '{{ route("api.reports.location-details") }}?' + urlParams.toString();
    console.log('Fetching from:', apiUrl);

    fetch(apiUrl)
        .then(response => {
            console.log('Response status:', response.status);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('Received data:', data);
            modalContent.innerHTML = window.buildLocationDetailsHTML(data);
        })
        .catch(error => {
            console.error('Error fetching location details:', error);
            modalContent.innerHTML = `
                <div class="alert alert-danger">
                    <i class="bi bi-exclamation-triangle"></i>
                    Error loading details: ${error.message}
                    <br><small>Please check the console for more details.</small>
                </div>
            `;
        });
};

window.showModalManually = function(modalElement) {
    modalElement.classList.add('show');
    modalElement.style.display = 'block';
    modalElement.setAttribute('aria-hidden', 'false');
    document.body.classList.add('modal-open');

    let backdrop = document.getElementById('modal-backdrop');
    if (!backdrop) {
        backdrop = document.createElement('div');
        backdrop.className = 'modal-backdrop fade show';
        backdrop.id = 'modal-backdrop';
        document.body.appendChild(backdrop);
    }

    console.log('Modal shown manually');
};

window.hideModalManually = function() {
    const modalElement = document.getElementById('detailsModal');
    const backdrop = document.getElementById('modal-backdrop');

    if (modalElement) {
        modalElement.classList.remove('show');
        modalElement.style.display = 'none';
        modalElement.setAttribute('aria-hidden', 'true');
    }

    if (backdrop) {
        backdrop.remove();
    }

    document.body.classList.remove('modal-open');
    console.log('Modal hidden manually');
};

window.expandRow = function(rowId) {
    console.log('Expanding row:', rowId);

    const row = document.querySelector(`tr[data-id="${rowId}"]`);
    if (!row) {
        console.error('Row not found for expansion:', rowId);
        return;
    }

    const rowType = row.dataset.type;
    const rowName = row.dataset.name;

    console.log('Row details:', { type: rowType, name: rowName });

    if (window.loadedChildren.has(rowId)) {
        console.log('Children already loaded, showing existing children');
        window.showChildRows(rowId);
        return;
    }

    window.showLoadingState(row);

    const urlParams = new URLSearchParams(window.location.search);
    urlParams.set('parent_id', rowId);
    urlParams.set('parent_type', rowType);
    urlParams.set('parent_name', rowName);

    if (rowType === 'parish') {
        const subcountyRow = window.findParentRow(row, 'subcounty');
        if (subcountyRow) {
            urlParams.set('grandparent_name', subcountyRow.dataset.name);
            console.log('Added grandparent (subcounty):', subcountyRow.dataset.name);
        }
    } else if (rowType === 'village') {
        const parishRow = window.findParentRow(row, 'parish');
        const subcountyRow = window.findParentRow(row, 'subcounty');
        if (parishRow) {
            urlParams.set('grandparent_name', parishRow.dataset.name);
            console.log('Added grandparent (parish):', parishRow.dataset.name);
        }
        if (subcountyRow) {
            urlParams.set('great_grandparent_name', subcountyRow.dataset.name);
            console.log('Added great-grandparent (subcounty):', subcountyRow.dataset.name);
        }
    }

    const apiUrl = '{{ route("api.reports.tree-child-data") }}?' + urlParams.toString();
    console.log('Fetching child data from:', apiUrl);

    fetch(apiUrl)
        .then(response => {
            console.log('API response status:', response.status);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('Received child data:', data);
            window.hideLoadingState(row);

            if (data.children && data.children.length > 0) {
                window.insertChildRows(rowId, data.children);
                window.loadedChildren.set(rowId, data.children);
                console.log('Children inserted and cached for row:', rowId);
            } else {
                console.log('No children found for row:', rowId);
                row.dataset.hasChildren = 'false';
                const toggleBtn = row.querySelector('.tree-toggle');
                if (toggleBtn) {
                    toggleBtn.style.display = 'none';
                }
            }
        })
        .catch(error => {
            console.error('Error loading child data:', error);
            window.hideLoadingState(row);
            window.showErrorState(row);
        });
};

window.collapseRow = function(rowId) {
    window.hideChildRows(rowId);
};

window.showChildRows = function(parentId) {
    const children = window.loadedChildren.get(parentId);
    if (!children) return;

    children.forEach(child => {
        const childRow = document.querySelector(`tr[data-id="${child.id}"]`);
        if (childRow) {
            childRow.classList.remove('hidden');
            childRow.classList.add('expanding');

            if (window.expandedRows.has(child.id)) {
                window.showChildRows(child.id);
            }
        }
    });
};

window.hideChildRows = function(parentId) {
    const children = window.loadedChildren.get(parentId);
    if (!children) return;

    children.forEach(child => {
        const childRow = document.querySelector(`tr[data-id="${child.id}"]`);
        if (childRow) {
            childRow.classList.add('hidden');
            childRow.classList.remove('expanding');

            window.hideChildRows(child.id);
        }
    });
};

window.insertChildRows = function(parentId, children) {
    console.log('Inserting child rows for parent:', parentId, 'Children count:', children.length);

    const parentRow = document.querySelector(`tr[data-id="${parentId}"]`);
    if (!parentRow) {
        console.error('Parent row not found:', parentId);
        return;
    }

    const tbody = parentRow.parentNode;
    let insertAfter = parentRow;

    children.forEach((child, index) => {
        console.log(`Processing child ${index + 1}:`, child.id, child.name);

        let existingRow = document.querySelector(`tr[data-id="${child.id}"]`);
        if (existingRow) {
            console.log('Row already exists, showing it:', child.id);
            existingRow.classList.remove('hidden');
            insertAfter = existingRow;
            return;
        }

        try {
            const childRow = window.createTreeRow(child);
            insertAfter.insertAdjacentElement('afterend', childRow);
            insertAfter = childRow;
            console.log('Child row inserted:', child.id);
        } catch (error) {
            console.error('Error creating child row:', error, child);
        }
    });

    console.log('Finished inserting child rows for parent:', parentId);
};

window.createTreeRow = function(data) {
    const row = document.createElement('tr');
    row.className = `tree-row level-${data.level} expanding`;
    row.dataset.id = data.id;
    row.dataset.level = data.level;
    row.dataset.type = data.type;
    row.dataset.name = data.name;
    row.dataset.hasChildren = data.has_children ? 'true' : 'false';
    row.dataset.expanded = 'false';

    let html = '';

    if (data.type === 'station') {
        const statusBadge = data.has_votes ?
            '<span class="badge bg-success">Submitted</span>' :
            '<span class="badge bg-secondary">Pending</span>';

        const lastSubmission = data.last_submission ?
            new Date(data.last_submission).toLocaleDateString() + ' ' + new Date(data.last_submission).toLocaleTimeString() :
            '<span class="text-muted">-</span>';

        const contributors = data.submission_users && data.submission_users.length > 0 ?
            data.submission_users.slice(0, 2).map(user => `<span class="badge bg-light text-dark me-1">${user.name}</span>`).join('') +
            (data.submission_users.length > 2 ? `<span class="text-muted">+${data.submission_users.length - 2}</span>` : '') :
            '<span class="text-muted">None</span>';

        html = `
            <td class="tree-cell">
                <div class="tree-content level-${data.level}">
                    <span class="tree-spacer me-2"></span>
                    <i class="bi bi-building text-muted me-2"></i>
                    ${data.name}
                    <small class="text-muted ms-2">(Station)</small>
                </div>
            </td>
            <td colspan="3">${statusBadge}</td>
            <td><small>${lastSubmission}</small></td>
            <td>
                <button class="btn btn-sm btn-outline-primary" onclick="window.showStationDetails('${data.id}', '${data.name}')">
                    <i class="bi bi-eye"></i>
                </button>
            </td>
        `;
    } else {
        const toggleButton = data.has_children ?
            `<button class="tree-toggle btn btn-sm p-0 me-2" onclick="window.toggleTreeRow('${data.id}')">
                <i class="bi bi-chevron-right"></i>
            </button>` :
            '<span class="tree-spacer me-2"></span>';

        const icon = data.type === 'parish' ? 'bi-geo' :
                    data.type === 'village' ? 'bi-house-door' : 'bi-geo-alt';

        const iconColor = data.type === 'parish' ? 'text-success' :
                         data.type === 'village' ? 'text-warning' : 'text-primary';

        const progressBarColor = data.percentage == 100 ? 'success' :
                               data.percentage >= 75 ? 'warning' : 'danger';

        const badgeColor = data.percentage == 100 ? 'success' :
                          data.percentage >= 75 ? 'warning' : 'danger';

        html = `
            <td class="tree-cell">
                <div class="tree-content level-${data.level}">
                    ${toggleButton}
                    <i class="bi ${icon} ${iconColor} me-2"></i>
                    <strong>${data.name}</strong>
                    <small class="text-muted ms-2">(${data.type.charAt(0).toUpperCase() + data.type.slice(1)})</small>
                </div>
            </td>
            <td>
                <span class="badge bg-primary">${data.total_stations}</span>
            </td>
            <td>
                <span class="badge bg-success">${data.submitted_stations}</span>
            </td>
            <td>
                <span class="badge bg-warning">${data.total_stations - data.submitted_stations}</span>
            </td>
            <td>
                <div class="d-flex align-items-center">
                    <div class="progress me-2" style="width: 60px; height: 8px;">
                        <div class="progress-bar bg-${progressBarColor}" style="width: ${data.percentage}%"></div>
                    </div>
                    <span class="badge bg-${badgeColor}">${data.percentage}%</span>
                </div>
            </td>
            <td>
                <button class="btn btn-sm btn-outline-primary" onclick="window.showLocationDetails('${data.type}', '${data.name}', '', '')">
                    <i class="bi bi-eye"></i>
                </button>
            </td>
        `;
    }

    row.innerHTML = html;
    return row;
};

// Helper functions
window.findParentRow = function(row, parentType) {
    let currentRow = row.previousElementSibling;
    while (currentRow) {
        if (currentRow.dataset.type === parentType) {
            return currentRow;
        }
        currentRow = currentRow.previousElementSibling;
    }
    return null;
};

window.showLoadingState = function(row) {
    const toggleBtn = row.querySelector('.tree-toggle');
    if (toggleBtn) {
        toggleBtn.innerHTML = '<div class="tree-loading-spinner"></div>';
        toggleBtn.disabled = true;
    }
};

window.hideLoadingState = function(row) {
    const toggleBtn = row.querySelector('.tree-toggle');
    if (toggleBtn) {
        toggleBtn.innerHTML = '<i class="bi bi-chevron-down"></i>';
        toggleBtn.disabled = false;
        toggleBtn.classList.add('expanded');
    }
};

window.showErrorState = function(row) {
    const toggleBtn = row.querySelector('.tree-toggle');
    if (toggleBtn) {
        toggleBtn.innerHTML = '<i class="bi bi-exclamation-triangle text-danger"></i>';
        toggleBtn.disabled = false;
    }
};

window.showStationDetails = function(stationId, stationName) {
    console.log('Showing station details for:', stationId, stationName);

    const modalElement = document.getElementById('detailsModal');
    const modalTitle = document.getElementById('detailsModalLabel');
    const modalContent = document.getElementById('modalContent');

    if (!modalElement || !modalTitle || !modalContent) {
        console.error('Modal elements not found');
        return;
    }

    const numericId = stationId.replace('station_', '');

    modalTitle.innerHTML = `<i class="bi bi-building"></i> Station Details: ${stationName}`;

    modalContent.innerHTML = `
        <div class="text-center py-4">
            <div class="spinner-border" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-2">Loading station details...</p>
        </div>
    `;

    try {
        if (typeof bootstrap !== 'undefined' && bootstrap.Modal) {
            const modal = new bootstrap.Modal(modalElement);
            modal.show();
        } else if (typeof $ !== 'undefined') {
            $(modalElement).modal('show');
        } else {
            window.showModalManually(modalElement);
        }
    } catch (error) {
        console.error('Error showing modal:', error);
        return;
    }

    setTimeout(() => {
        modalContent.innerHTML = `
            <div class="alert alert-info">
                <i class="bi bi-info-circle"></i>
                Detailed station information will be available in the next update.
                <br><strong>Station ID:</strong> ${numericId}
                <br><strong>Station Name:</strong> ${stationName}
                <br><br>
                <small class="text-muted">This feature will include vote counts, submission history, and contributor details.</small>
            </div>
        `;
    }, 500);
};

window.buildLocationDetailsHTML = function(data) {
    return `
        <div class="alert alert-success">
            <i class="bi bi-check-circle"></i>
            <strong>Location Details Loaded Successfully</strong>
            <br>Location: ${data.location ? data.location.name : 'Unknown'}
            <br>Total Stations: ${data.location ? data.location.total_stations : 0}
            <br>Submitted: ${data.location ? data.location.submitted_stations : 0}
            <br>Completion: ${data.location ? data.location.percentage : 0}%
            <br><br>
            <small class="text-muted">Full details implementation coming soon...</small>
        </div>
    `;
};

// Page size change function
window.changePageSize = function(perPage) {
    console.log('Changing page size to:', perPage);

    const url = new URL(window.location);
    url.searchParams.set('per_page', perPage);
    url.searchParams.set('page', '1'); // Reset to first page

    window.location.href = url.toString();
};

// Initialize modal close handlers when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM Content Loaded - Setting up modal handlers');

    const modalElement = document.getElementById('detailsModal');
    if (modalElement) {
        // Handle close button clicks
        const closeButtons = modalElement.querySelectorAll('[data-bs-dismiss="modal"]');
        closeButtons.forEach(button => {
            button.addEventListener('click', function() {
                console.log('Close button clicked');
                window.hideModalManually();
            });
        });

        // Handle backdrop clicks
        modalElement.addEventListener('click', function(e) {
            if (e.target === modalElement) {
                console.log('Backdrop clicked');
                window.hideModalManually();
            }
        });

        // Handle escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && modalElement.classList.contains('show')) {
                console.log('Escape key pressed');
                window.hideModalManually();
            }
        });
    }

    // Check tree table elements
    const treeTable = document.getElementById('hierarchicalTable');
    if (treeTable) {
        console.log('Tree table found');
        const rows = treeTable.querySelectorAll('.tree-row');
        console.log('Tree rows found:', rows.length);
    }
});

console.log('All inline functions loaded successfully');
</script>

@endsection

@section('styles')
<style>
/* Modern Dashboard Base */
.modern-dashboard {
    min-height: 100vh;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

/* Compact Hero Header */
.hero-header {
    position: relative;
    padding: 1.5rem 2rem;
    margin-bottom: 1rem;
    overflow: hidden;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
}

.hero-pattern {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
    background-size: 100px 100px;
}

.hero-gradient {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
    border-radius: 24px;
}

.hero-content {
    position: relative;
    z-index: 2;
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;
    gap: 2rem;
}

.hero-main {
    flex: 1;
    color: white;
}

.hero-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.375rem;
    background: rgba(255, 255, 255, 0.2);
    padding: 0.375rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    margin-bottom: 0.75rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.hero-title {
    font-size: 2.25rem;
    font-weight: 800;
    line-height: 1.1;
    margin-bottom: 0.5rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.hero-highlight {
    background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-description {
    font-size: 0.95rem;
    line-height: 1.4;
    margin-bottom: 1rem;
    opacity: 0.95;
    max-width: 500px;
}

.hero-stats {
    display: flex;
    gap: 1.5rem;
    margin-bottom: 1rem;
}

.hero-stat {
    text-align: center;
}

.hero-stat-number {
    font-size: 1.75rem;
    font-weight: 800;
    line-height: 1;
    margin-bottom: 0.25rem;
}

.hero-stat-label {
    font-size: 0.75rem;
    opacity: 0.9;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.hero-actions {
    display: flex;
    flex-direction: row;
    gap: 0.75rem;
    align-items: center;
}

.hero-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.25rem;
    border-radius: 8px;
    font-weight: 600;
    font-size: 0.875rem;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    min-width: 140px;
    justify-content: center;
}

.hero-btn-primary {
    background: rgba(255, 255, 255, 0.95);
    color: #667eea;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.hero-btn-primary:hover {
    background: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.hero-btn-secondary {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
}

.hero-btn-secondary:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
}

.hero-btn-outline {
    background: transparent;
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.5);
}

.hero-btn-outline:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.8);
}

/* Dashboard Content */
.dashboard-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
}

/* Compact Smart Filters */
.smart-filters {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
    margin-bottom: 1rem;
    overflow: hidden;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.filters-container {
    position: relative;
}

.filters-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.5rem;
    background: linear-gradient(135deg, #f8fafc 0%, #edf2f7 100%);
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.filters-title {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1rem;
    font-weight: 700;
    color: #2d3748;
}

.filters-title i {
    color: #667eea;
    font-size: 1.125rem;
}

.filters-toggle {
    display: flex;
    align-items: center;
}

.toggle-btn {
    background: none;
    border: none;
    color: #667eea;
    font-size: 1.25rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.toggle-btn:hover {
    background: rgba(102, 126, 234, 0.1);
}

.filters-content {
    padding: 1.25rem;
    background: white;
}

.modern-form {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.form-grid {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.form-section {
    background: #f8fafc;
    border-radius: 10px;
    padding: 1rem;
    border: 1px solid #e2e8f0;
}

.section-title {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 1rem;
}

.section-title i {
    color: #667eea;
    font-size: 0.875rem;
}

.form-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 1rem;
    align-items: end;
}

.form-field {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.field-label {
    font-size: 0.875rem;
    font-weight: 600;
    color: #4a5568;
}

.field-wrapper {
    position: relative;
}

.modern-select,
.modern-input {
    width: 100%;
    padding: 0.5rem 0.75rem;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    font-size: 0.8rem;
    background: white;
    transition: all 0.3s ease;
    appearance: none;
}

.modern-select:focus,
.modern-input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.field-icon {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: #a0aec0;
    pointer-events: none;
}

.form-actions {
    display: flex;
    justify-content: flex-end;
}

.action-buttons {
    display: flex;
    gap: 1rem;
}

.modern-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border-radius: 12px;
    font-weight: 600;
    font-size: 0.875rem;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
}

.modern-btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.modern-btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.modern-btn-outline {
    background: transparent;
    color: #667eea;
    border: 2px solid #667eea;
}

.modern-btn-outline:hover {
    background: #667eea;
    color: white;
}

/* Compact Analytics Dashboard */
.analytics-dashboard {
    margin-bottom: 1rem;
}

.metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: 1rem;
    margin-bottom: 1rem;
}

.metric-card {
    background: white;
    border-radius: 12px;
    padding: 1rem;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.metric-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--metric-color);
}

.metric-primary {
    --metric-color: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.metric-success {
    --metric-color: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
}

.metric-warning {
    --metric-color: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);
}

.metric-info {
    --metric-color: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
}

.metric-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.metric-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.75rem;
}

.metric-icon {
    width: 36px;
    height: 36px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--metric-color);
    color: white;
    font-size: 1.125rem;
}

.metric-trend {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    font-size: 0.875rem;
    font-weight: 600;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
}

.metric-trend.positive {
    background: rgba(72, 187, 120, 0.1);
    color: #38a169;
}

.metric-trend.neutral {
    background: rgba(160, 174, 192, 0.1);
    color: #718096;
}

.metric-body {
    margin-bottom: 0.75rem;
}

.metric-value {
    font-size: 1.75rem;
    font-weight: 800;
    color: #2d3748;
    line-height: 1;
    margin-bottom: 0.25rem;
}

.metric-label {
    font-size: 0.875rem;
    font-weight: 600;
    color: #4a5568;
    margin-bottom: 0.125rem;
}

.metric-subtitle {
    font-size: 0.75rem;
    color: #718096;
}

.metric-footer {
    position: relative;
}

.metric-progress {
    height: 6px;
    background: #e2e8f0;
    border-radius: 3px;
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    background: var(--metric-color);
    border-radius: 3px;
    transition: width 0.3s ease;
}

.circular-progress {
    position: relative;
    width: 48px;
    height: 48px;
}

.circular-chart {
    width: 100%;
    height: 100%;
    transform: rotate(-90deg);
}

.circle-bg {
    fill: none;
    stroke: #e2e8f0;
    stroke-width: 3;
}

.circle {
    fill: none;
    stroke: #667eea;
    stroke-width: 3;
    stroke-linecap: round;
    transition: stroke-dasharray 0.3s ease;
}

.percentage {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 0.875rem;
    font-weight: 700;
    color: #2d3748;
}

/* All Insights Panel CSS Removed - Section Deleted for Maximum Compactness */

/* Data Explorer */
.data-explorer {
    background: white;
    border-radius: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(0, 0, 0, 0.05);
    overflow: hidden;
}

.empty-state-modern {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 4rem 2rem;
    text-align: center;
}

.empty-illustration {
    margin-bottom: 2rem;
}

.empty-illustration i {
    font-size: 4rem;
    color: #a0aec0;
}

.empty-content {
    max-width: 400px;
}

.empty-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 1rem;
}

.empty-description {
    color: #718096;
    margin-bottom: 2rem;
    line-height: 1.6;
}

.empty-action {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.empty-action:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.explorer-container {
    display: flex;
    flex-direction: column;
}

/* Compact Explorer Header */
.explorer-header-compact {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.5rem;
    background: linear-gradient(135deg, #f8fafc 0%, #edf2f7 100%);
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    flex-wrap: wrap;
    gap: 1rem;
}

.compact-title {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.compact-icon {
    width: 36px;
    height: 36px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.125rem;
}

.compact-content {
    display: flex;
    flex-direction: column;
    gap: 0.125rem;
}

.compact-main {
    font-size: 1.125rem;
    font-weight: 700;
    color: #2d3748;
    margin: 0;
    line-height: 1.2;
}

.compact-count {
    font-size: 0.75rem;
    color: #64748b;
    font-weight: 500;
    background: rgba(100, 116, 139, 0.1);
    padding: 0.125rem 0.5rem;
    border-radius: 12px;
    display: inline-block;
    width: fit-content;
}

/* Legacy styles for backward compatibility */
.explorer-title {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.title-icon {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
}

.title-content {
    display: flex;
    flex-direction: column;
}

.title-main {
    font-size: 1.5rem;
    font-weight: 700;
    color: #2d3748;
    margin: 0;
}

.title-subtitle {
    color: #718096;
    font-size: 0.875rem;
    margin: 0.25rem 0 0 0;
}

.compact-controls {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    flex-wrap: wrap;
}

/* Legacy explorer-controls for backward compatibility */
.explorer-controls {
    display: flex;
    align-items: center;
    gap: 2rem;
    flex-wrap: wrap;
}

/* Modern Explorer Summary */
.explorer-summary {
    display: flex;
    align-items: center;
}

.summary-card {
    display: flex;
    align-items: center;
    gap: 1rem;
    background: linear-gradient(135deg, #f8fafc 0%, #edf2f7 100%);
    padding: 1rem 1.5rem;
    border-radius: 12px;
    border: 1px solid #e2e8f0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.summary-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.25rem;
}

.summary-content {
    flex: 1;
}

.summary-value {
    font-size: 1.5rem;
    font-weight: 800;
    color: #2d3748;
    line-height: 1;
    margin-bottom: 0.25rem;
}

.summary-label {
    font-size: 0.875rem;
    font-weight: 600;
    color: #4a5568;
    margin-bottom: 0.125rem;
}

.summary-subtitle {
    font-size: 0.75rem;
    color: #718096;
}

.summary-progress {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.progress-ring {
    position: relative;
    width: 48px;
    height: 48px;
}

.progress-ring svg {
    transform: rotate(-90deg);
}

.progress-ring circle {
    transition: stroke-dashoffset 0.5s ease;
}

.progress-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 0.75rem;
    font-weight: 700;
    color: #667eea;
}

/* Modern View Controls */
.modern-view-controls {
    background: white;
    border-radius: 16px;
    padding: 1.5rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(0, 0, 0, 0.05);
    min-width: 320px;
}

.controls-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.25rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #f1f5f9;
}

.controls-title {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 1.125rem;
    font-weight: 700;
    color: #1e293b;
}

.controls-title i {
    color: #667eea;
    font-size: 1.25rem;
}

.controls-badge {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 0.375rem 0.875rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.controls-actions {
    display: flex;
    flex-direction: column;
    gap: 1.25rem;
}

.action-group {
    display: flex;
    gap: 1rem;
}

.modern-control-btn {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem 1.25rem;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    flex: 1;
    min-height: 70px;
    position: relative;
    overflow: hidden;
}

.modern-control-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.modern-control-btn:hover::before {
    opacity: 1;
}

.modern-control-btn:hover {
    border-color: #667eea;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
}

.modern-control-btn:active {
    transform: translateY(0);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.2);
}

.btn-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.125rem;
    flex-shrink: 0;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.btn-content {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
    flex: 1;
}

.btn-label {
    font-size: 0.875rem;
    font-weight: 600;
    color: #1e293b;
    line-height: 1;
}

.btn-shortcut {
    font-size: 0.75rem;
    color: #64748b;
    background: #f1f5f9;
    padding: 0.125rem 0.5rem;
    border-radius: 4px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.view-options {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-radius: 10px;
    border: 1px solid #e2e8f0;
}

.option-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.option-icon {
    width: 32px;
    height: 32px;
    background: white;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #667eea;
    font-size: 0.875rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.option-text {
    display: flex;
    flex-direction: column;
    gap: 0.125rem;
}

.option-value {
    font-size: 1rem;
    font-weight: 700;
    color: #1e293b;
    line-height: 1;
}

.option-label {
    font-size: 0.75rem;
    color: #64748b;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.option-divider {
    width: 1px;
    height: 32px;
    background: #e2e8f0;
    margin: 0 0.5rem;
}

/* Expand/Collapse Button Specific Styling */
.expand-btn .btn-icon {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.expand-btn:hover {
    border-color: #10b981;
    box-shadow: 0 8px 25px rgba(16, 185, 129, 0.15);
}

.collapse-btn .btn-icon {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
}

.collapse-btn:hover {
    border-color: #f59e0b;
    box-shadow: 0 8px 25px rgba(245, 158, 11, 0.15);
}

.explorer-content {
    padding: 0;
}

.data-table-modern {
    width: 100%;
    overflow-x: auto;
}

.modern-tree-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    background: white;
}

.table-head-modern {
    background: linear-gradient(135deg, #f8fafc 0%, #edf2f7 100%);
    position: sticky;
    top: 0;
    z-index: 10;
}

.header-row {
    border-bottom: 2px solid #e2e8f0;
}

.header-row th {
    padding: 1.5rem;
    text-align: left;
    border: none;
}

.column-header {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.header-icon {
    width: 32px;
    height: 32px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.875rem;
}

.header-text {
    display: flex;
    flex-direction: column;
}

.header-title {
    font-size: 0.875rem;
    font-weight: 700;
    color: #2d3748;
    margin: 0;
}

.header-subtitle {
    font-size: 0.75rem;
    color: #718096;
    margin: 0.125rem 0 0 0;
}

/* Modern Table Rows */
.table-body-modern {
    background: white;
}

.data-row {
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.data-row:hover {
    background: linear-gradient(135deg, #f8fafc 0%, #edf2f7 100%);
    transform: scale(1.001);
}

.modern-row td {
    padding: 1.5rem;
    vertical-align: middle;
    border: none;
}

/* Location Column */
.location-column {
    width: 35%;
}

.location-container {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.location-controls {
    display: flex;
    align-items: center;
}

.expand-btn {
    width: 28px;
    height: 28px;
    border: none;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.875rem;
}

.expand-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.expand-btn.expanded {
    transform: rotate(90deg);
    background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
}

.expand-btn.expanded:hover {
    background: linear-gradient(135deg, #38a169 0%, #2f855a 100%);
}

.expand-spacer {
    width: 28px;
    height: 28px;
}

.location-info-modern {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.location-avatar {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.125rem;
}

.location-details-modern {
    display: flex;
    flex-direction: column;
}

.location-name-modern {
    font-size: 1rem;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 0.25rem;
}

.location-meta {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.75rem;
    color: #718096;
}

.location-type-modern {
    background: #e2e8f0;
    color: #4a5568;
    padding: 0.125rem 0.5rem;
    border-radius: 4px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.location-separator {
    color: #cbd5e0;
}

.location-level {
    font-weight: 500;
}

/* Metrics Column */
.metrics-column {
    width: 30%;
}

.metrics-container {
    display: flex;
    gap: 1rem;
}

.metric-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: #f8fafc;
    padding: 0.75rem;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
    flex: 1;
}

.metric-icon {
    width: 24px;
    height: 24px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.875rem;
    color: white;
}

.metric-total .metric-icon {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.metric-submitted .metric-icon {
    background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
}

.metric-pending .metric-icon {
    background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);
}

.metric-content {
    display: flex;
    flex-direction: column;
}

.metric-value {
    font-size: 1.125rem;
    font-weight: 700;
    color: #2d3748;
    line-height: 1;
}

.metric-label {
    font-size: 0.75rem;
    color: #718096;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-top: 0.125rem;
}

/* Progress Column */
.progress-column {
    width: 25%;
}

.progress-container {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.progress-visual {
    display: flex;
    align-items: center;
    justify-content: center;
}

.progress-circle-modern {
    position: relative;
    width: 48px;
    height: 48px;
}

.circle-chart {
    width: 100%;
    height: 100%;
    transform: rotate(-90deg);
}

.circle-bg {
    fill: none;
    stroke: #e2e8f0;
    stroke-width: 3;
}

.circle-progress {
    fill: none;
    stroke-width: 3;
    stroke-linecap: round;
    transition: stroke-dasharray 0.3s ease;
}

.circle-high {
    stroke: #48bb78;
}

.circle-medium {
    stroke: #ed8936;
}

.circle-low {
    stroke: #e53e3e;
}

.circle-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 0.75rem;
    font-weight: 700;
    color: #2d3748;
}

.progress-details {
    display: flex;
    flex-direction: column;
}

.progress-percentage {
    font-size: 1.25rem;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 0.25rem;
}

.progress-status {
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-complete {
    color: #38a169;
}

.status-good {
    color: #48bb78;
}

.status-moderate {
    color: #ed8936;
}

.status-low {
    color: #e53e3e;
}

/* Actions Column */
.actions-column {
    width: 10%;
}

.actions-container {
    display: flex;
    justify-content: center;
}

.action-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.action-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

/* Modern Pagination */
.pagination-modern {
    background: linear-gradient(135deg, #f8fafc 0%, #edf2f7 100%);
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    padding: 1.5rem 2rem;
}

.pagination-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1.5rem;
}

.pagination-info-modern {
    display: flex;
    align-items: center;
}

.info-stats {
    display: flex;
    align-items: center;
    gap: 1rem;
    background: white;
    padding: 1rem 1.5rem;
    border-radius: 12px;
    border: 1px solid #e2e8f0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.stats-icon {
    width: 32px;
    height: 32px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1rem;
}

.stats-content {
    display: flex;
    flex-direction: column;
}

.stats-primary {
    font-size: 0.875rem;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 0.125rem;
}

.stats-secondary {
    font-size: 0.75rem;
    color: #718096;
}

.pagination-controls-modern {
    display: flex;
    align-items: center;
    gap: 1.5rem;
}

.page-size-control {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.control-label {
    font-size: 0.75rem;
    font-weight: 600;
    color: #718096;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.page-size-select {
    padding: 0.5rem 0.75rem;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    background: white;
    color: #4a5568;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.page-size-select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.pagination-nav {
    display: flex;
    align-items: center;
}

/* Custom Pagination Links */
.pagination {
    margin: 0;
    display: flex;
    gap: 0.25rem;
}

.page-item {
    display: flex;
}

.page-link {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0.75rem 1rem;
    background: white;
    border: 1px solid #e2e8f0;
    color: #4a5568;
    text-decoration: none;
    font-size: 0.875rem;
    font-weight: 500;
    border-radius: 8px;
    transition: all 0.3s ease;
    min-width: 44px;
}

.page-link:hover {
    background: #f7fafc;
    border-color: #cbd5e0;
    color: #2d3748;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.page-item.active .page-link {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-color: #667eea;
    color: white;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.page-item.disabled .page-link {
    background: #f7fafc;
    border-color: #e2e8f0;
    color: #a0aec0;
    cursor: not-allowed;
}

.page-item.disabled .page-link:hover {
    transform: none;
    box-shadow: none;
    background: #f7fafc;
}

/* Modern Modal */
.modern-modal .modal-dialog {
    margin: 2rem auto;
    max-width: 90vw;
}

.modern-modal-content {
    border: none;
    border-radius: 20px;
    box-shadow: 0 25px 80px rgba(0, 0, 0, 0.2);
    overflow: hidden;
}

.modern-modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem;
    border-bottom: none;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.modal-header-content {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.modal-icon-modern {
    width: 48px;
    height: 48px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.modal-title-modern {
    display: flex;
    flex-direction: column;
}

.modal-title {
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0;
}

.modal-subtitle {
    font-size: 0.875rem;
    opacity: 0.9;
    margin: 0.25rem 0 0 0;
}

.modal-close-modern {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 1.25rem;
}

.modal-close-modern:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

.modern-modal-body {
    padding: 2rem;
    min-height: 300px;
}

.loading-state-modern {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem;
    text-align: center;
}

.loading-animation {
    margin-bottom: 2rem;
}

.loading-dots {
    display: flex;
    gap: 0.5rem;
}

.dot {
    width: 12px;
    height: 12px;
    background: #667eea;
    border-radius: 50%;
    animation: bounce 1.4s infinite ease-in-out both;
}

.dot:nth-child(1) { animation-delay: -0.32s; }
.dot:nth-child(2) { animation-delay: -0.16s; }

@keyframes bounce {
    0%, 80%, 100% { transform: scale(0); }
    40% { transform: scale(1); }
}

.loading-content {
    max-width: 300px;
}

.loading-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 0.5rem;
}

.loading-description {
    color: #718096;
    margin: 0;
}

.modern-modal-footer {
    background: #f8fafc;
    padding: 1.5rem 2rem;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
}

.modal-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border-radius: 10px;
    font-weight: 600;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.3s ease;
    border: none;
}

.modal-btn-secondary {
    background: white;
    color: #4a5568;
    border: 1px solid #e2e8f0;
}

.modal-btn-secondary:hover {
    background: #f7fafc;
    border-color: #cbd5e0;
}

.modal-btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.modal-btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

/* Responsive Design */
@media (max-width: 1200px) {
    .hero-content {
        flex-direction: column;
        text-align: center;
        gap: 1.5rem;
    }

    .hero-actions {
        align-items: center;
        flex-direction: row;
        flex-wrap: wrap;
        justify-content: center;
    }

    .metrics-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    /* Insights grid CSS removed */

    .explorer-header-compact {
        flex-direction: column;
        align-items: stretch;
        gap: 0.75rem;
    }

    .compact-title {
        justify-content: center;
    }

    .compact-controls {
        justify-content: center;
        gap: 1rem;
    }

    .explorer-controls {
        justify-content: center;
    }

    .modern-view-controls {
        min-width: auto;
        width: 100%;
    }

    .action-group {
        flex-direction: column;
        gap: 0.75rem;
    }

    .view-options {
        flex-direction: column;
        gap: 0.75rem;
        align-items: stretch;
    }

    .option-divider {
        width: 100%;
        height: 1px;
        margin: 0;
    }
}

@media (max-width: 768px) {
    .hero-header {
        padding: 1rem;
    }

    .hero-title {
        font-size: 1.75rem;
    }

    .hero-description {
        font-size: 0.875rem;
    }

    .hero-stats {
        flex-direction: row;
        gap: 1rem;
        justify-content: center;
    }

    .hero-btn {
        min-width: auto;
        padding: 0.5rem 1rem;
        font-size: 0.8rem;
    }

    .dashboard-content {
        padding: 0 1rem;
    }

    .filters-content {
        padding: 0.75rem;
    }

    .form-row {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }

    .metrics-grid {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }

    .metric-card {
        padding: 0.75rem;
    }

    .modern-view-controls {
        padding: 1rem;
        margin-bottom: 1rem;
    }

    .controls-header {
        flex-direction: column;
        gap: 0.75rem;
        align-items: stretch;
        text-align: center;
    }

    .controls-title {
        justify-content: center;
        font-size: 1rem;
    }

    .modern-control-btn {
        padding: 0.875rem 1rem;
        min-height: 60px;
    }

    .btn-icon {
        width: 36px;
        height: 36px;
        font-size: 1rem;
    }

    .btn-label {
        font-size: 0.8rem;
    }

    .btn-shortcut {
        font-size: 0.7rem;
    }

    .view-options {
        padding: 0.75rem;
    }

    .option-icon {
        width: 28px;
        height: 28px;
        font-size: 0.8rem;
    }

    .option-value {
        font-size: 0.875rem;
    }

    .explorer-header-compact {
        padding: 0.75rem 1rem;
        gap: 0.5rem;
    }

    .compact-icon {
        width: 32px;
        height: 32px;
        font-size: 1rem;
    }

    .compact-main {
        font-size: 1rem;
    }

    .compact-count {
        font-size: 0.7rem;
        padding: 0.1rem 0.4rem;
    }

    .modern-row td {
        padding: 1rem 0.5rem;
    }

    .metrics-container {
        flex-direction: column;
        gap: 0.5rem;
    }

    .progress-container {
        flex-direction: column;
        gap: 0.5rem;
        text-align: center;
    }

    .pagination-container {
        flex-direction: column;
        gap: 1rem;
    }

    .pagination-controls-modern {
        flex-direction: column;
        gap: 1rem;
    }

    .modern-modal .modal-dialog {
        margin: 1rem;
    }

    .modern-modal-header {
        padding: 1rem;
    }

    .modern-modal-body {
        padding: 1rem;
    }
}

@media (max-width: 576px) {
    .hero-title {
        font-size: 2rem;
    }

    .hero-stat-number {
        font-size: 2rem;
    }

    .location-info-modern {
        flex-direction: column;
        text-align: center;
        gap: 0.5rem;
    }

    .location-avatar {
        width: 32px;
        height: 32px;
        font-size: 1rem;
    }

    .metric-item {
        padding: 0.5rem;
    }

    .progress-circle-modern {
        width: 40px;
        height: 40px;
    }

    .action-btn span {
        display: none;
    }

    .control-btn span {
        display: none;
    }

    .hero-btn span {
        display: none;
    }

    .pagination {
        flex-wrap: wrap;
        justify-content: center;
    }

    .page-link {
        padding: 0.5rem 0.75rem;
        min-width: 40px;
    }
}

/* Animations */
.data-row.expanding {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Custom Scrollbars */
.data-table-modern::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

.data-table-modern::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.data-table-modern::-webkit-scrollbar-thumb {
    background: #cbd5e0;
    border-radius: 3px;
}

.data-table-modern::-webkit-scrollbar-thumb:hover {
    background: #a0aec0;
}

/* Focus States */
.expand-btn:focus,
.control-btn:focus,
.action-btn:focus,
.modern-select:focus,
.modern-input:focus,
.page-size-select:focus {
    outline: 2px solid #667eea;
    outline-offset: 2px;
}

/* Print Styles */
@media print {
    .hero-actions,
    .explorer-controls,
    .pagination-modern,
    .modern-modal {
        display: none !important;
    }

    .hero-header {
        background: white !important;
        color: black !important;
        box-shadow: none !important;
    }

    .data-row:hover {
        background: white !important;
        transform: none !important;
    }

    .modern-dashboard {
        background: white !important;
    }
}

/* Utility Classes */
.hidden {
    display: none !important;
}

.expanding {
    animation: slideIn 0.3s ease-out;
}

/* JavaScript Function Additions */
window.toggleFilters = function() {
    const content = document.getElementById('filtersContent');
    const toggle = document.querySelector('.toggle-btn i');

    if (content.style.display === 'none') {
        content.style.display = 'block';
        toggle.style.transform = 'rotate(180deg)';
    } else {
        content.style.display = 'none';
        toggle.style.transform = 'rotate(0deg)';
    }
};

// Insights panel functions removed - section deleted for compactness

window.exportLocationData = function() {
    console.log('Export location data clicked');
    // Implementation for exporting location data
};

window.exportReport = function() {
    console.log('Export report clicked');

    // Get current filter values
    const form = document.getElementById('reportForm');
    const formData = new FormData(form);

    // Build query string
    const params = new URLSearchParams();
    for (let [key, value] of formData.entries()) {
        if (value) {
            params.append(key, value);
        }
    }

    // Create download URL
    const exportUrl = '/reports/polling-station-submission/export?' + params.toString();

    // Trigger download
    window.location.href = exportUrl;
};

// Debug function to test expand/collapse functionality
window.debugTreeTable = function() {
    console.log('=== Tree Table Debug ===');

    // Check for rows
    const allRows = document.querySelectorAll('tr[data-id]');
    console.log('Total rows found:', allRows.length);

    // Check for expand buttons
    const expandButtons = document.querySelectorAll('.expand-btn, .tree-toggle, .enhanced-toggle');
    console.log('Expand buttons found:', expandButtons.length);

    // Check for rows with children
    const rowsWithChildren = document.querySelectorAll('tr[data-has-children="true"]');
    console.log('Rows with children:', rowsWithChildren.length);

    // List first few rows
    allRows.forEach((row, index) => {
        if (index < 3) {
            console.log(`Row ${index + 1}:`, {
                id: row.dataset.id,
                hasChildren: row.dataset.hasChildren,
                expanded: row.dataset.expanded,
                level: row.dataset.level,
                hasExpandBtn: !!row.querySelector('.expand-btn, .tree-toggle, .enhanced-toggle')
            });
        }
    });

    console.log('=== End Debug ===');
};

// Test function to manually toggle first row
window.testToggle = function() {
    const firstRowWithChildren = document.querySelector('tr[data-has-children="true"]');
    if (firstRowWithChildren) {
        console.log('Testing toggle on first row with children:', firstRowWithChildren.dataset.id);
        window.toggleTreeRow(firstRowWithChildren.dataset.id);
    } else {
        console.log('No rows with children found');
    }
};

.header-actions {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.btn-action {
    padding: 0.5rem 1rem;
    border-radius: 8px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.375rem;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    font-size: 0.875rem;
}

.btn-action:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.btn-outline-light {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border-color: rgba(255, 255, 255, 0.3);
}

.btn-light {
    background: rgba(255, 255, 255, 0.9);
    color: #667eea;
    border-color: transparent;
}

/* Compact Filters */
.filters-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
    margin-bottom: 1rem;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.filters-header {
    padding: 1rem 1.5rem 0.75rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.filters-title {
    color: #2d3748;
    font-size: 1rem;
    font-weight: 600;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.filters-title i {
    color: #667eea;
    font-size: 0.875rem;
}

.filters-body {
    padding: 1rem 1.5rem;
}

.filters-form {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.filter-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
    gap: 0.75rem;
    align-items: end;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 0.375rem;
}

.filter-label {
    font-weight: 600;
    color: #4a5568;
    font-size: 0.75rem;
    display: flex;
    align-items: center;
    gap: 0.375rem;
}

.filter-label i {
    color: #667eea;
    font-size: 0.75rem;
}

.filter-select,
.filter-input {
    padding: 0.5rem 0.75rem;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    font-size: 0.8rem;
    transition: all 0.3s ease;
    background: white;
}

.filter-select:focus,
.filter-input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.filter-actions {
    display: flex;
    gap: 0.5rem;
    align-items: end;
}

.btn-filter {
    padding: 0.5rem 1rem;
    border-radius: 6px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.375rem;
    transition: all 0.3s ease;
    border: none;
    font-size: 0.8rem;
}

.btn-filter:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Compact Statistics Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 0.75rem;
    margin-bottom: 1rem;
}

.stat-card {
    background: white;
    border-radius: 10px;
    padding: 1rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(0, 0, 0, 0.05);
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--stat-color);
}

.stat-primary {
    --stat-color: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-success {
    --stat-color: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
}

.stat-warning {
    --stat-color: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);
}

.stat-info {
    --stat-color: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
}

.stat-card {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.stat-icon {
    width: 36px;
    height: 36px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.125rem;
    color: white;
    background: var(--stat-color);
}

.stat-content {
    flex: 1;
}

.stat-number {
    font-size: 1.375rem;
    font-weight: 700;
    color: #2d3748;
    line-height: 1;
}

.stat-label {
    color: #718096;
    font-size: 0.75rem;
    font-weight: 500;
    margin-top: 0.125rem;
}

.stat-trend {
    color: #48bb78;
    font-size: 1rem;
}

.progress-circle {
    width: 40px;
    height: 40px;
    position: relative;
}

.progress-circle svg {
    width: 100%;
    height: 100%;
    transform: rotate(-90deg);
}

.circle-bg {
    fill: none;
    stroke: #e2e8f0;
    stroke-width: 3;
}

.circle {
    fill: none;
    stroke: #48bb78;
    stroke-width: 3;
    stroke-linecap: round;
    transition: stroke-dasharray 0.3s ease;
}

/* Tree Table Styles */
.enhanced-tree-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    background: white;
}

/* Compact Summary Cards */
.summary-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 0.75rem;
    margin-bottom: 1rem;
}

.summary-card {
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(0, 0, 0, 0.05);
    overflow: hidden;
}

.summary-header {
    padding: 1rem 1rem 0.75rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.summary-icon {
    width: 32px;
    height: 32px;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1rem;
}

.bg-gradient-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.bg-gradient-success {
    background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
}

.summary-title {
    color: #2d3748;
    font-size: 0.9rem;
    font-weight: 600;
    margin: 0;
}

.summary-content {
    padding: 0.75rem 1rem 1rem;
    max-height: 200px;
    overflow-y: auto;
}

.contributor-item,
.activity-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 0;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.contributor-item:last-child,
.activity-item:last-child {
    border-bottom: none;
}

.contributor-rank {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 0.75rem;
}

.contributor-info,
.activity-info {
    flex: 1;
}

.contributor-name,
.activity-title {
    font-weight: 600;
    color: #2d3748;
    font-size: 0.8rem;
}

.contributor-meta,
.activity-meta {
    display: flex;
    gap: 0.5rem;
    margin-top: 0.125rem;
}

.contributor-type,
.activity-user {
    color: #718096;
    font-size: 0.75rem;
    background: #f7fafc;
    padding: 0.25rem 0.5rem;
    border-radius: 6px;
}

.contributor-count,
.activity-time {
    color: #667eea;
    font-size: 0.75rem;
    font-weight: 500;
}

.contributor-badge {
    color: #ed8936;
    font-size: 1.25rem;
}

.activity-icon {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
}

.activity-timestamp {
    color: #718096;
    font-size: 0.75rem;
    font-weight: 500;
}

.empty-state {
    text-align: center;
    padding: 2rem;
    color: #a0aec0;
}

.empty-state i {
    font-size: 2rem;
    margin-bottom: 0.5rem;
    display: block;
}

/* Compact Table Styles */
.report-table-card {
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(0, 0, 0, 0.05);
    overflow: hidden;
}

.table-header {
    padding: 1rem 1.5rem;
    background: linear-gradient(135deg, #f8fafc 0%, #edf2f7 100%);
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 0.75rem;
}

.table-title-section {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.table-icon {
    width: 32px;
    height: 32px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1rem;
}

.table-title {
    color: #2d3748;
    font-size: 1rem;
    font-weight: 600;
    margin: 0;
}

.table-subtitle {
    color: #718096;
    font-size: 0.75rem;
    margin: 0.125rem 0 0 0;
}

.table-controls {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.control-group {
    display: flex;
    gap: 0.375rem;
}

.btn-control {
    padding: 0.375rem 0.75rem;
    border-radius: 6px;
    font-size: 0.75rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.375rem;
    transition: all 0.3s ease;
    border: 1px solid #e2e8f0;
    background: white;
    color: #4a5568;
}

.btn-control:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border-color: #667eea;
    color: #667eea;
}

.stats-badge {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 0.375rem 0.75rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.375rem;
}

.table-container {
    position: relative;
}

.table-wrapper {
    overflow-x: auto;
}

.table-header-enhanced {
    background: linear-gradient(135deg, #f8fafc 0%, #edf2f7 100%);
    position: sticky;
    top: 0;
    z-index: 10;
}

.table-header-enhanced th {
    padding: 0.75rem;
    border: none;
    font-weight: 600;
    font-size: 0.75rem;
    color: #4a5568;
    text-align: left;
    border-bottom: 1px solid #e2e8f0;
}

.header-content {
    display: flex;
    align-items: center;
    gap: 0.375rem;
}

.header-content i {
    color: #667eea;
    font-size: 0.75rem;
}

/* Enhanced Row Styles */
.enhanced-row {
    transition: all 0.3s ease;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.enhanced-row:hover {
    background: linear-gradient(135deg, #f8fafc 0%, #edf2f7 100%);
    transform: scale(1.001);
}

.enhanced-cell {
    padding: 0.75rem;
    vertical-align: middle;
    border: none;
}

.tree-content {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.enhanced-toggle {
    width: 20px;
    height: 20px;
    border: none;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    font-size: 0.65rem;
}

.enhanced-toggle:hover {
    transform: scale(1.1);
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.enhanced-toggle.expanded {
    transform: rotate(90deg);
}

.tree-spacer {
    width: 20px;
    height: 20px;
}

.location-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.location-icon {
    width: 28px;
    height: 28px;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.875rem;
    color: white;
}

.subcounty-icon {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.parish-icon {
    background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
}

.village-icon {
    background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);
}

.station-icon {
    background: linear-gradient(135deg, #718096 0%, #4a5568 100%);
}

.location-details {
    display: flex;
    flex-direction: column;
}

.location-name {
    font-weight: 600;
    color: #2d3748;
    font-size: 0.8rem;
}

.location-type {
    color: #718096;
    font-size: 0.65rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stat-cell {
    padding: 0.75rem;
    text-align: center;
}

.stat-value {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.125rem;
}

.stat-number {
    font-size: 1rem;
    font-weight: 700;
    line-height: 1;
}

.stat-label {
    font-size: 0.65rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    opacity: 0.7;
}

.total-stat .stat-number {
    color: #667eea;
}

.success-stat .stat-number {
    color: #48bb78;
}

.warning-stat .stat-number {
    color: #ed8936;
}

.completion-cell {
    padding: 0.75rem;
    text-align: center;
}

.completion-display {
    display: flex;
    justify-content: center;
    align-items: center;
}

.progress-ring {
    position: relative;
    width: 32px;
    height: 32px;
}

.progress-ring-svg {
    transform: rotate(-90deg);
}

.progress-ring-circle-bg {
    fill: none;
    stroke: #e2e8f0;
    stroke-width: 3;
}

.progress-ring-circle {
    fill: none;
    stroke-width: 3;
    stroke-linecap: round;
    transition: stroke-dasharray 0.3s ease;
}

.progress-high {
    stroke: #48bb78;
}

.progress-medium {
    stroke: #ed8936;
}

.progress-low {
    stroke: #e53e3e;
}

.progress-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 0.65rem;
    font-weight: 600;
    color: #2d3748;
}

.actions-cell {
    padding: 0.75rem;
    text-align: center;
}

.btn-details {
    padding: 0.375rem 0.75rem;
    border-radius: 6px;
    font-size: 0.75rem;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    gap: 0.375rem;
    transition: all 0.3s ease;
    border: none;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-details:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
    color: white;
}

.table-footer {
    padding: 0.75rem 1.5rem;
    background: #f8fafc;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
}

.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 0.75rem;
}

/* Enhanced Pagination Styles */
.pagination-info {
    display: flex;
    align-items: center;
}

.info-card {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    background: white;
    padding: 0.75rem 1rem;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.info-card i {
    color: #667eea;
    font-size: 1rem;
}

.info-details {
    display: flex;
    flex-direction: column;
    gap: 0.125rem;
}

.info-primary {
    color: #2d3748;
    font-size: 0.875rem;
    font-weight: 600;
}

.info-secondary {
    color: #718096;
    font-size: 0.75rem;
}

.pagination-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.pagination-wrapper {
    display: flex;
    align-items: center;
}

.pagination-summary {
    display: flex;
    align-items: center;
}

.page-size-selector {
    padding: 0.5rem 0.75rem;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    font-size: 0.75rem;
    background: white;
    color: #4a5568;
    transition: all 0.3s ease;
}

.page-size-selector:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* Custom Pagination Styling */
.pagination {
    margin: 0;
    gap: 0.25rem;
}

.page-link {
    padding: 0.5rem 0.75rem;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    color: #4a5568;
    text-decoration: none;
    font-size: 0.875rem;
    font-weight: 500;
    transition: all 0.3s ease;
    background: white;
}

.page-link:hover {
    background: #f7fafc;
    border-color: #cbd5e0;
    color: #2d3748;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.page-item.active .page-link {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-color: #667eea;
    color: white;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.page-item.disabled .page-link {
    background: #f7fafc;
    border-color: #e2e8f0;
    color: #a0aec0;
    cursor: not-allowed;
}

.page-item.disabled .page-link:hover {
    transform: none;
    box-shadow: none;
}

.tree-row {
    transition: background-color 0.2s ease;
}

/* Enhanced Modal Styles */
.enhanced-modal .modal-dialog {
    margin: 2rem auto;
}

.enhanced-modal-content {
    border: none;
    border-radius: 16px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
    overflow: hidden;
}

.enhanced-modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1.5rem 2rem;
    border-bottom: none;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.modal-title-section {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.modal-icon {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
}

.modal-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin: 0;
}

.modal-subtitle {
    font-size: 0.875rem;
    opacity: 0.9;
    margin: 0.25rem 0 0 0;
}

.enhanced-close {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    width: 32px;
    height: 32px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.enhanced-close:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

.enhanced-modal-body {
    padding: 2rem;
    min-height: 200px;
}

.loading-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem;
}

.loading-spinner {
    margin-bottom: 1rem;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 3px solid #e2e8f0;
    border-top: 3px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-text {
    color: #718096;
    font-size: 0.875rem;
    margin: 0;
}

.enhanced-modal-footer {
    padding: 1rem 2rem;
    background: #f8fafc;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    display: flex;
    justify-content: flex-end;
}

.btn-modal-close {
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    border: 1px solid #e2e8f0;
    background: white;
    color: #4a5568;
}

.btn-modal-close:hover {
    background: #f7fafc;
    border-color: #cbd5e0;
    color: #2d3748;
}

.tree-row.hidden {
    display: none;
}

/* Hierarchy Level Styles */
.tree-content.level-1 {
    padding-left: 0;
    font-weight: 600;
}

.tree-content.level-2 {
    padding-left: 20px;
    font-weight: 500;
}

.tree-content.level-3 {
    padding-left: 40px;
    font-weight: 400;
}

.tree-content.level-4 {
    padding-left: 60px;
    font-weight: 400;
    font-size: 0.85rem;
}

/* Level-specific row styling */
.tree-row.level-1 {
    background-color: #ffffff;
    border-left: 4px solid #007bff;
}

.tree-row.level-2 {
    background-color: #f8f9fa;
    border-left: 4px solid #28a745;
}

.tree-row.level-3 {
    background-color: #ffffff;
    border-left: 4px solid #ffc107;
}

.tree-row.level-4 {
    background-color: #f8f9fa;
    border-left: 4px solid #6c757d;
}

/* Tree Toggle Button */
.tree-toggle {
    border: none;
    background: none;
    color: #6c757d;
    font-size: 0.875rem;
    width: 20px;
    height: 20px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    transition: transform 0.2s ease, color 0.2s ease;
}

.tree-toggle:hover {
    color: #007bff;
    background-color: #e9ecef;
    border-radius: 3px;
}

.tree-toggle.expanded {
    transform: rotate(90deg);
}

.tree-spacer {
    display: inline-block;
    width: 20px;
    height: 20px;
}

/* Loading States */
.tree-loading {
    opacity: 0.6;
    pointer-events: none;
}

.tree-loading-spinner {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-left: 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Progress Bars */
.progress {
    background-color: #e9ecef;
    height: 8px;
}

.progress-bar {
    transition: width 0.3s ease;
}

/* Badges */
.badge {
    font-size: 0.75rem;
    font-weight: 500;
}

/* Modal Styles */
.modal-xl {
    max-width: 1200px;
}

.nav-tabs .nav-link {
    color: #495057;
    border: 1px solid transparent;
    border-top-left-radius: 0.375rem;
    border-top-right-radius: 0.375rem;
}

.nav-tabs .nav-link:hover {
    border-color: #e9ecef #e9ecef #dee2e6;
    isolation: isolate;
}

.nav-tabs .nav-link.active {
    color: #495057;
    background-color: #fff;
    border-color: #dee2e6 #dee2e6 #fff;
}

/* Card Styles */
.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
}

/* List Group Styles */
.list-group-item {
    border-left: none;
    border-right: none;
}

.list-group-item:first-child {
    border-top: none;
}

.list-group-item:last-child {
    border-bottom: none;
}

/* Spinner */
.spinner-border {
    width: 3rem;
    height: 3rem;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .summary-cards {
        grid-template-columns: 1fr;
    }

    .table-controls {
        flex-direction: column;
        align-items: stretch;
    }

    .control-group {
        justify-content: center;
    }
}

@media (max-width: 768px) {
    .report-header {
        padding: 1rem;
    }

    .header-content {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .header-actions {
        width: 100%;
        justify-content: center;
    }

    .stats-grid {
        grid-template-columns: 1fr;
        gap: 0.5rem;
    }

    .stat-card {
        padding: 0.75rem;
    }

    .filters-body {
        padding: 0.75rem;
    }

    .filter-row {
        grid-template-columns: 1fr;
        gap: 0.5rem;
    }

    .filter-actions {
        flex-direction: column;
        align-items: stretch;
    }

    .table-header {
        padding: 0.75rem;
        flex-direction: column;
        align-items: stretch;
        gap: 0.75rem;
    }

    .table-title-section {
        justify-content: center;
        text-align: center;
    }

    .table-controls {
        justify-content: center;
    }

    .enhanced-cell {
        padding: 0.5rem 0.375rem;
    }

    .location-icon {
        width: 24px;
        height: 24px;
        font-size: 0.75rem;
    }

    .location-name {
        font-size: 0.75rem;
    }

    .stat-number {
        font-size: 0.875rem;
    }

    .progress-ring {
        width: 28px;
        height: 28px;
    }

    .progress-text {
        font-size: 0.6rem;
    }

    .btn-details {
        padding: 0.25rem 0.5rem;
        font-size: 0.7rem;
    }

    .footer-content {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .pagination-controls {
        flex-direction: column;
        gap: 0.75rem;
    }

    .info-card {
        padding: 0.5rem 0.75rem;
    }

    .info-primary {
        font-size: 0.8rem;
    }

    .info-secondary {
        font-size: 0.7rem;
    }

    .page-size-selector {
        font-size: 0.7rem;
        padding: 0.375rem 0.5rem;
    }

    .enhanced-modal .modal-dialog {
        margin: 0.5rem;
    }

    .enhanced-modal-header {
        padding: 0.75rem;
    }

    .enhanced-modal-body {
        padding: 0.75rem;
    }
}

@media (max-width: 576px) {
    .header-title {
        font-size: 1.5rem;
    }

    .header-subtitle {
        font-size: 0.875rem;
    }

    .btn-action span {
        display: none;
    }

    .stat-card {
        flex-direction: column;
        text-align: center;
        gap: 0.75rem;
    }

    .table-wrapper {
        font-size: 0.8rem;
    }

    .enhanced-toggle {
        width: 20px;
        height: 20px;
        font-size: 0.65rem;
    }

    .tree-spacer {
        width: 20px;
        height: 20px;
    }

    .location-info {
        gap: 0.5rem;
    }

    .location-icon {
        width: 24px;
        height: 24px;
        font-size: 0.75rem;
    }

    .btn-control span,
    .btn-details span {
        display: none;
    }

    .stats-badge span {
        display: none;
    }

    .pagination {
        flex-wrap: wrap;
        justify-content: center;
    }

    .page-link {
        padding: 0.375rem 0.5rem;
        font-size: 0.75rem;
    }

    .info-card {
        flex-direction: column;
        text-align: center;
        gap: 0.5rem;
    }

    .info-details {
        align-items: center;
    }
}

/* Animation for row expansion */
.tree-row.expanding {
    animation: fadeInUp 0.3s ease-in-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Custom scrollbar */
.table-wrapper::-webkit-scrollbar,
.summary-content::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

.table-wrapper::-webkit-scrollbar-track,
.summary-content::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.table-wrapper::-webkit-scrollbar-thumb,
.summary-content::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.table-wrapper::-webkit-scrollbar-thumb:hover,
.summary-content::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Focus states for accessibility */
.enhanced-toggle:focus,
.btn-control:focus,
.btn-details:focus,
.filter-select:focus,
.filter-input:focus {
    outline: 2px solid #667eea;
    outline-offset: 2px;
}

/* Print styles */
@media print {
    .header-actions,
    .table-controls,
    .table-footer,
    .enhanced-modal {
        display: none !important;
    }

    .report-header {
        background: white !important;
        color: black !important;
        box-shadow: none !important;
    }

    .enhanced-row:hover {
        background: white !important;
        transform: none !important;
    }
}

@media (max-width: 576px) {
    .tree-table th,
    .tree-table td {
        padding: 0.5rem 0.25rem;
        font-size: 0.75rem;
    }

    .tree-content.level-2 {
        padding-left: 10px;
    }

    .tree-content.level-3 {
        padding-left: 20px;
    }

    .tree-content.level-4 {
        padding-left: 30px;
    }

    .badge {
        font-size: 0.65rem;
    }

    .btn-sm {
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
    }

    .progress {
        width: 40px !important;
    }
}

/* Animation for row expansion */
.tree-row.expanding {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
</style>
@endsection

@section('scripts')
<script>
// Global variables for tree state management
window.expandedRows = new Set();
window.loadedChildren = new Map();

// Make functions globally accessible
window.exportReport = function() {
    // Get current filter parameters
    const urlParams = new URLSearchParams(window.location.search);
    const exportUrl = '{{ route("reports.polling-station-submission.export") }}?' + urlParams.toString();

    // Open export URL in new window
    window.open(exportUrl, '_blank');
};

window.refreshReport = function() {
    window.location.reload();
};

// Modern Tree Table Functions - Updated for new design
window.toggleTreeRow = function(rowId) {
    console.log('Toggling modern tree row:', rowId);

    const row = document.querySelector(`tr[data-id="${rowId}"]`);
    if (!row) {
        console.error('Row not found:', rowId);
        return;
    }

    // Look for both old and new button classes for compatibility
    const toggleBtn = row.querySelector('.expand-btn, .tree-toggle, .enhanced-toggle');
    if (!toggleBtn) {
        console.error('Toggle button not found for row:', rowId);
        return;
    }

    const icon = toggleBtn.querySelector('i');
    if (!icon) {
        console.error('Icon not found for row:', rowId);
        return;
    }

    const isExpanded = row.dataset.expanded === 'true';
    console.log('Current expanded state:', isExpanded);

    if (isExpanded) {
        // Collapse
        collapseRow(rowId);
        row.dataset.expanded = 'false';
        icon.className = 'bi bi-chevron-right';
        toggleBtn.classList.remove('expanded');
        if (window.expandedRows) {
            window.expandedRows.delete(rowId);
        }
        console.log('Row collapsed:', rowId);
    } else {
        // Expand
        expandRow(rowId);
        row.dataset.expanded = 'true';
        icon.className = 'bi bi-chevron-down';
        toggleBtn.classList.add('expanded');
        if (!window.expandedRows) {
            window.expandedRows = new Set();
        }
        window.expandedRows.add(rowId);
        console.log('Row expanded:', rowId);
    }
};

window.expandRow = function(rowId) {
    console.log('Expanding row:', rowId);

    const row = document.querySelector(`tr[data-id="${rowId}"]`);
    if (!row) {
        console.error('Row not found for expansion:', rowId);
        return;
    }

    const rowType = row.dataset.type;
    const rowName = row.dataset.name;

    console.log('Row details:', { type: rowType, name: rowName });

    // Check if children are already loaded
    if (window.loadedChildren.has(rowId)) {
        console.log('Children already loaded, showing existing children');
        showChildRows(rowId);
        return;
    }

    // Show loading state
    showLoadingState(row);

    // Get current filter parameters
    const urlParams = new URLSearchParams(window.location.search);
    urlParams.set('parent_id', rowId);
    urlParams.set('parent_type', rowType);
    urlParams.set('parent_name', rowName);

    // Add grandparent and great-grandparent names for deeper levels
    if (rowType === 'parish') {
        const subcountyRow = findParentRow(row, 'subcounty');
        if (subcountyRow) {
            urlParams.set('grandparent_name', subcountyRow.dataset.name);
            console.log('Added grandparent (subcounty):', subcountyRow.dataset.name);
        }
    } else if (rowType === 'village') {
        const parishRow = findParentRow(row, 'parish');
        const subcountyRow = findParentRow(row, 'subcounty');
        if (parishRow) {
            urlParams.set('grandparent_name', parishRow.dataset.name);
            console.log('Added grandparent (parish):', parishRow.dataset.name);
        }
        if (subcountyRow) {
            urlParams.set('great_grandparent_name', subcountyRow.dataset.name);
            console.log('Added great-grandparent (subcounty):', subcountyRow.dataset.name);
        }
    }

    // Fetch child data
    const apiUrl = '{{ route("api.reports.tree-child-data") }}?' + urlParams.toString();
    console.log('Fetching child data from:', apiUrl);

    fetch(apiUrl)
        .then(response => {
            console.log('API response status:', response.status);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('Received child data:', data);
            hideLoadingState(row);

            if (data.children && data.children.length > 0) {
                insertChildRows(rowId, data.children);
                window.loadedChildren.set(rowId, data.children);
                console.log('Children inserted and cached for row:', rowId);
            } else {
                console.log('No children found for row:', rowId);
                // Update the row to indicate it has no children
                row.dataset.hasChildren = 'false';
                const toggleBtn = row.querySelector('.tree-toggle');
                if (toggleBtn) {
                    toggleBtn.style.display = 'none';
                }
            }
        })
        .catch(error => {
            console.error('Error loading child data:', error);
            hideLoadingState(row);
            showErrorState(row);
        });
}

window.collapseRow = function(rowId) {
    hideChildRows(rowId);
};

window.showChildRows = function(parentId) {
    const children = window.loadedChildren.get(parentId);
    if (!children) return;

    children.forEach(child => {
        const childRow = document.querySelector(`tr[data-id="${child.id}"]`);
        if (childRow) {
            childRow.classList.remove('hidden');
            childRow.classList.add('expanding');

            // If this child is also expanded, show its children
            if (window.expandedRows.has(child.id)) {
                window.showChildRows(child.id);
            }
        }
    });
};

window.hideChildRows = function(parentId) {
    const children = window.loadedChildren.get(parentId);
    if (!children) return;

    children.forEach(child => {
        const childRow = document.querySelector(`tr[data-id="${child.id}"]`);
        if (childRow) {
            childRow.classList.add('hidden');
            childRow.classList.remove('expanding');

            // Recursively hide grandchildren
            window.hideChildRows(child.id);
        }
    });
};

window.insertChildRows = function(parentId, children) {
    console.log('Inserting child rows for parent:', parentId, 'Children count:', children.length);

    const parentRow = document.querySelector(`tr[data-id="${parentId}"]`);
    if (!parentRow) {
        console.error('Parent row not found:', parentId);
        return;
    }

    const tbody = parentRow.parentNode;
    let insertAfter = parentRow;

    children.forEach((child, index) => {
        console.log(`Processing child ${index + 1}:`, child.id, child.name);

        // Check if row already exists
        let existingRow = document.querySelector(`tr[data-id="${child.id}"]`);
        if (existingRow) {
            console.log('Row already exists, showing it:', child.id);
            existingRow.classList.remove('hidden');
            insertAfter = existingRow;
            return;
        }

        try {
            const childRow = createTreeRow(child);
            insertAfter.insertAdjacentElement('afterend', childRow);
            insertAfter = childRow;
            console.log('Child row inserted:', child.id);
        } catch (error) {
            console.error('Error creating child row:', error, child);
        }
    });

    console.log('Finished inserting child rows for parent:', parentId);
};

window.createTreeRow = function(data) {
    const row = document.createElement('tr');
    row.className = `tree-row level-${data.level} expanding`;
    row.dataset.id = data.id;
    row.dataset.level = data.level;
    row.dataset.type = data.type;
    row.dataset.name = data.name;
    row.dataset.hasChildren = data.has_children ? 'true' : 'false';
    row.dataset.expanded = 'false';

    let html = '';

    if (data.type === 'station') {
        // Station row
        const statusBadge = data.has_votes ?
            '<span class="badge bg-success">Submitted</span>' :
            '<span class="badge bg-secondary">Pending</span>';

        const lastSubmission = data.last_submission ?
            new Date(data.last_submission).toLocaleDateString() + ' ' + new Date(data.last_submission).toLocaleTimeString() :
            '<span class="text-muted">-</span>';

        const contributors = data.submission_users && data.submission_users.length > 0 ?
            data.submission_users.slice(0, 2).map(user => `<span class="badge bg-light text-dark me-1">${user.name}</span>`).join('') +
            (data.submission_users.length > 2 ? `<span class="text-muted">+${data.submission_users.length - 2}</span>` : '') :
            '<span class="text-muted">None</span>';

        html = `
            <td class="tree-cell">
                <div class="tree-content level-${data.level}">
                    <span class="tree-spacer me-2"></span>
                    <i class="bi bi-building text-muted me-2"></i>
                    ${data.name}
                    <small class="text-muted ms-2">(Station)</small>
                </div>
            </td>
            <td colspan="3">${statusBadge}</td>
            <td><small>${lastSubmission}</small></td>
            <td>
                <button class="btn btn-sm btn-outline-primary" onclick="showStationDetails('${data.id}', '${data.name}')">
                    <i class="bi bi-eye"></i>
                </button>
            </td>
        `;
    } else {
        // Location row (subcounty, parish, village)
        const toggleButton = data.has_children ?
            `<button class="tree-toggle btn btn-sm p-0 me-2" onclick="toggleTreeRow('${data.id}')">
                <i class="bi bi-chevron-right"></i>
            </button>` :
            '<span class="tree-spacer me-2"></span>';

        const icon = data.type === 'parish' ? 'bi-geo' :
                    data.type === 'village' ? 'bi-house-door' : 'bi-geo-alt';

        const iconColor = data.type === 'parish' ? 'text-success' :
                         data.type === 'village' ? 'text-warning' : 'text-primary';

        const progressBarColor = data.percentage == 100 ? 'success' :
                               data.percentage >= 75 ? 'warning' : 'danger';

        const badgeColor = data.percentage == 100 ? 'success' :
                          data.percentage >= 75 ? 'warning' : 'danger';

        html = `
            <td class="tree-cell">
                <div class="tree-content level-${data.level}">
                    ${toggleButton}
                    <i class="bi ${icon} ${iconColor} me-2"></i>
                    <strong>${data.name}</strong>
                    <small class="text-muted ms-2">(${data.type.charAt(0).toUpperCase() + data.type.slice(1)})</small>
                </div>
            </td>
            <td>
                <span class="badge bg-primary">${data.total_stations}</span>
            </td>
            <td>
                <span class="badge bg-success">${data.submitted_stations}</span>
            </td>
            <td>
                <span class="badge bg-warning">${data.total_stations - data.submitted_stations}</span>
            </td>
            <td>
                <div class="d-flex align-items-center">
                    <div class="progress me-2" style="width: 60px; height: 8px;">
                        <div class="progress-bar bg-${progressBarColor}" style="width: ${data.percentage}%"></div>
                    </div>
                    <span class="badge bg-${badgeColor}">${data.percentage}%</span>
                </div>
            </td>
            <td>
                <button class="btn btn-sm btn-outline-primary" onclick="showLocationDetails('${data.type}', '${data.name}', '', '')">
                    <i class="bi bi-eye"></i>
                </button>
            </td>
        `;
    }

    row.innerHTML = html;
    return row;
};

window.findParentRow = function(row, parentType) {
    let currentRow = row.previousElementSibling;
    while (currentRow) {
        if (currentRow.dataset.type === parentType) {
            return currentRow;
        }
        currentRow = currentRow.previousElementSibling;
    }
    return null;
};

window.showLoadingState = function(row) {
    const toggleBtn = row.querySelector('.tree-toggle');
    if (toggleBtn) {
        toggleBtn.innerHTML = '<div class="tree-loading-spinner"></div>';
        toggleBtn.disabled = true;
    }
};

window.hideLoadingState = function(row) {
    const toggleBtn = row.querySelector('.tree-toggle');
    if (toggleBtn) {
        toggleBtn.innerHTML = '<i class="bi bi-chevron-down"></i>';
        toggleBtn.disabled = false;
        toggleBtn.classList.add('expanded');
    }
};

window.showErrorState = function(row) {
    const toggleBtn = row.querySelector('.tree-toggle');
    if (toggleBtn) {
        toggleBtn.innerHTML = '<i class="bi bi-exclamation-triangle text-danger"></i>';
        toggleBtn.disabled = false;
    }
};

window.expandAllRows = function() {
    console.log('Expanding all rows');
    const level1Rows = document.querySelectorAll('.tree-row.level-1[data-has-children="true"]');
    console.log('Found level 1 rows with children:', level1Rows.length);

    level1Rows.forEach((row, index) => {
        console.log(`Processing row ${index + 1}:`, row.dataset.id, row.dataset.expanded);
        if (row.dataset.expanded === 'false') {
            // Add a small delay between expansions to prevent overwhelming the API
            setTimeout(() => {
                window.toggleTreeRow(row.dataset.id);
            }, index * 100);
        }
    });
};

window.collapseAllRows = function() {
    console.log('Collapsing all rows');
    const expandedRows = document.querySelectorAll('.tree-row[data-expanded="true"]');
    console.log('Found expanded rows:', expandedRows.length);

    expandedRows.forEach((row, index) => {
        console.log(`Collapsing row ${index + 1}:`, row.dataset.id);
        // Add a small delay between collapses for smooth animation
        setTimeout(() => {
            window.toggleTreeRow(row.dataset.id);
        }, index * 50);
    });
};

window.showLocationDetails = function(level, name, parentName = '', grandparentName = '') {
    console.log('Showing location details for:', level, name, parentName, grandparentName);

    const modalElement = document.getElementById('detailsModal');
    const modalTitle = document.getElementById('detailsModalLabel');
    const modalContent = document.getElementById('modalContent');

    if (!modalElement) {
        console.error('Modal element not found');
        return;
    }

    if (!modalTitle) {
        console.error('Modal title element not found');
        return;
    }

    if (!modalContent) {
        console.error('Modal content element not found');
        return;
    }

    // Update modal title
    modalTitle.innerHTML = `<i class="bi bi-info-circle"></i> ${level.charAt(0).toUpperCase() + level.slice(1)} Details: ${name}`;

    // Show loading state
    modalContent.innerHTML = `
        <div class="text-center py-4">
            <div class="spinner-border" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-2">Loading details...</p>
        </div>
    `;

    // Try different ways to show modal for compatibility
    try {
        // Method 1: Using Bootstrap 5 Modal class
        if (typeof bootstrap !== 'undefined' && bootstrap.Modal) {
            const modal = new bootstrap.Modal(modalElement);
            modal.show();
            console.log('Modal shown using Bootstrap 5 Modal class');
        } else {
            // Method 2: Using jQuery if available (fallback)
            if (typeof $ !== 'undefined') {
                $(modalElement).modal('show');
                console.log('Modal shown using jQuery');
            } else {
                // Method 3: Manual show using Bootstrap classes
                modalElement.classList.add('show');
                modalElement.style.display = 'block';
                modalElement.setAttribute('aria-hidden', 'false');
                document.body.classList.add('modal-open');

                // Add backdrop
                const backdrop = document.createElement('div');
                backdrop.className = 'modal-backdrop fade show';
                backdrop.id = 'modal-backdrop';
                document.body.appendChild(backdrop);

                console.log('Modal shown manually');
            }
        }
    } catch (error) {
        console.error('Error showing modal:', error);
        return;
    }

    // Get current filter parameters
    const urlParams = new URLSearchParams(window.location.search);
    urlParams.set('level', level);
    urlParams.set('name', name);
    if (parentName) urlParams.set('parent_name', parentName);
    if (grandparentName) urlParams.set('grandparent_name', grandparentName);

    // Fetch details
    const apiUrl = '{{ route("api.reports.location-details") }}?' + urlParams.toString();
    console.log('Fetching from:', apiUrl);

    fetch(apiUrl)
        .then(response => {
            console.log('Response status:', response.status);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('Received data:', data);
            modalContent.innerHTML = window.buildLocationDetailsHTML(data);
        })
        .catch(error => {
            console.error('Error fetching location details:', error);
            modalContent.innerHTML = `
                <div class="alert alert-danger">
                    <i class="bi bi-exclamation-triangle"></i>
                    Error loading details: ${error.message}
                    <br><small>Please check the console for more details.</small>
                </div>
            `;
        });
};

window.showStationDetails = function(stationId, stationName) {
    console.log('Showing station details for:', stationId, stationName);

    const modalElement = document.getElementById('detailsModal');
    const modalTitle = document.getElementById('detailsModalLabel');
    const modalContent = document.getElementById('modalContent');

    if (!modalElement || !modalTitle || !modalContent) {
        console.error('Modal elements not found');
        return;
    }

    // Extract numeric ID from station ID
    const numericId = stationId.replace('station_', '');

    // Update modal title
    modalTitle.innerHTML = `<i class="bi bi-building"></i> Station Details: ${stationName}`;

    // Show loading state
    modalContent.innerHTML = `
        <div class="text-center py-4">
            <div class="spinner-border" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-2">Loading station details...</p>
        </div>
    `;

    // Show modal using the same method as location details
    try {
        if (typeof bootstrap !== 'undefined' && bootstrap.Modal) {
            const modal = new bootstrap.Modal(modalElement);
            modal.show();
        } else if (typeof $ !== 'undefined') {
            $(modalElement).modal('show');
        } else {
            window.showModalManually(modalElement);
        }
    } catch (error) {
        console.error('Error showing modal:', error);
        return;
    }

    // For now, show basic station info (can be enhanced later)
    setTimeout(() => {
        modalContent.innerHTML = `
            <div class="alert alert-info">
                <i class="bi bi-info-circle"></i>
                Detailed station information will be available in the next update.
                <br><strong>Station ID:</strong> ${numericId}
                <br><strong>Station Name:</strong> ${stationName}
                <br><br>
                <small class="text-muted">This feature will include vote counts, submission history, and contributor details.</small>
            </div>
        `;
    }, 500);
};

// Helper function to manually show modal
window.showModalManually = function(modalElement) {
    modalElement.classList.add('show');
    modalElement.style.display = 'block';
    modalElement.setAttribute('aria-hidden', 'false');
    document.body.classList.add('modal-open');

    // Add backdrop
    let backdrop = document.getElementById('modal-backdrop');
    if (!backdrop) {
        backdrop = document.createElement('div');
        backdrop.className = 'modal-backdrop fade show';
        backdrop.id = 'modal-backdrop';
        document.body.appendChild(backdrop);
    }

    console.log('Modal shown manually');
};

// Helper function to manually hide modal
window.hideModalManually = function() {
    const modalElement = document.getElementById('detailsModal');
    const backdrop = document.getElementById('modal-backdrop');

    if (modalElement) {
        modalElement.classList.remove('show');
        modalElement.style.display = 'none';
        modalElement.setAttribute('aria-hidden', 'true');
    }

    if (backdrop) {
        backdrop.remove();
    }

    document.body.classList.remove('modal-open');
    console.log('Modal hidden manually');
};

window.buildLocationDetailsHTML = function(data) {
    const location = data.location;
    const stations = data.stations;
    const users = data.users;
    const recentSubmissions = data.recent_submissions;

    let html = `
        <!-- Location Summary -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card bg-primary text-white">
                    <div class="card-body text-center">
                        <h4>${location.total_stations}</h4>
                        <small>Total Stations</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-success text-white">
                    <div class="card-body text-center">
                        <h4>${location.submitted_stations}</h4>
                        <small>Submitted</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-warning text-white">
                    <div class="card-body text-center">
                        <h4>${location.pending_stations}</h4>
                        <small>Pending</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-info text-white">
                    <div class="card-body text-center">
                        <h4>${location.percentage}%</h4>
                        <small>Completion</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Tabs -->
        <ul class="nav nav-tabs" id="detailsTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="stations-tab" data-bs-toggle="tab" data-bs-target="#stations" type="button" role="tab">
                    <i class="bi bi-building"></i> Stations (${stations.length})
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="contributors-tab" data-bs-toggle="tab" data-bs-target="#contributors" type="button" role="tab">
                    <i class="bi bi-people"></i> Contributors (${users.length})
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="activity-tab" data-bs-toggle="tab" data-bs-target="#activity" type="button" role="tab">
                    <i class="bi bi-clock-history"></i> Recent Activity
                </button>
            </li>
        </ul>

        <div class="tab-content mt-3" id="detailsTabContent">
            <!-- Stations Tab -->
            <div class="tab-pane fade show active" id="stations" role="tabpanel">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Station Name</th>
                                <th>Status</th>
                                <th>Last Submission</th>
                                <th>Vote Count</th>
                            </tr>
                        </thead>
                        <tbody>
    `;

    stations.forEach(station => {
        const statusBadge = station.has_votes ?
            '<span class="badge bg-success">Submitted</span>' :
            '<span class="badge bg-secondary">Pending</span>';

        const lastSubmission = station.last_submission ?
            new Date(station.last_submission).toLocaleDateString() + ' ' + new Date(station.last_submission).toLocaleTimeString() :
            '-';

        html += `
            <tr>
                <td><strong>${station.name}</strong></td>
                <td>${statusBadge}</td>
                <td><small>${lastSubmission}</small></td>
                <td><span class="badge bg-primary">${station.vote_count || 0}</span></td>
            </tr>
        `;
    });

    html += `
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Contributors Tab -->
            <div class="tab-pane fade" id="contributors" role="tabpanel">
                <div class="row">
    `;

    users.forEach(user => {
        html += `
            <div class="col-md-6 mb-3">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-1">${user.name}</h6>
                                <span class="badge bg-secondary">${user.type.charAt(0).toUpperCase() + user.type.slice(1)}</span>
                            </div>
                            <div class="text-end">
                                <div class="badge bg-primary">${user.submission_count} submissions</div>
                                <div><small class="text-muted">Last: ${new Date(user.last_submission).toLocaleDateString()}</small></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    });

    html += `
                </div>
            </div>

            <!-- Activity Tab -->
            <div class="tab-pane fade" id="activity" role="tabpanel">
                <div class="list-group list-group-flush">
    `;

    recentSubmissions.forEach(submission => {
        html += `
            <div class="list-group-item">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="mb-1">${submission.station_name}</h6>
                        <small class="text-muted">Submitted by ${submission.user_name} (${submission.user_type})</small>
                    </div>
                    <small>${new Date(submission.submission_time).toLocaleDateString()} ${new Date(submission.submission_time).toLocaleTimeString()}</small>
                </div>
            </div>
        `;
    });

    html += `
                </div>
            </div>
        </div>
    `;

    return html;
};

// Preserve tree state during pagination
window.preserveTreeState = function() {
    const state = {
        expanded: Array.from(window.expandedRows),
        loaded: Array.from(window.loadedChildren.keys())
    };
    sessionStorage.setItem('treeState', JSON.stringify(state));
};

window.restoreTreeState = function() {
    const stateStr = sessionStorage.getItem('treeState');
    if (stateStr) {
        try {
            const state = JSON.parse(stateStr);
            window.expandedRows = new Set(state.expanded || []);
            // Note: We don't restore loaded children as they need to be re-fetched
        } catch (e) {
            console.warn('Failed to restore tree state:', e);
        }
    }
};

// Initialize tree state on page load
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM Content Loaded - Initializing tree table');

    // Check if Bootstrap is available
    if (typeof bootstrap !== 'undefined') {
        console.log('Bootstrap is available:', bootstrap.Modal ? 'Modal class found' : 'Modal class not found');
    } else {
        console.log('Bootstrap is not available');
    }

    // Check if jQuery is available
    if (typeof $ !== 'undefined') {
        console.log('jQuery is available');
    } else {
        console.log('jQuery is not available');
    }

    // Set up modal close handlers
    const modalElement = document.getElementById('detailsModal');
    if (modalElement) {
        console.log('Modal element found');

        // Handle close button clicks
        const closeButtons = modalElement.querySelectorAll('[data-bs-dismiss="modal"]');
        closeButtons.forEach(button => {
            button.addEventListener('click', function() {
                console.log('Close button clicked');
                window.hideModalManually();
            });
        });

        // Handle backdrop clicks
        modalElement.addEventListener('click', function(e) {
            if (e.target === modalElement) {
                console.log('Backdrop clicked');
                window.hideModalManually();
            }
        });

        // Handle escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && modalElement.classList.contains('show')) {
                console.log('Escape key pressed');
                window.hideModalManually();
            }
        });
    } else {
        console.error('Modal element not found');
    }

    // Check tree table elements
    const treeTable = document.getElementById('hierarchicalTable');
    if (treeTable) {
        console.log('Tree table found');
        const rows = treeTable.querySelectorAll('.tree-row');
        console.log('Tree rows found:', rows.length);

        // Log first few rows for debugging
        rows.forEach((row, index) => {
            if (index < 3) {
                console.log(`Row ${index}:`, {
                    id: row.dataset.id,
                    level: row.dataset.level,
                    type: row.dataset.type,
                    hasChildren: row.dataset.hasChildren,
                    expanded: row.dataset.expanded
                });
            }
        });
    } else {
        console.error('Tree table not found');
    }

    window.restoreTreeState();
});

// Save tree state before page unload
window.addEventListener('beforeunload', function() {
    window.preserveTreeState();
});

// Auto-refresh every 5 minutes
setInterval(function() {
    const lastRefresh = localStorage.getItem('lastReportRefresh');
    const now = Date.now();

    if (!lastRefresh || (now - parseInt(lastRefresh)) > 300000) { // 5 minutes
        localStorage.setItem('lastReportRefresh', now.toString());
        // Optionally auto-refresh
        // window.location.reload();
    }
}, 60000); // Check every minute

// Keyboard shortcuts
document.addEventListener('keydown', function(e) {
    // Ctrl/Cmd + E: Expand all
    if ((e.ctrlKey || e.metaKey) && e.key === 'e') {
        e.preventDefault();
        window.expandAllRows();
    }

    // Ctrl/Cmd + C: Collapse all
    if ((e.ctrlKey || e.metaKey) && e.key === 'c') {
        e.preventDefault();
        window.collapseAllRows();
    }

    // Ctrl/Cmd + R: Refresh
    if ((e.ctrlKey || e.metaKey) && e.key === 'r') {
        e.preventDefault();
        window.refreshReport();
    }
});

// Add tooltips for keyboard shortcuts and initialize tree table
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, initializing page...');

    // Initialize tree table functionality
    window.initializeTreeTable();

    const expandBtn = document.querySelector('button[onclick="expandAllRows()"]');
    const collapseBtn = document.querySelector('button[onclick="collapseAllRows()"]');

    if (expandBtn) {
        expandBtn.title = 'Expand All (Ctrl+E)';
    }
    if (collapseBtn) {
        collapseBtn.title = 'Collapse All (Ctrl+C)';
    }

    // Debug calls removed - tools section eliminated
});

// Debug and test functions removed - tools section eliminated
</script>
@endsection
