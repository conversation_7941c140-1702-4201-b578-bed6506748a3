@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Header Section -->
            <div class="report-header">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <h1 class="h3 mb-1">Polling Station Submission Report</h1>
                        <p class="text-muted mb-0">Comprehensive analysis of vote submission status across all administrative levels</p>
                    </div>
                    <div class="d-flex gap-2">
                        <button class="btn btn-outline-primary" onclick="exportReport()">
                            <i class="bi bi-download"></i> Export
                        </button>
                        <button class="btn btn-primary" onclick="refreshReport()">
                            <i class="bi bi-arrow-clockwise"></i> Refresh
                        </button>
                    </div>
                </div>

                <!-- Filters Section -->
                <div class="card mb-4">
                    <div class="card-body">
                        <form method="GET" action="{{ route('reports.polling-station-submission') }}" class="row g-3">
                            <div class="col-md-2">
                                <label class="form-label small fw-bold">District</label>
                                <select name="district" class="form-select form-select-sm">
                                    <option value="">All Districts</option>
                                    @foreach($filterOptions['districts'] as $district)
                                        <option value="{{ $district }}" {{ $districtFilter == $district ? 'selected' : '' }}>
                                            {{ $district }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label small fw-bold">County</label>
                                <select name="county" class="form-select form-select-sm">
                                    <option value="">All Counties</option>
                                    @foreach($filterOptions['counties'] as $county)
                                        <option value="{{ $county }}" {{ $countyFilter == $county ? 'selected' : '' }}>
                                            {{ $county }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label small fw-bold">Subcounty</label>
                                <select name="subcounty" class="form-select form-select-sm">
                                    <option value="">All Subcounties</option>
                                    @foreach($filterOptions['subcounties'] as $subcounty)
                                        <option value="{{ $subcounty }}" {{ $subcountyFilter == $subcounty ? 'selected' : '' }}>
                                            {{ $subcounty }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label small fw-bold">Parish</label>
                                <select name="parish" class="form-select form-select-sm">
                                    <option value="">All Parishes</option>
                                    @foreach($filterOptions['parishes'] as $parish)
                                        <option value="{{ $parish }}" {{ $parishFilter == $parish ? 'selected' : '' }}>
                                            {{ $parish }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label small fw-bold">Date From</label>
                                <input type="date" name="date_from" class="form-control form-control-sm" value="{{ $dateFrom }}">
                            </div>
                            <div class="col-md-2">
                                <label class="form-label small fw-bold">Date To</label>
                                <input type="date" name="date_to" class="form-control form-control-sm" value="{{ $dateTo }}">
                            </div>
                            <div class="col-12">
                                <button type="submit" class="btn btn-primary btn-sm">
                                    <i class="bi bi-funnel"></i> Apply Filters
                                </button>
                                <a href="{{ route('reports.polling-station-submission') }}" class="btn btn-outline-secondary btn-sm">
                                    <i class="bi bi-x-circle"></i> Clear Filters
                                </a>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Summary Statistics -->
                @php
                    $totalStations = collect($reportData)->sum('total_stations');
                    $totalSubmitted = collect($reportData)->sum('submitted_stations');
                    $overallPercentage = $totalStations > 0 ? round(($totalSubmitted / $totalStations) * 100, 1) : 0;
                @endphp

                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4 class="mb-0">{{ number_format($totalStations) }}</h4>
                                        <p class="mb-0">Total Stations</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="bi bi-building fs-2"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-success text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4 class="mb-0">{{ number_format($totalSubmitted) }}</h4>
                                        <p class="mb-0">Submitted</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="bi bi-check-circle fs-2"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-warning text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4 class="mb-0">{{ number_format($totalStations - $totalSubmitted) }}</h4>
                                        <p class="mb-0">Pending</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="bi bi-clock fs-2"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-info text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4 class="mb-0">{{ $overallPercentage }}%</h4>
                                        <p class="mb-0">Completion Rate</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="bi bi-graph-up fs-2"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Report Content -->
            <div class="report-content">
                @if(empty($reportData))
                    <div class="card">
                        <div class="card-body text-center py-5">
                            <i class="bi bi-inbox display-1 text-muted"></i>
                            <h4 class="mt-3">No Data Available</h4>
                            <p class="text-muted">No polling stations found matching the selected criteria.</p>
                        </div>
                    </div>
                @else
                    @foreach($reportData as $subcounty)
                        <div class="card mb-4 subcounty-card">
                            <div class="card-header bg-light">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h5 class="mb-0">
                                            <i class="bi bi-geo-alt-fill text-primary"></i>
                                            {{ $subcounty['name'] }}
                                        </h5>
                                        <small class="text-muted">Subcounty Level</small>
                                    </div>
                                    <div class="text-end">
                                        <div class="submission-stats">
                                            <span class="badge bg-{{ $subcounty['percentage'] == 100 ? 'success' : ($subcounty['percentage'] >= 75 ? 'warning' : 'danger') }} fs-6">
                                                {{ $subcounty['submitted_stations'] }} out of {{ $subcounty['total_stations'] }} stations ({{ $subcounty['percentage'] }}%)
                                            </span>
                                        </div>
                                        @if(!empty($subcounty['users']))
                                            <div class="mt-1">
                                                <small class="text-muted">Contributors: 
                                                    @foreach(array_slice($subcounty['users'], 0, 3) as $user)
                                                        <span class="badge bg-secondary">{{ $user['name'] }} ({{ $user['submission_count'] }})</span>
                                                    @endforeach
                                                    @if(count($subcounty['users']) > 3)
                                                        <span class="text-muted">+{{ count($subcounty['users']) - 3 }} more</span>
                                                    @endif
                                                </small>
                                            </div>
                                        @endif
                                    </div>
                                </div>
                            </div>
                            <div class="card-body">
                                @foreach($subcounty['parishes'] as $parish)
                                    <div class="parish-section mb-4">
                                        <div class="parish-header d-flex justify-content-between align-items-center mb-3">
                                            <div>
                                                <h6 class="mb-0">
                                                    <i class="bi bi-geo text-secondary"></i>
                                                    {{ $parish['name'] }}
                                                </h6>
                                                <small class="text-muted">Parish Level</small>
                                            </div>
                                            <div class="text-end">
                                                <span class="badge bg-{{ $parish['percentage'] == 100 ? 'success' : ($parish['percentage'] >= 75 ? 'warning' : 'danger') }}">
                                                    {{ $parish['submitted_stations'] }} out of {{ $parish['total_stations'] }} stations ({{ $parish['percentage'] }}%)
                                                </span>
                                            </div>
                                        </div>

                                        <div class="villages-grid">
                                            @foreach($parish['villages'] as $village)
                                                <div class="village-card card border-start border-3 border-{{ $village['percentage'] == 100 ? 'success' : ($village['percentage'] >= 75 ? 'warning' : 'danger') }} mb-3">
                                                    <div class="card-body py-3">
                                                        <div class="d-flex justify-content-between align-items-start">
                                                            <div class="flex-grow-1">
                                                                <h6 class="mb-1">
                                                                    <i class="bi bi-house-door text-muted"></i>
                                                                    {{ $village['name'] }}
                                                                </h6>
                                                                <div class="submission-status mb-2">
                                                                    <span class="badge bg-{{ $village['percentage'] == 100 ? 'success' : ($village['percentage'] >= 75 ? 'warning' : 'danger') }}">
                                                                        {{ $village['submitted_stations'] }} out of {{ $village['total_stations'] }} stations ({{ $village['percentage'] }}%)
                                                                    </span>
                                                                </div>

                                                                @if(!empty($village['users']))
                                                                    <div class="contributors mb-2">
                                                                        <small class="text-muted fw-bold">Contributors:</small>
                                                                        @foreach($village['users'] as $user)
                                                                            <div class="contributor-item">
                                                                                <small>
                                                                                    <i class="bi bi-person-fill text-primary"></i>
                                                                                    {{ $user['name'] }} 
                                                                                    <span class="badge bg-light text-dark">{{ ucfirst($user['type']) }}</span>
                                                                                    <span class="text-muted">({{ $user['submission_count'] }} submissions)</span>
                                                                                    <span class="text-muted">- Last: {{ \Carbon\Carbon::parse($user['last_submission'])->format('M j, Y H:i') }}</span>
                                                                                </small>
                                                                            </div>
                                                                        @endforeach
                                                                    </div>
                                                                @endif

                                                                <div class="stations-detail">
                                                                    <button class="btn btn-sm btn-outline-secondary" type="button" data-bs-toggle="collapse" data-bs-target="#stations-{{ md5($subcounty['name'] . $parish['name'] . $village['name']) }}">
                                                                        <i class="bi bi-list-ul"></i> View Stations ({{ count($village['stations']) }})
                                                                    </button>
                                                                </div>
                                                            </div>
                                                        </div>

                                                        <div class="collapse mt-3" id="stations-{{ md5($subcounty['name'] . $parish['name'] . $village['name']) }}">
                                                            <div class="stations-list">
                                                                @foreach($village['stations'] as $station)
                                                                    <div class="station-item d-flex justify-content-between align-items-center py-2 border-bottom">
                                                                        <div>
                                                                            <strong>{{ $station['name'] }}</strong>
                                                                            <span class="badge bg-{{ $station['has_votes'] ? 'success' : 'secondary' }} ms-2">
                                                                                {{ $station['has_votes'] ? 'Submitted' : 'Pending' }}
                                                                            </span>
                                                                        </div>
                                                                        <div class="text-end">
                                                                            @if($station['has_votes'] && $station['last_submission'])
                                                                                <small class="text-muted">
                                                                                    Last: {{ \Carbon\Carbon::parse($station['last_submission'])->format('M j, Y H:i') }}
                                                                                </small>
                                                                            @endif
                                                                        </div>
                                                                    </div>
                                                                @endforeach
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            @endforeach
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    @endforeach
                @endif
            </div>
        </div>
    </div>
</div>
@endsection

@section('styles')
<style>
.report-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 2rem;
    border-radius: 10px;
    margin-bottom: 2rem;
}

.subcounty-card {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    border: none;
    border-radius: 10px;
}

.parish-section {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 8px;
    border-left: 4px solid #007bff;
}

.village-card {
    background: white;
    transition: transform 0.2s ease;
}

.village-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.contributor-item {
    margin-bottom: 0.25rem;
}

.submission-stats .badge {
    font-size: 0.875rem;
}

.stations-list {
    max-height: 300px;
    overflow-y: auto;
    background: #f8f9fa;
    border-radius: 6px;
    padding: 0.5rem;
}

.station-item:last-child {
    border-bottom: none !important;
}

@media (max-width: 768px) {
    .report-header {
        padding: 1rem;
    }
    
    .villages-grid {
        display: block;
    }
    
    .village-card {
        margin-bottom: 1rem;
    }
}
</style>
@endsection

@section('scripts')
<script>
function exportReport() {
    // Get current filter parameters
    const urlParams = new URLSearchParams(window.location.search);
    const exportUrl = '{{ route("reports.polling-station-submission.export") }}?' + urlParams.toString();

    // Open export URL in new window
    window.open(exportUrl, '_blank');
}

function refreshReport() {
    window.location.reload();
}

// Auto-refresh every 5 minutes
setInterval(function() {
    const lastRefresh = localStorage.getItem('lastReportRefresh');
    const now = Date.now();
    
    if (!lastRefresh || (now - parseInt(lastRefresh)) > 300000) { // 5 minutes
        localStorage.setItem('lastReportRefresh', now.toString());
        // Optionally auto-refresh
        // window.location.reload();
    }
}, 60000); // Check every minute
</script>
@endsection
