@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Header Section -->
            <div class="report-header">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <h1 class="h3 mb-1">Polling Station Submission Report</h1>
                        <p class="text-muted mb-0">Comprehensive analysis of vote submission status across all administrative levels</p>
                    </div>
                    <div class="d-flex gap-2">
                        <button class="btn btn-outline-primary" onclick="exportReport()">
                            <i class="bi bi-download"></i> Export
                        </button>
                        <button class="btn btn-primary" onclick="refreshReport()">
                            <i class="bi bi-arrow-clockwise"></i> Refresh
                        </button>
                    </div>
                </div>

                <!-- Filters Section -->
                <div class="card mb-4">
                    <div class="card-body">
                        <form method="GET" action="{{ route('reports.polling-station-submission') }}" class="row g-3">
                            <div class="col-md-2">
                                <label class="form-label small fw-bold">District</label>
                                <select name="district" class="form-select form-select-sm">
                                    <option value="">All Districts</option>
                                    @foreach($filterOptions['districts'] as $district)
                                        <option value="{{ $district }}" {{ $districtFilter == $district ? 'selected' : '' }}>
                                            {{ $district }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label small fw-bold">County</label>
                                <select name="county" class="form-select form-select-sm">
                                    <option value="">All Counties</option>
                                    @foreach($filterOptions['counties'] as $county)
                                        <option value="{{ $county }}" {{ $countyFilter == $county ? 'selected' : '' }}>
                                            {{ $county }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label small fw-bold">Subcounty</label>
                                <select name="subcounty" class="form-select form-select-sm">
                                    <option value="">All Subcounties</option>
                                    @foreach($filterOptions['subcounties'] as $subcounty)
                                        <option value="{{ $subcounty }}" {{ $subcountyFilter == $subcounty ? 'selected' : '' }}>
                                            {{ $subcounty }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label small fw-bold">Parish</label>
                                <select name="parish" class="form-select form-select-sm">
                                    <option value="">All Parishes</option>
                                    @foreach($filterOptions['parishes'] as $parish)
                                        <option value="{{ $parish }}" {{ $parishFilter == $parish ? 'selected' : '' }}>
                                            {{ $parish }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label small fw-bold">Date From</label>
                                <input type="date" name="date_from" class="form-control form-control-sm" value="{{ $dateFrom }}">
                            </div>
                            <div class="col-md-2">
                                <label class="form-label small fw-bold">Date To</label>
                                <input type="date" name="date_to" class="form-control form-control-sm" value="{{ $dateTo }}">
                            </div>
                            <div class="col-md-2">
                                <label class="form-label small fw-bold">Items Per Page</label>
                                <select name="per_page" class="form-select form-select-sm">
                                    <option value="10" {{ $perPage == 10 ? 'selected' : '' }}>10</option>
                                    <option value="25" {{ $perPage == 25 ? 'selected' : '' }}>25</option>
                                    <option value="50" {{ $perPage == 50 ? 'selected' : '' }}>50</option>
                                    <option value="100" {{ $perPage == 100 ? 'selected' : '' }}>100</option>
                                </select>
                            </div>
                            <div class="col-12">
                                <button type="submit" class="btn btn-primary btn-sm">
                                    <i class="bi bi-funnel"></i> Apply Filters
                                </button>
                                <a href="{{ route('reports.polling-station-submission') }}" class="btn btn-outline-secondary btn-sm">
                                    <i class="bi bi-x-circle"></i> Clear Filters
                                </a>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Summary Statistics -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4 class="mb-0">{{ number_format($summaryStats['total_stations']) }}</h4>
                                        <p class="mb-0">Total Stations</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="bi bi-building fs-2"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-success text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4 class="mb-0">{{ number_format($summaryStats['submitted_stations']) }}</h4>
                                        <p class="mb-0">Submitted</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="bi bi-check-circle fs-2"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-warning text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4 class="mb-0">{{ number_format($summaryStats['pending_stations']) }}</h4>
                                        <p class="mb-0">Pending</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="bi bi-clock fs-2"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-info text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4 class="mb-0">{{ $summaryStats['completion_percentage'] }}%</h4>
                                        <p class="mb-0">Completion Rate</p>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="bi bi-graph-up fs-2"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Additional Summary Info -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0"><i class="bi bi-people-fill"></i> Top Contributors</h6>
                            </div>
                            <div class="card-body">
                                @if(!empty($summaryStats['top_contributors']))
                                    @foreach($summaryStats['top_contributors'] as $contributor)
                                        <div class="d-flex justify-content-between align-items-center mb-2">
                                            <div>
                                                <strong>{{ $contributor['name'] }}</strong>
                                                <span class="badge bg-secondary ms-1">{{ ucfirst($contributor['type']) }}</span>
                                            </div>
                                            <div>
                                                <span class="badge bg-primary">{{ $contributor['submission_count'] }} submissions</span>
                                            </div>
                                        </div>
                                    @endforeach
                                @else
                                    <p class="text-muted mb-0">No submissions yet</p>
                                @endif
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0"><i class="bi bi-clock-history"></i> Recent Submissions</h6>
                            </div>
                            <div class="card-body">
                                @if(!empty($summaryStats['recent_submissions']))
                                    @foreach($summaryStats['recent_submissions'] as $submission)
                                        <div class="d-flex justify-content-between align-items-center mb-2">
                                            <div>
                                                <strong>{{ $submission['station_name'] }}</strong><br>
                                                <small class="text-muted">by {{ $submission['user_name'] }}</small>
                                            </div>
                                            <div class="text-end">
                                                <small class="text-muted">{{ \Carbon\Carbon::parse($submission['submission_time'])->format('M j, H:i') }}</small>
                                            </div>
                                        </div>
                                    @endforeach
                                @else
                                    <p class="text-muted mb-0">No recent submissions</p>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Report Content -->
            <div class="report-content">
                @if(empty($reportData['data']))
                    <div class="card">
                        <div class="card-body text-center py-5">
                            <i class="bi bi-inbox display-1 text-muted"></i>
                            <h4 class="mt-3">No Data Available</h4>
                            <p class="text-muted">No polling stations found matching the selected criteria.</p>
                        </div>
                    </div>
                @else
                    <div class="card">
                        <div class="card-header">
                            <div class="d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">
                                    <i class="bi bi-diagram-3"></i>
                                    Hierarchical Submission Report
                                </h5>
                                <div class="d-flex gap-2">
                                    <button class="btn btn-sm btn-outline-secondary" onclick="expandAllRows()">
                                        <i class="bi bi-arrows-expand"></i> Expand All
                                    </button>
                                    <button class="btn btn-sm btn-outline-secondary" onclick="collapseAllRows()">
                                        <i class="bi bi-arrows-collapse"></i> Collapse All
                                    </button>
                                    <button class="btn btn-sm btn-outline-info" onclick="testModal()">
                                        <i class="bi bi-gear"></i> Test Modal
                                    </button>
                                    <button class="btn btn-sm btn-outline-warning" onclick="debugTree()">
                                        <i class="bi bi-bug"></i> Debug
                                    </button>
                                    <span class="badge bg-info">{{ $reportData['pagination']->total() }} subcounties</span>
                                </div>
                            </div>
                        </div>
                        <div class="card-body p-0">
                            <div class="table-responsive">
                                <table class="table table-hover mb-0 tree-table" id="hierarchicalTable">
                                    <thead class="table-light">
                                        <tr>
                                            <th style="width: 40%;">Location Name</th>
                                            <th style="width: 12%;">Total</th>
                                            <th style="width: 12%;">Submitted</th>
                                            <th style="width: 12%;">Pending</th>
                                            <th style="width: 15%;">Completion</th>
                                            <th style="width: 9%;">Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody id="treeTableBody">
                                        @foreach($reportData['data'] as $subcounty)
                                            <tr class="tree-row level-1"
                                                data-id="{{ $subcounty['id'] }}"
                                                data-level="1"
                                                data-type="{{ $subcounty['type'] }}"
                                                data-name="{{ $subcounty['name'] }}"
                                                data-has-children="{{ $subcounty['has_children'] ? 'true' : 'false' }}"
                                                data-expanded="false">
                                                <td class="tree-cell">
                                                    <div class="tree-content level-1">
                                                        @if($subcounty['has_children'])
                                                            <button class="tree-toggle btn btn-sm p-0 me-2" onclick="toggleTreeRow('{{ $subcounty['id'] }}')">
                                                                <i class="bi bi-chevron-right"></i>
                                                            </button>
                                                        @else
                                                            <span class="tree-spacer me-2"></span>
                                                        @endif
                                                        <i class="bi bi-geo-alt-fill text-primary me-2"></i>
                                                        <strong>{{ $subcounty['name'] }}</strong>
                                                        <small class="text-muted ms-2">(Subcounty)</small>
                                                    </div>
                                                </td>
                                                <td>
                                                    <span class="badge bg-primary">{{ $subcounty['total_stations'] }}</span>
                                                </td>
                                                <td>
                                                    <span class="badge bg-success">{{ $subcounty['submitted_stations'] }}</span>
                                                </td>
                                                <td>
                                                    <span class="badge bg-warning">{{ $subcounty['total_stations'] - $subcounty['submitted_stations'] }}</span>
                                                </td>
                                                <td>
                                                    <div class="d-flex align-items-center">
                                                        <div class="progress me-2" style="width: 60px; height: 8px;">
                                                            <div class="progress-bar bg-{{ $subcounty['percentage'] == 100 ? 'success' : ($subcounty['percentage'] >= 75 ? 'warning' : 'danger') }}"
                                                                 style="width: {{ $subcounty['percentage'] }}%"></div>
                                                        </div>
                                                        <span class="badge bg-{{ $subcounty['percentage'] == 100 ? 'success' : ($subcounty['percentage'] >= 75 ? 'warning' : 'danger') }}">
                                                            {{ $subcounty['percentage'] }}%
                                                        </span>
                                                    </div>
                                                </td>
                                                <td>
                                                    <button class="btn btn-sm btn-outline-primary"
                                                            onclick="showLocationDetails('subcounty', '{{ $subcounty['name'] }}', '', '')">
                                                        <i class="bi bi-eye"></i>
                                                    </button>
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div class="card-footer">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <small class="text-muted">
                                        Showing {{ $reportData['pagination']->firstItem() ?? 0 }} to {{ $reportData['pagination']->lastItem() ?? 0 }}
                                        of {{ $reportData['pagination']->total() }} subcounties
                                    </small>
                                </div>
                                <div>
                                    {{ $reportData['pagination']->appends(request()->query())->links() }}
                                </div>
                            </div>
                        </div>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>

<!-- Details Modal -->
<div class="modal fade" id="detailsModal" tabindex="-1" aria-labelledby="detailsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="detailsModalLabel">
                    <i class="bi bi-info-circle"></i> Details
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="modalContent">
                <div class="text-center py-4">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2">Loading details...</p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
@endsection

@section('styles')
<style>
.report-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 2rem;
    border-radius: 10px;
    margin-bottom: 2rem;
}

/* Tree Table Styles */
.tree-table {
    border-collapse: separate;
    border-spacing: 0;
}

.tree-table th {
    background-color: #f8f9fa;
    border-top: none;
    font-weight: 600;
    font-size: 0.875rem;
    color: #495057;
    position: sticky;
    top: 0;
    z-index: 10;
}

.tree-table td {
    vertical-align: middle;
    font-size: 0.875rem;
    border-bottom: 1px solid #dee2e6;
}

.tree-row {
    transition: background-color 0.2s ease;
}

.tree-row:hover {
    background-color: #f8f9fa;
}

.tree-row.hidden {
    display: none;
}

/* Hierarchy Level Styles */
.tree-content.level-1 {
    padding-left: 0;
    font-weight: 600;
}

.tree-content.level-2 {
    padding-left: 20px;
    font-weight: 500;
}

.tree-content.level-3 {
    padding-left: 40px;
    font-weight: 400;
}

.tree-content.level-4 {
    padding-left: 60px;
    font-weight: 400;
    font-size: 0.85rem;
}

/* Level-specific row styling */
.tree-row.level-1 {
    background-color: #ffffff;
    border-left: 4px solid #007bff;
}

.tree-row.level-2 {
    background-color: #f8f9fa;
    border-left: 4px solid #28a745;
}

.tree-row.level-3 {
    background-color: #ffffff;
    border-left: 4px solid #ffc107;
}

.tree-row.level-4 {
    background-color: #f8f9fa;
    border-left: 4px solid #6c757d;
}

/* Tree Toggle Button */
.tree-toggle {
    border: none;
    background: none;
    color: #6c757d;
    font-size: 0.875rem;
    width: 20px;
    height: 20px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    transition: transform 0.2s ease, color 0.2s ease;
}

.tree-toggle:hover {
    color: #007bff;
    background-color: #e9ecef;
    border-radius: 3px;
}

.tree-toggle.expanded {
    transform: rotate(90deg);
}

.tree-spacer {
    display: inline-block;
    width: 20px;
    height: 20px;
}

/* Loading States */
.tree-loading {
    opacity: 0.6;
    pointer-events: none;
}

.tree-loading-spinner {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-left: 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Progress Bars */
.progress {
    background-color: #e9ecef;
    height: 8px;
}

.progress-bar {
    transition: width 0.3s ease;
}

/* Badges */
.badge {
    font-size: 0.75rem;
    font-weight: 500;
}

/* Modal Styles */
.modal-xl {
    max-width: 1200px;
}

.nav-tabs .nav-link {
    color: #495057;
    border: 1px solid transparent;
    border-top-left-radius: 0.375rem;
    border-top-right-radius: 0.375rem;
}

.nav-tabs .nav-link:hover {
    border-color: #e9ecef #e9ecef #dee2e6;
    isolation: isolate;
}

.nav-tabs .nav-link.active {
    color: #495057;
    background-color: #fff;
    border-color: #dee2e6 #dee2e6 #fff;
}

/* Card Styles */
.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
}

/* List Group Styles */
.list-group-item {
    border-left: none;
    border-right: none;
}

.list-group-item:first-child {
    border-top: none;
}

.list-group-item:last-child {
    border-bottom: none;
}

/* Spinner */
.spinner-border {
    width: 3rem;
    height: 3rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .report-header {
        padding: 1rem;
    }

    .tree-content.level-2 {
        padding-left: 15px;
    }

    .tree-content.level-3 {
        padding-left: 30px;
    }

    .tree-content.level-4 {
        padding-left: 45px;
    }

    .modal-xl {
        max-width: 95%;
    }

    .card-body {
        padding: 1rem;
    }

    .table-responsive {
        font-size: 0.8rem;
    }
}

@media (max-width: 576px) {
    .tree-table th,
    .tree-table td {
        padding: 0.5rem 0.25rem;
        font-size: 0.75rem;
    }

    .tree-content.level-2 {
        padding-left: 10px;
    }

    .tree-content.level-3 {
        padding-left: 20px;
    }

    .tree-content.level-4 {
        padding-left: 30px;
    }

    .badge {
        font-size: 0.65rem;
    }

    .btn-sm {
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
    }

    .progress {
        width: 40px !important;
    }
}

/* Animation for row expansion */
.tree-row.expanding {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
</style>
@endsection

@section('scripts')
<script>
// Global variables for tree state management
let expandedRows = new Set();
let loadedChildren = new Map();

function exportReport() {
    // Get current filter parameters
    const urlParams = new URLSearchParams(window.location.search);
    const exportUrl = '{{ route("reports.polling-station-submission.export") }}?' + urlParams.toString();

    // Open export URL in new window
    window.open(exportUrl, '_blank');
}

function refreshReport() {
    window.location.reload();
}

// Tree Table Functions
function toggleTreeRow(rowId) {
    console.log('Toggling row:', rowId);

    const row = document.querySelector(`tr[data-id="${rowId}"]`);
    if (!row) {
        console.error('Row not found:', rowId);
        return;
    }

    const toggleBtn = row.querySelector('.tree-toggle');
    if (!toggleBtn) {
        console.error('Toggle button not found for row:', rowId);
        return;
    }

    const icon = toggleBtn.querySelector('i');
    if (!icon) {
        console.error('Icon not found for row:', rowId);
        return;
    }

    const isExpanded = row.dataset.expanded === 'true';
    console.log('Current expanded state:', isExpanded);

    if (isExpanded) {
        // Collapse
        collapseRow(rowId);
        row.dataset.expanded = 'false';
        icon.className = 'bi bi-chevron-right';
        toggleBtn.classList.remove('expanded');
        expandedRows.delete(rowId);
        console.log('Row collapsed:', rowId);
    } else {
        // Expand
        expandRow(rowId);
        row.dataset.expanded = 'true';
        icon.className = 'bi bi-chevron-down';
        toggleBtn.classList.add('expanded');
        expandedRows.add(rowId);
        console.log('Row expanded:', rowId);
    }
}

function expandRow(rowId) {
    console.log('Expanding row:', rowId);

    const row = document.querySelector(`tr[data-id="${rowId}"]`);
    if (!row) {
        console.error('Row not found for expansion:', rowId);
        return;
    }

    const rowType = row.dataset.type;
    const rowName = row.dataset.name;

    console.log('Row details:', { type: rowType, name: rowName });

    // Check if children are already loaded
    if (loadedChildren.has(rowId)) {
        console.log('Children already loaded, showing existing children');
        showChildRows(rowId);
        return;
    }

    // Show loading state
    showLoadingState(row);

    // Get current filter parameters
    const urlParams = new URLSearchParams(window.location.search);
    urlParams.set('parent_id', rowId);
    urlParams.set('parent_type', rowType);
    urlParams.set('parent_name', rowName);

    // Add grandparent and great-grandparent names for deeper levels
    if (rowType === 'parish') {
        const subcountyRow = findParentRow(row, 'subcounty');
        if (subcountyRow) {
            urlParams.set('grandparent_name', subcountyRow.dataset.name);
            console.log('Added grandparent (subcounty):', subcountyRow.dataset.name);
        }
    } else if (rowType === 'village') {
        const parishRow = findParentRow(row, 'parish');
        const subcountyRow = findParentRow(row, 'subcounty');
        if (parishRow) {
            urlParams.set('grandparent_name', parishRow.dataset.name);
            console.log('Added grandparent (parish):', parishRow.dataset.name);
        }
        if (subcountyRow) {
            urlParams.set('great_grandparent_name', subcountyRow.dataset.name);
            console.log('Added great-grandparent (subcounty):', subcountyRow.dataset.name);
        }
    }

    // Fetch child data
    const apiUrl = '{{ route("api.reports.tree-child-data") }}?' + urlParams.toString();
    console.log('Fetching child data from:', apiUrl);

    fetch(apiUrl)
        .then(response => {
            console.log('API response status:', response.status);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('Received child data:', data);
            hideLoadingState(row);

            if (data.children && data.children.length > 0) {
                insertChildRows(rowId, data.children);
                loadedChildren.set(rowId, data.children);
                console.log('Children inserted and cached for row:', rowId);
            } else {
                console.log('No children found for row:', rowId);
                // Update the row to indicate it has no children
                row.dataset.hasChildren = 'false';
                const toggleBtn = row.querySelector('.tree-toggle');
                if (toggleBtn) {
                    toggleBtn.style.display = 'none';
                }
            }
        })
        .catch(error => {
            console.error('Error loading child data:', error);
            hideLoadingState(row);
            showErrorState(row);
        });
}

function collapseRow(rowId) {
    hideChildRows(rowId);
}

function showChildRows(parentId) {
    const children = loadedChildren.get(parentId);
    if (!children) return;

    children.forEach(child => {
        const childRow = document.querySelector(`tr[data-id="${child.id}"]`);
        if (childRow) {
            childRow.classList.remove('hidden');
            childRow.classList.add('expanding');

            // If this child is also expanded, show its children
            if (expandedRows.has(child.id)) {
                showChildRows(child.id);
            }
        }
    });
}

function hideChildRows(parentId) {
    const children = loadedChildren.get(parentId);
    if (!children) return;

    children.forEach(child => {
        const childRow = document.querySelector(`tr[data-id="${child.id}"]`);
        if (childRow) {
            childRow.classList.add('hidden');
            childRow.classList.remove('expanding');

            // Recursively hide grandchildren
            hideChildRows(child.id);
        }
    });
}

function insertChildRows(parentId, children) {
    console.log('Inserting child rows for parent:', parentId, 'Children count:', children.length);

    const parentRow = document.querySelector(`tr[data-id="${parentId}"]`);
    if (!parentRow) {
        console.error('Parent row not found:', parentId);
        return;
    }

    const tbody = parentRow.parentNode;
    let insertAfter = parentRow;

    children.forEach((child, index) => {
        console.log(`Processing child ${index + 1}:`, child.id, child.name);

        // Check if row already exists
        let existingRow = document.querySelector(`tr[data-id="${child.id}"]`);
        if (existingRow) {
            console.log('Row already exists, showing it:', child.id);
            existingRow.classList.remove('hidden');
            insertAfter = existingRow;
            return;
        }

        try {
            const childRow = createTreeRow(child);
            insertAfter.insertAdjacentElement('afterend', childRow);
            insertAfter = childRow;
            console.log('Child row inserted:', child.id);
        } catch (error) {
            console.error('Error creating child row:', error, child);
        }
    });

    console.log('Finished inserting child rows for parent:', parentId);
}

function createTreeRow(data) {
    const row = document.createElement('tr');
    row.className = `tree-row level-${data.level} expanding`;
    row.dataset.id = data.id;
    row.dataset.level = data.level;
    row.dataset.type = data.type;
    row.dataset.name = data.name;
    row.dataset.hasChildren = data.has_children ? 'true' : 'false';
    row.dataset.expanded = 'false';

    let html = '';

    if (data.type === 'station') {
        // Station row
        const statusBadge = data.has_votes ?
            '<span class="badge bg-success">Submitted</span>' :
            '<span class="badge bg-secondary">Pending</span>';

        const lastSubmission = data.last_submission ?
            new Date(data.last_submission).toLocaleDateString() + ' ' + new Date(data.last_submission).toLocaleTimeString() :
            '<span class="text-muted">-</span>';

        const contributors = data.submission_users && data.submission_users.length > 0 ?
            data.submission_users.slice(0, 2).map(user => `<span class="badge bg-light text-dark me-1">${user.name}</span>`).join('') +
            (data.submission_users.length > 2 ? `<span class="text-muted">+${data.submission_users.length - 2}</span>` : '') :
            '<span class="text-muted">None</span>';

        html = `
            <td class="tree-cell">
                <div class="tree-content level-${data.level}">
                    <span class="tree-spacer me-2"></span>
                    <i class="bi bi-building text-muted me-2"></i>
                    ${data.name}
                    <small class="text-muted ms-2">(Station)</small>
                </div>
            </td>
            <td colspan="3">${statusBadge}</td>
            <td><small>${lastSubmission}</small></td>
            <td>
                <button class="btn btn-sm btn-outline-primary" onclick="showStationDetails('${data.id}', '${data.name}')">
                    <i class="bi bi-eye"></i>
                </button>
            </td>
        `;
    } else {
        // Location row (subcounty, parish, village)
        const toggleButton = data.has_children ?
            `<button class="tree-toggle btn btn-sm p-0 me-2" onclick="toggleTreeRow('${data.id}')">
                <i class="bi bi-chevron-right"></i>
            </button>` :
            '<span class="tree-spacer me-2"></span>';

        const icon = data.type === 'parish' ? 'bi-geo' :
                    data.type === 'village' ? 'bi-house-door' : 'bi-geo-alt';

        const iconColor = data.type === 'parish' ? 'text-success' :
                         data.type === 'village' ? 'text-warning' : 'text-primary';

        const progressBarColor = data.percentage == 100 ? 'success' :
                               data.percentage >= 75 ? 'warning' : 'danger';

        const badgeColor = data.percentage == 100 ? 'success' :
                          data.percentage >= 75 ? 'warning' : 'danger';

        html = `
            <td class="tree-cell">
                <div class="tree-content level-${data.level}">
                    ${toggleButton}
                    <i class="bi ${icon} ${iconColor} me-2"></i>
                    <strong>${data.name}</strong>
                    <small class="text-muted ms-2">(${data.type.charAt(0).toUpperCase() + data.type.slice(1)})</small>
                </div>
            </td>
            <td>
                <span class="badge bg-primary">${data.total_stations}</span>
            </td>
            <td>
                <span class="badge bg-success">${data.submitted_stations}</span>
            </td>
            <td>
                <span class="badge bg-warning">${data.total_stations - data.submitted_stations}</span>
            </td>
            <td>
                <div class="d-flex align-items-center">
                    <div class="progress me-2" style="width: 60px; height: 8px;">
                        <div class="progress-bar bg-${progressBarColor}" style="width: ${data.percentage}%"></div>
                    </div>
                    <span class="badge bg-${badgeColor}">${data.percentage}%</span>
                </div>
            </td>
            <td>
                <button class="btn btn-sm btn-outline-primary" onclick="showLocationDetails('${data.type}', '${data.name}', '', '')">
                    <i class="bi bi-eye"></i>
                </button>
            </td>
        `;
    }

    row.innerHTML = html;
    return row;
}

function findParentRow(row, parentType) {
    let currentRow = row.previousElementSibling;
    while (currentRow) {
        if (currentRow.dataset.type === parentType) {
            return currentRow;
        }
        currentRow = currentRow.previousElementSibling;
    }
    return null;
}

function showLoadingState(row) {
    const toggleBtn = row.querySelector('.tree-toggle');
    if (toggleBtn) {
        toggleBtn.innerHTML = '<div class="tree-loading-spinner"></div>';
        toggleBtn.disabled = true;
    }
}

function hideLoadingState(row) {
    const toggleBtn = row.querySelector('.tree-toggle');
    if (toggleBtn) {
        toggleBtn.innerHTML = '<i class="bi bi-chevron-down"></i>';
        toggleBtn.disabled = false;
        toggleBtn.classList.add('expanded');
    }
}

function showErrorState(row) {
    const toggleBtn = row.querySelector('.tree-toggle');
    if (toggleBtn) {
        toggleBtn.innerHTML = '<i class="bi bi-exclamation-triangle text-danger"></i>';
        toggleBtn.disabled = false;
    }
}

function expandAllRows() {
    console.log('Expanding all rows');
    const level1Rows = document.querySelectorAll('.tree-row.level-1[data-has-children="true"]');
    console.log('Found level 1 rows with children:', level1Rows.length);

    level1Rows.forEach((row, index) => {
        console.log(`Processing row ${index + 1}:`, row.dataset.id, row.dataset.expanded);
        if (row.dataset.expanded === 'false') {
            // Add a small delay between expansions to prevent overwhelming the API
            setTimeout(() => {
                toggleTreeRow(row.dataset.id);
            }, index * 100);
        }
    });
}

function collapseAllRows() {
    console.log('Collapsing all rows');
    const expandedRows = document.querySelectorAll('.tree-row[data-expanded="true"]');
    console.log('Found expanded rows:', expandedRows.length);

    expandedRows.forEach((row, index) => {
        console.log(`Collapsing row ${index + 1}:`, row.dataset.id);
        // Add a small delay between collapses for smooth animation
        setTimeout(() => {
            toggleTreeRow(row.dataset.id);
        }, index * 50);
    });
}

function showLocationDetails(level, name, parentName = '', grandparentName = '') {
    console.log('Showing location details for:', level, name, parentName, grandparentName);

    const modalElement = document.getElementById('detailsModal');
    const modalTitle = document.getElementById('detailsModalLabel');
    const modalContent = document.getElementById('modalContent');

    if (!modalElement) {
        console.error('Modal element not found');
        return;
    }

    if (!modalTitle) {
        console.error('Modal title element not found');
        return;
    }

    if (!modalContent) {
        console.error('Modal content element not found');
        return;
    }

    // Update modal title
    modalTitle.innerHTML = `<i class="bi bi-info-circle"></i> ${level.charAt(0).toUpperCase() + level.slice(1)} Details: ${name}`;

    // Show loading state
    modalContent.innerHTML = `
        <div class="text-center py-4">
            <div class="spinner-border" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-2">Loading details...</p>
        </div>
    `;

    // Try different ways to show modal for compatibility
    try {
        // Method 1: Using Bootstrap 5 Modal class
        if (typeof bootstrap !== 'undefined' && bootstrap.Modal) {
            const modal = new bootstrap.Modal(modalElement);
            modal.show();
            console.log('Modal shown using Bootstrap 5 Modal class');
        } else {
            // Method 2: Using jQuery if available (fallback)
            if (typeof $ !== 'undefined') {
                $(modalElement).modal('show');
                console.log('Modal shown using jQuery');
            } else {
                // Method 3: Manual show using Bootstrap classes
                modalElement.classList.add('show');
                modalElement.style.display = 'block';
                modalElement.setAttribute('aria-hidden', 'false');
                document.body.classList.add('modal-open');

                // Add backdrop
                const backdrop = document.createElement('div');
                backdrop.className = 'modal-backdrop fade show';
                backdrop.id = 'modal-backdrop';
                document.body.appendChild(backdrop);

                console.log('Modal shown manually');
            }
        }
    } catch (error) {
        console.error('Error showing modal:', error);
        return;
    }

    // Get current filter parameters
    const urlParams = new URLSearchParams(window.location.search);
    urlParams.set('level', level);
    urlParams.set('name', name);
    if (parentName) urlParams.set('parent_name', parentName);
    if (grandparentName) urlParams.set('grandparent_name', grandparentName);

    // Fetch details
    const apiUrl = '{{ route("api.reports.location-details") }}?' + urlParams.toString();
    console.log('Fetching from:', apiUrl);

    fetch(apiUrl)
        .then(response => {
            console.log('Response status:', response.status);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('Received data:', data);
            modalContent.innerHTML = buildLocationDetailsHTML(data);
        })
        .catch(error => {
            console.error('Error fetching location details:', error);
            modalContent.innerHTML = `
                <div class="alert alert-danger">
                    <i class="bi bi-exclamation-triangle"></i>
                    Error loading details: ${error.message}
                    <br><small>Please check the console for more details.</small>
                </div>
            `;
        });
}

function showStationDetails(stationId, stationName) {
    console.log('Showing station details for:', stationId, stationName);

    const modalElement = document.getElementById('detailsModal');
    const modalTitle = document.getElementById('detailsModalLabel');
    const modalContent = document.getElementById('modalContent');

    if (!modalElement || !modalTitle || !modalContent) {
        console.error('Modal elements not found');
        return;
    }

    // Extract numeric ID from station ID
    const numericId = stationId.replace('station_', '');

    // Update modal title
    modalTitle.innerHTML = `<i class="bi bi-building"></i> Station Details: ${stationName}`;

    // Show loading state
    modalContent.innerHTML = `
        <div class="text-center py-4">
            <div class="spinner-border" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-2">Loading station details...</p>
        </div>
    `;

    // Show modal using the same method as location details
    try {
        if (typeof bootstrap !== 'undefined' && bootstrap.Modal) {
            const modal = new bootstrap.Modal(modalElement);
            modal.show();
        } else if (typeof $ !== 'undefined') {
            $(modalElement).modal('show');
        } else {
            showModalManually(modalElement);
        }
    } catch (error) {
        console.error('Error showing modal:', error);
        return;
    }

    // For now, show basic station info (can be enhanced later)
    setTimeout(() => {
        modalContent.innerHTML = `
            <div class="alert alert-info">
                <i class="bi bi-info-circle"></i>
                Detailed station information will be available in the next update.
                <br><strong>Station ID:</strong> ${numericId}
                <br><strong>Station Name:</strong> ${stationName}
                <br><br>
                <small class="text-muted">This feature will include vote counts, submission history, and contributor details.</small>
            </div>
        `;
    }, 500);
}

// Helper function to manually show modal
function showModalManually(modalElement) {
    modalElement.classList.add('show');
    modalElement.style.display = 'block';
    modalElement.setAttribute('aria-hidden', 'false');
    document.body.classList.add('modal-open');

    // Add backdrop
    let backdrop = document.getElementById('modal-backdrop');
    if (!backdrop) {
        backdrop = document.createElement('div');
        backdrop.className = 'modal-backdrop fade show';
        backdrop.id = 'modal-backdrop';
        document.body.appendChild(backdrop);
    }

    console.log('Modal shown manually');
}

// Helper function to manually hide modal
function hideModalManually() {
    const modalElement = document.getElementById('detailsModal');
    const backdrop = document.getElementById('modal-backdrop');

    if (modalElement) {
        modalElement.classList.remove('show');
        modalElement.style.display = 'none';
        modalElement.setAttribute('aria-hidden', 'true');
    }

    if (backdrop) {
        backdrop.remove();
    }

    document.body.classList.remove('modal-open');
    console.log('Modal hidden manually');
}

function buildLocationDetailsHTML(data) {
    const location = data.location;
    const stations = data.stations;
    const users = data.users;
    const recentSubmissions = data.recent_submissions;

    let html = `
        <!-- Location Summary -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card bg-primary text-white">
                    <div class="card-body text-center">
                        <h4>${location.total_stations}</h4>
                        <small>Total Stations</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-success text-white">
                    <div class="card-body text-center">
                        <h4>${location.submitted_stations}</h4>
                        <small>Submitted</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-warning text-white">
                    <div class="card-body text-center">
                        <h4>${location.pending_stations}</h4>
                        <small>Pending</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-info text-white">
                    <div class="card-body text-center">
                        <h4>${location.percentage}%</h4>
                        <small>Completion</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Tabs -->
        <ul class="nav nav-tabs" id="detailsTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="stations-tab" data-bs-toggle="tab" data-bs-target="#stations" type="button" role="tab">
                    <i class="bi bi-building"></i> Stations (${stations.length})
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="contributors-tab" data-bs-toggle="tab" data-bs-target="#contributors" type="button" role="tab">
                    <i class="bi bi-people"></i> Contributors (${users.length})
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="activity-tab" data-bs-toggle="tab" data-bs-target="#activity" type="button" role="tab">
                    <i class="bi bi-clock-history"></i> Recent Activity
                </button>
            </li>
        </ul>

        <div class="tab-content mt-3" id="detailsTabContent">
            <!-- Stations Tab -->
            <div class="tab-pane fade show active" id="stations" role="tabpanel">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Station Name</th>
                                <th>Status</th>
                                <th>Last Submission</th>
                                <th>Vote Count</th>
                            </tr>
                        </thead>
                        <tbody>
    `;

    stations.forEach(station => {
        const statusBadge = station.has_votes ?
            '<span class="badge bg-success">Submitted</span>' :
            '<span class="badge bg-secondary">Pending</span>';

        const lastSubmission = station.last_submission ?
            new Date(station.last_submission).toLocaleDateString() + ' ' + new Date(station.last_submission).toLocaleTimeString() :
            '-';

        html += `
            <tr>
                <td><strong>${station.name}</strong></td>
                <td>${statusBadge}</td>
                <td><small>${lastSubmission}</small></td>
                <td><span class="badge bg-primary">${station.vote_count || 0}</span></td>
            </tr>
        `;
    });

    html += `
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Contributors Tab -->
            <div class="tab-pane fade" id="contributors" role="tabpanel">
                <div class="row">
    `;

    users.forEach(user => {
        html += `
            <div class="col-md-6 mb-3">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-1">${user.name}</h6>
                                <span class="badge bg-secondary">${user.type.charAt(0).toUpperCase() + user.type.slice(1)}</span>
                            </div>
                            <div class="text-end">
                                <div class="badge bg-primary">${user.submission_count} submissions</div>
                                <div><small class="text-muted">Last: ${new Date(user.last_submission).toLocaleDateString()}</small></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    });

    html += `
                </div>
            </div>

            <!-- Activity Tab -->
            <div class="tab-pane fade" id="activity" role="tabpanel">
                <div class="list-group list-group-flush">
    `;

    recentSubmissions.forEach(submission => {
        html += `
            <div class="list-group-item">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="mb-1">${submission.station_name}</h6>
                        <small class="text-muted">Submitted by ${submission.user_name} (${submission.user_type})</small>
                    </div>
                    <small>${new Date(submission.submission_time).toLocaleDateString()} ${new Date(submission.submission_time).toLocaleTimeString()}</small>
                </div>
            </div>
        `;
    });

    html += `
                </div>
            </div>
        </div>
    `;

    return html;
}

// Preserve tree state during pagination
function preserveTreeState() {
    const state = {
        expanded: Array.from(expandedRows),
        loaded: Array.from(loadedChildren.keys())
    };
    sessionStorage.setItem('treeState', JSON.stringify(state));
}

function restoreTreeState() {
    const stateStr = sessionStorage.getItem('treeState');
    if (stateStr) {
        try {
            const state = JSON.parse(stateStr);
            expandedRows = new Set(state.expanded || []);
            // Note: We don't restore loaded children as they need to be re-fetched
        } catch (e) {
            console.warn('Failed to restore tree state:', e);
        }
    }
}

// Initialize tree state on page load
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM Content Loaded - Initializing tree table');

    // Check if Bootstrap is available
    if (typeof bootstrap !== 'undefined') {
        console.log('Bootstrap is available:', bootstrap.Modal ? 'Modal class found' : 'Modal class not found');
    } else {
        console.log('Bootstrap is not available');
    }

    // Check if jQuery is available
    if (typeof $ !== 'undefined') {
        console.log('jQuery is available');
    } else {
        console.log('jQuery is not available');
    }

    // Set up modal close handlers
    const modalElement = document.getElementById('detailsModal');
    if (modalElement) {
        console.log('Modal element found');

        // Handle close button clicks
        const closeButtons = modalElement.querySelectorAll('[data-bs-dismiss="modal"]');
        closeButtons.forEach(button => {
            button.addEventListener('click', function() {
                console.log('Close button clicked');
                hideModalManually();
            });
        });

        // Handle backdrop clicks
        modalElement.addEventListener('click', function(e) {
            if (e.target === modalElement) {
                console.log('Backdrop clicked');
                hideModalManually();
            }
        });

        // Handle escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && modalElement.classList.contains('show')) {
                console.log('Escape key pressed');
                hideModalManually();
            }
        });
    } else {
        console.error('Modal element not found');
    }

    // Check tree table elements
    const treeTable = document.getElementById('hierarchicalTable');
    if (treeTable) {
        console.log('Tree table found');
        const rows = treeTable.querySelectorAll('.tree-row');
        console.log('Tree rows found:', rows.length);

        // Log first few rows for debugging
        rows.forEach((row, index) => {
            if (index < 3) {
                console.log(`Row ${index}:`, {
                    id: row.dataset.id,
                    level: row.dataset.level,
                    type: row.dataset.type,
                    hasChildren: row.dataset.hasChildren,
                    expanded: row.dataset.expanded
                });
            }
        });
    } else {
        console.error('Tree table not found');
    }

    restoreTreeState();
});

// Save tree state before page unload
window.addEventListener('beforeunload', function() {
    preserveTreeState();
});

// Auto-refresh every 5 minutes
setInterval(function() {
    const lastRefresh = localStorage.getItem('lastReportRefresh');
    const now = Date.now();

    if (!lastRefresh || (now - parseInt(lastRefresh)) > 300000) { // 5 minutes
        localStorage.setItem('lastReportRefresh', now.toString());
        // Optionally auto-refresh
        // window.location.reload();
    }
}, 60000); // Check every minute

// Keyboard shortcuts
document.addEventListener('keydown', function(e) {
    // Ctrl/Cmd + E: Expand all
    if ((e.ctrlKey || e.metaKey) && e.key === 'e') {
        e.preventDefault();
        expandAllRows();
    }

    // Ctrl/Cmd + C: Collapse all
    if ((e.ctrlKey || e.metaKey) && e.key === 'c') {
        e.preventDefault();
        collapseAllRows();
    }

    // Ctrl/Cmd + R: Refresh
    if ((e.ctrlKey || e.metaKey) && e.key === 'r') {
        e.preventDefault();
        refreshReport();
    }
});

// Add tooltips for keyboard shortcuts
document.addEventListener('DOMContentLoaded', function() {
    const expandBtn = document.querySelector('button[onclick="expandAllRows()"]');
    const collapseBtn = document.querySelector('button[onclick="collapseAllRows()"]');

    if (expandBtn) {
        expandBtn.title = 'Expand All (Ctrl+E)';
    }
    if (collapseBtn) {
        collapseBtn.title = 'Collapse All (Ctrl+C)';
    }
});

// Debug and test functions
function testModal() {
    console.log('Testing modal functionality');
    showLocationDetails('subcounty', 'Test Subcounty', '', '');
}

function debugTree() {
    console.log('=== TREE DEBUG INFO ===');
    console.log('Expanded rows:', Array.from(expandedRows));
    console.log('Loaded children:', Array.from(loadedChildren.keys()));

    const allRows = document.querySelectorAll('.tree-row');
    console.log('Total rows:', allRows.length);

    const level1Rows = document.querySelectorAll('.tree-row.level-1');
    console.log('Level 1 rows:', level1Rows.length);

    level1Rows.forEach((row, index) => {
        console.log(`Level 1 Row ${index + 1}:`, {
            id: row.dataset.id,
            name: row.dataset.name,
            hasChildren: row.dataset.hasChildren,
            expanded: row.dataset.expanded
        });
    });

    // Test first toggle button
    const firstToggle = document.querySelector('.tree-toggle');
    if (firstToggle) {
        console.log('First toggle button found:', firstToggle);
        console.log('First toggle onclick:', firstToggle.getAttribute('onclick'));
    } else {
        console.log('No toggle buttons found');
    }

    // Test modal elements
    const modal = document.getElementById('detailsModal');
    const modalTitle = document.getElementById('detailsModalLabel');
    const modalContent = document.getElementById('modalContent');

    console.log('Modal elements:', {
        modal: !!modal,
        modalTitle: !!modalTitle,
        modalContent: !!modalContent
    });

    // Test Bootstrap
    console.log('Bootstrap available:', typeof bootstrap !== 'undefined');
    if (typeof bootstrap !== 'undefined') {
        console.log('Bootstrap Modal class:', typeof bootstrap.Modal);
    }

    console.log('=== END DEBUG INFO ===');
}

// Add a simple click test for the first row
function testFirstRowToggle() {
    const firstRow = document.querySelector('.tree-row.level-1[data-has-children="true"]');
    if (firstRow) {
        console.log('Testing first row toggle:', firstRow.dataset.id);
        toggleTreeRow(firstRow.dataset.id);
    } else {
        console.log('No expandable first row found');
    }
}

// Expose functions to global scope for debugging
window.debugTree = debugTree;
window.testModal = testModal;
window.testFirstRowToggle = testFirstRowToggle;
</script>
@endsection
