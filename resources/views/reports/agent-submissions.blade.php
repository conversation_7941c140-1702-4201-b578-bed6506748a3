@extends('layouts.app')

@section('title', 'Agent Submission Statistics')

@section('content')
<div class="container-fluid">
    <div class="modern-dashboard">
        <!-- Compact Hero Header -->
        <div class="hero-header">
            <div class="hero-background">
                <div class="hero-pattern"></div>
                <div class="hero-gradient"></div>
            </div>
            <div class="hero-content">
                <div class="hero-main">
                    <div class="hero-badge">
                        <div class="pulse"></div>
                        Live Report
                    </div>
                    <h1 class="hero-title">
                        Agent Submission
                        <span class="hero-highlight">Analytics</span>
                    </h1>
                    <p class="hero-description">Comprehensive agent performance and submission tracking</p>
                    <div class="hero-stats">
                        <div class="hero-stat">
                            <div class="hero-stat-number">{{ number_format($summaryStats['total_agents']) }}</div>
                            <div class="hero-stat-label">Total Agents</div>
                        </div>
                        <div class="hero-stat">
                            <div class="hero-stat-number">{{ number_format($summaryStats['active_agents']) }}</div>
                            <div class="hero-stat-label">Active Agents</div>
                        </div>
                        <div class="hero-stat">
                            <div class="hero-stat-number">{{ $summaryStats['completion_rate'] }}%</div>
                            <div class="hero-stat-label">Completion Rate</div>
                        </div>
                    </div>
                </div>
                <div class="hero-actions">
                    <button class="hero-btn hero-btn-primary" onclick="refreshReport()">
                        <i class="bi bi-arrow-clockwise"></i>
                        <span>Refresh</span>
                    </button>
                    <button class="hero-btn hero-btn-outline" onclick="exportAgentReport()">
                        <i class="bi bi-download"></i>
                        <span>Export</span>
                    </button>
                </div>
            </div>
        </div>

        <!-- Compact Smart Filters -->
        <div class="smart-filters">
            <div class="filters-header">
                <div class="filters-title">
                    <i class="bi bi-funnel"></i>
                    <span>Smart Filters</span>
                </div>
                <button class="toggle-btn" onclick="toggleFilters()">
                    <i class="bi bi-chevron-down"></i>
                </button>
            </div>
            <div class="filters-content" id="filtersContent">
                <form method="GET" action="{{ route('reports.agent-submissions') }}" class="modern-form" id="reportForm">
                    <div class="form-grid">
                        <div class="form-section">
                            <div class="section-title">
                                <i class="bi bi-geo-alt"></i>
                                <span>Location Filters</span>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label class="form-label">District</label>
                                    <select name="district" class="modern-select" onchange="this.form.submit()">
                                        <option value="">All Districts</option>
                                        @foreach($filterOptions['districts'] as $district)
                                            <option value="{{ $district }}" {{ $filters['district'] == $district ? 'selected' : '' }}>
                                                {{ $district }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">County</label>
                                    <select name="county" class="modern-select" onchange="this.form.submit()">
                                        <option value="">All Counties</option>
                                        @foreach($filterOptions['counties'] as $county)
                                            <option value="{{ $county }}" {{ $filters['county'] == $county ? 'selected' : '' }}>
                                                {{ $county }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">Subcounty</label>
                                    <select name="subcounty" class="modern-select" onchange="this.form.submit()">
                                        <option value="">All Subcounties</option>
                                        @foreach($filterOptions['subcounties'] as $subcounty)
                                            <option value="{{ $subcounty }}" {{ $filters['subcounty'] == $subcounty ? 'selected' : '' }}>
                                                {{ $subcounty }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">Parish</label>
                                    <select name="parish" class="modern-select" onchange="this.form.submit()">
                                        <option value="">All Parishes</option>
                                        @foreach($filterOptions['parishes'] as $parish)
                                            <option value="{{ $parish }}" {{ $filters['parish'] == $parish ? 'selected' : '' }}>
                                                {{ $parish }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="form-section">
                            <div class="section-title">
                                <i class="bi bi-calendar-range"></i>
                                <span>Date Range</span>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label class="form-label">From Date</label>
                                    <input type="date" name="date_from" class="modern-input" value="{{ $filters['date_from'] }}" onchange="this.form.submit()">
                                </div>
                                <div class="form-group">
                                    <label class="form-label">To Date</label>
                                    <input type="date" name="date_to" class="modern-input" value="{{ $filters['date_to'] }}" onchange="this.form.submit()">
                                </div>
                                <div class="form-group">
                                    <label class="form-label">Per Page</label>
                                    <select name="per_page" class="modern-select" onchange="this.form.submit()">
                                        <option value="10" {{ $filters['per_page'] == 10 ? 'selected' : '' }}>10</option>
                                        <option value="25" {{ $filters['per_page'] == 25 ? 'selected' : '' }}>25</option>
                                        <option value="50" {{ $filters['per_page'] == 50 ? 'selected' : '' }}>50</option>
                                        <option value="100" {{ $filters['per_page'] == 100 ? 'selected' : '' }}>100</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">&nbsp;</label>
                                    <button type="button" class="action-btn clear-btn" onclick="clearFilters()">
                                        <i class="bi bi-x-circle"></i>
                                        Clear All
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Compact Analytics Dashboard -->
        <div class="analytics-dashboard">
            <div class="metrics-grid">
                <div class="metric-card metric-primary">
                    <div class="metric-header">
                        <div class="metric-icon">👥</div>
                        <div class="metric-trend positive">
                            <i class="bi bi-arrow-up"></i>
                            <span>{{ $summaryStats['completion_rate'] }}%</span>
                        </div>
                    </div>
                    <div class="metric-body">
                        <div class="metric-value">{{ number_format($summaryStats['total_agents']) }}</div>
                        <div class="metric-label">Total Agents</div>
                        <div class="metric-subtitle">Registered in system</div>
                    </div>
                    <div class="metric-footer">
                        <div class="metric-progress">
                            <div class="progress-bar" style="width: 100%"></div>
                        </div>
                    </div>
                </div>

                <div class="metric-card metric-success">
                    <div class="metric-header">
                        <div class="metric-icon">✅</div>
                        <div class="metric-trend positive">
                            <i class="bi bi-arrow-up"></i>
                            <span>Active</span>
                        </div>
                    </div>
                    <div class="metric-body">
                        <div class="metric-value">{{ number_format($summaryStats['active_agents']) }}</div>
                        <div class="metric-label">Active Agents</div>
                        <div class="metric-subtitle">With submissions</div>
                    </div>
                    <div class="metric-footer">
                        <div class="metric-progress">
                            @php
                                $activePercentage = $summaryStats['total_agents'] > 0 ? ($summaryStats['active_agents'] / $summaryStats['total_agents']) * 100 : 0;
                            @endphp
                            <div class="progress-bar" style="width: {{ $activePercentage }}%"></div>
                        </div>
                    </div>
                </div>

                <div class="metric-card metric-warning">
                    <div class="metric-header">
                        <div class="metric-icon">📊</div>
                        <div class="metric-trend neutral">
                            <i class="bi bi-bar-chart"></i>
                            <span>Avg</span>
                        </div>
                    </div>
                    <div class="metric-body">
                        <div class="metric-value">{{ $summaryStats['average_submissions'] }}</div>
                        <div class="metric-label">Avg Submissions</div>
                        <div class="metric-subtitle">Per active agent</div>
                    </div>
                    <div class="metric-footer">
                        <div class="metric-progress">
                            <div class="progress-bar" style="width: 75%"></div>
                        </div>
                    </div>
                </div>

                <div class="metric-card metric-info">
                    <div class="metric-header">
                        <div class="metric-icon">📈</div>
                        <div class="metric-trend positive">
                            <i class="bi bi-arrow-up"></i>
                            <span>Total</span>
                        </div>
                    </div>
                    <div class="metric-body">
                        <div class="metric-value">{{ number_format($summaryStats['total_submissions']) }}</div>
                        <div class="metric-label">Total Submissions</div>
                        <div class="metric-subtitle">All time count</div>
                    </div>
                    <div class="metric-footer">
                        <div class="metric-progress">
                            <div class="progress-bar" style="width: 90%"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Data Explorer -->
        <div class="data-explorer">
            <div class="explorer-header-compact">
                <div class="compact-title-inline">
                    <div class="compact-icon">
                        <i class="bi bi-people"></i>
                    </div>
                    <h3 class="compact-main-inline">Agent Data Explorer</h3>
                    <span class="compact-separator">•</span>
                    <span class="compact-count-inline">{{ count($reportData['data']) }} subcounties</span>
                </div>
                <div class="compact-controls">
                    <div class="simple-view-controls">
                        <button class="simple-btn expand-btn" onclick="expandAllRows()" title="Expand All (Ctrl+E)">
                            <i class="bi bi-arrows-expand"></i>
                            <span>Expand</span>
                        </button>
                        <button class="simple-btn collapse-btn" onclick="collapseAllRows()" title="Collapse All (Ctrl+C)">
                            <i class="bi bi-arrows-collapse"></i>
                            <span>Collapse</span>
                        </button>
                    </div>
                    <div class="explorer-summary">
                        <div class="summary-card">
                            <div class="summary-icon">
                                <i class="bi bi-graph-up"></i>
                            </div>
                            <div class="summary-content">
                                <div class="summary-value">{{ $summaryStats['completion_rate'] }}%</div>
                                <div class="summary-label">Completion</div>
                                <div class="summary-subtitle">Overall rate</div>
                            </div>
                            <div class="summary-progress">
                                <div class="progress-ring">
                                    <svg width="48" height="48" viewBox="0 0 48 48">
                                        <circle cx="24" cy="24" r="20" fill="none" stroke="#e2e8f0" stroke-width="4"/>
                                        <circle cx="24" cy="24" r="20" fill="none" stroke="#667eea" stroke-width="4" 
                                                stroke-dasharray="{{ 2 * pi() * 20 }}" 
                                                stroke-dashoffset="{{ 2 * pi() * 20 * (1 - $summaryStats['completion_rate'] / 100) }}"
                                                stroke-linecap="round" 
                                                transform="rotate(-90 24 24)"/>
                                    </svg>
                                    <div class="progress-text">{{ number_format($summaryStats['completion_rate'], 0) }}%</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Modern Tree Table -->
            <div class="explorer-content">
                <div class="table-container">
                    <table class="modern-tree-table" id="hierarchicalTable">
                        <thead class="table-head-modern">
                            <tr class="header-row">
                                <th class="col-location">
                                    <div class="column-header">
                                        <div class="header-icon">🌍</div>
                                        <div class="header-text">
                                            <span class="header-title">Location</span>
                                            <span class="header-subtitle">Administrative area</span>
                                        </div>
                                    </div>
                                </th>
                                <th class="col-agents">
                                    <div class="column-header">
                                        <div class="header-icon">👥</div>
                                        <div class="header-text">
                                            <span class="header-title">Agents</span>
                                            <span class="header-subtitle">Total / Active</span>
                                        </div>
                                    </div>
                                </th>
                                <th class="col-submissions">
                                    <div class="column-header">
                                        <div class="header-icon">📊</div>
                                        <div class="header-text">
                                            <span class="header-title">Submissions</span>
                                            <span class="header-subtitle">Total count</span>
                                        </div>
                                    </div>
                                </th>
                                <th class="col-completion">
                                    <div class="column-header">
                                        <div class="header-icon">📈</div>
                                        <div class="header-text">
                                            <span class="header-title">Completion</span>
                                            <span class="header-subtitle">Rate %</span>
                                        </div>
                                    </div>
                                </th>
                                <th class="col-last-submission">
                                    <div class="column-header">
                                        <div class="header-icon">⏰</div>
                                        <div class="header-text">
                                            <span class="header-title">Last Activity</span>
                                            <span class="header-subtitle">Recent submission</span>
                                        </div>
                                    </div>
                                </th>
                                <th class="col-actions">
                                    <div class="column-header">
                                        <div class="header-icon">⚡</div>
                                        <div class="header-text">
                                            <span class="header-title">Actions</span>
                                            <span class="header-subtitle">Quick tools</span>
                                        </div>
                                    </div>
                                </th>
                            </tr>
                        </thead>
                        <tbody class="table-body-modern">
                            @forelse($reportData['data'] as $subcounty)
                                <tr class="data-row modern-row level-1"
                                    data-id="{{ $subcounty['id'] }}"
                                    data-level="1"
                                    data-type="subcounty"
                                    data-name="{{ $subcounty['name'] }}"
                                    data-has-children="{{ $subcounty['has_children'] ? 'true' : 'false' }}"
                                    data-expanded="false">

                                    <td class="location-cell">
                                        <div class="location-content">
                                            @if($subcounty['has_children'])
                                                <div class="location-controls">
                                                    <button class="expand-btn" onclick="toggleTreeRow('{{ $subcounty['id'] }}')" title="Expand/Collapse">
                                                        <i class="bi bi-chevron-right"></i>
                                                    </button>
                                                </div>
                                            @else
                                                <div class="location-spacer"></div>
                                            @endif
                                            <div class="location-info">
                                                <div class="location-avatar">
                                                    <div class="avatar-circle subcounty">
                                                        {{ strtoupper(substr($subcounty['name'], 0, 2)) }}
                                                    </div>
                                                </div>
                                                <div class="location-details">
                                                    <div class="location-name">{{ $subcounty['name'] }}</div>
                                                    <div class="location-type">Subcounty</div>
                                                </div>
                                            </div>
                                        </div>
                                    </td>

                                    <td class="agents-cell">
                                        <div class="agents-info">
                                            <div class="agents-count">
                                                <span class="total-agents">{{ $subcounty['total_agents'] }}</span>
                                                <span class="agents-separator">/</span>
                                                <span class="active-agents">{{ $subcounty['active_agents'] }}</span>
                                            </div>
                                            <div class="agents-label">Total / Active</div>
                                        </div>
                                    </td>

                                    <td class="submissions-cell">
                                        <div class="submissions-info">
                                            <div class="submissions-count">{{ number_format($subcounty['total_submissions']) }}</div>
                                            <div class="submissions-label">Submissions</div>
                                        </div>
                                    </td>

                                    <td class="completion-cell">
                                        <div class="completion-info">
                                            <div class="completion-percentage">{{ $subcounty['completion_rate'] }}%</div>
                                            <div class="completion-bar">
                                                <div class="completion-fill" style="width: {{ $subcounty['completion_rate'] }}%"></div>
                                            </div>
                                        </div>
                                    </td>

                                    <td class="last-submission-cell">
                                        <div class="last-submission-info">
                                            @if($subcounty['last_submission'])
                                                <div class="submission-time">{{ \Carbon\Carbon::parse($subcounty['last_submission'])->format('M j, Y') }}</div>
                                                <div class="submission-relative">{{ \Carbon\Carbon::parse($subcounty['last_submission'])->diffForHumans() }}</div>
                                            @else
                                                <div class="no-submission">No submissions</div>
                                            @endif
                                        </div>
                                    </td>

                                    <td class="actions-cell">
                                        <div class="action-buttons">
                                            <button class="action-btn view-btn" onclick="viewAgentDetails('{{ $subcounty['type'] }}', '{{ $subcounty['name'] }}', '{{ $subcounty['id'] }}')" title="View Details">
                                                <i class="bi bi-eye"></i>
                                            </button>
                                            <button class="action-btn export-btn" onclick="exportLocationData('{{ $subcounty['id'] }}')" title="Export Data">
                                                <i class="bi bi-download"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            @empty
                                <tr class="empty-row">
                                    <td colspan="6" class="empty-cell">
                                        <div class="empty-state">
                                            <div class="empty-icon">📊</div>
                                            <div class="empty-title">No Agent Data Available</div>
                                            <div class="empty-subtitle">Try adjusting your filters or check back later</div>
                                        </div>
                                    </td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

@section('styles')
<style>
/* Modern Dashboard Base */
.modern-dashboard {
    min-height: 100vh;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    padding: 0;
    margin: 0;
}

/* Compact Hero Header */
.hero-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem 0;
    margin-bottom: 2rem;
    position: relative;
    overflow: hidden;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    opacity: 0.1;
}

.hero-pattern {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: radial-gradient(circle at 25% 25%, white 2px, transparent 2px);
    background-size: 50px 50px;
}

.hero-gradient {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, transparent 100%);
}

.hero-content {
    position: relative;
    z-index: 2;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 2rem;
}

.hero-main {
    flex: 1;
    min-width: 300px;
}

.hero-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: rgba(255, 255, 255, 0.2);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 600;
    margin-bottom: 1rem;
    backdrop-filter: blur(10px);
}

.pulse {
    width: 8px;
    height: 8px;
    background: #48bb78;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; transform: scale(1); }
    50% { opacity: 0.5; transform: scale(1.2); }
    100% { opacity: 1; transform: scale(1); }
}

.hero-title {
    font-size: 3rem;
    font-weight: 800;
    margin: 0 0 1rem 0;
    line-height: 1.1;
    background: linear-gradient(135deg, #ffffff 0%, #f1f5f9 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-highlight {
    background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-description {
    font-size: 1.125rem;
    opacity: 0.9;
    margin-bottom: 2rem;
    line-height: 1.6;
}

.hero-stats {
    display: flex;
    gap: 2rem;
    flex-wrap: wrap;
}

.hero-stat {
    text-align: center;
}

.hero-stat-number {
    font-size: 2rem;
    font-weight: 700;
    line-height: 1;
    margin-bottom: 0.25rem;
}

.hero-stat-label {
    font-size: 0.875rem;
    opacity: 0.8;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.hero-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.hero-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.875rem 1.5rem;
    border-radius: 12px;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    font-size: 0.875rem;
}

.hero-btn-primary {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    backdrop-filter: blur(10px);
}

.hero-btn-primary:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
}

.hero-btn-outline {
    background: transparent;
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
}

.hero-btn-outline:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-2px);
}

/* Smart Filters */
.smart-filters {
    background: white;
    border-radius: 20px;
    margin: 0 2rem 2rem 2rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(0, 0, 0, 0.05);
    overflow: hidden;
}

.filters-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 2rem;
    background: linear-gradient(135deg, #f8fafc 0%, #edf2f7 100%);
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.filters-title {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 1.125rem;
    font-weight: 700;
    color: #2d3748;
}

.filters-title i {
    color: #667eea;
    font-size: 1.25rem;
}

.toggle-btn {
    background: none;
    border: none;
    color: #667eea;
    font-size: 1.25rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 8px;
    transition: all 0.2s ease;
}

.toggle-btn:hover {
    background: rgba(102, 126, 234, 0.1);
}

.filters-content {
    padding: 2rem;
}

.modern-form {
    width: 100%;
}

.form-grid {
    display: grid;
    gap: 2rem;
}

.form-section {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.section-title {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 1rem;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 0.5rem;
}

.section-title i {
    color: #667eea;
    font-size: 1.125rem;
}

.form-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.form-label {
    font-size: 0.875rem;
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.25rem;
}

.modern-select,
.modern-input {
    padding: 0.75rem 1rem;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    font-size: 0.875rem;
    background: white;
    transition: all 0.2s ease;
    outline: none;
}

.modern-select:focus,
.modern-input:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.action-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1rem;
    background: #f3f4f6;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    font-size: 0.875rem;
    font-weight: 500;
    color: #374151;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
}

.clear-btn:hover {
    background: #fee2e2;
    border-color: #fca5a5;
    color: #dc2626;
}

/* Analytics Dashboard */
.analytics-dashboard {
    margin: 0 2rem 2rem 2rem;
}

.metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
}

.metric-card {
    background: white;
    border-radius: 16px;
    padding: 1.5rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.metric-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.metric-card.metric-primary::before {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.metric-card.metric-success::before {
    background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
}

.metric-card.metric-warning::before {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.metric-card.metric-info::before {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
}

.metric-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.metric-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.metric-icon {
    font-size: 2rem;
    line-height: 1;
}

.metric-trend {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    font-size: 0.75rem;
    font-weight: 600;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
}

.metric-trend.positive {
    background: rgba(72, 187, 120, 0.1);
    color: #38a169;
}

.metric-trend.neutral {
    background: rgba(245, 158, 11, 0.1);
    color: #d97706;
}

.metric-body {
    margin-bottom: 1rem;
}

.metric-value {
    font-size: 2.5rem;
    font-weight: 800;
    line-height: 1;
    color: #1a202c;
    margin-bottom: 0.25rem;
}

.metric-label {
    font-size: 1rem;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 0.125rem;
}

.metric-subtitle {
    font-size: 0.875rem;
    color: #718096;
}

.metric-footer {
    margin-top: 1rem;
}

.metric-progress {
    width: 100%;
    height: 6px;
    background: #e2e8f0;
    border-radius: 3px;
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 3px;
    transition: width 0.3s ease;
}

.metric-success .progress-bar {
    background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
}

.metric-warning .progress-bar {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.metric-info .progress-bar {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
}

/* Data Explorer */
.data-explorer {
    background: white;
    border-radius: 20px;
    margin: 0 2rem 2rem 2rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(0, 0, 0, 0.05);
    overflow: hidden;
}

.explorer-container {
    display: flex;
    flex-direction: column;
}

/* Compact Explorer Header */
.explorer-header-compact {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.5rem;
    background: linear-gradient(135deg, #f8fafc 0%, #edf2f7 100%);
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    flex-wrap: wrap;
    gap: 1rem;
}

/* Inline Compact Title */
.compact-title-inline {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.compact-icon {
    width: 32px;
    height: 32px;
    background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1rem;
    flex-shrink: 0;
}

.compact-main-inline {
    font-size: 1.125rem;
    font-weight: 700;
    color: #2d3748;
    margin: 0;
    line-height: 1;
    white-space: nowrap;
}

.compact-separator {
    color: #cbd5e0;
    font-weight: 400;
    font-size: 1rem;
    margin: 0 0.25rem;
}

.compact-count-inline {
    font-size: 0.875rem;
    color: #64748b;
    font-weight: 500;
    background: rgba(100, 116, 139, 0.1);
    padding: 0.25rem 0.75rem;
    border-radius: 16px;
    white-space: nowrap;
}

.compact-controls {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    flex-wrap: wrap;
}

/* Simplified View Controls */
.simple-view-controls {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.simple-view-controls .simple-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 0.875rem;
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    font-size: 0.875rem;
    font-weight: 500;
    color: #4a5568;
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap;
    text-decoration: none;
}

.simple-view-controls .simple-btn:hover {
    background: #f7fafc;
    border-color: #cbd5e0;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.simple-view-controls .simple-btn:active {
    transform: translateY(0);
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

.simple-view-controls .simple-btn i {
    font-size: 0.875rem;
    color: #667eea;
}

.simple-view-controls .simple-btn.expand-btn:hover {
    border-color: #10b981;
    color: #059669;
}

.simple-view-controls .simple-btn.expand-btn:hover i {
    color: #10b981;
}

.simple-view-controls .simple-btn.collapse-btn:hover {
    border-color: #f59e0b;
    color: #d97706;
}

.simple-view-controls .simple-btn.collapse-btn:hover i {
    color: #f59e0b;
}

/* Explorer Summary */
.explorer-summary {
    display: flex;
    gap: 1rem;
}

.summary-card {
    display: flex;
    align-items: center;
    gap: 1rem;
    background: white;
    padding: 1rem;
    border-radius: 12px;
    border: 1px solid #e2e8f0;
    min-width: 200px;
}

.summary-icon {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.25rem;
    flex-shrink: 0;
}

.summary-content {
    flex: 1;
}

.summary-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1a202c;
    line-height: 1;
    margin-bottom: 0.25rem;
}

.summary-label {
    font-size: 0.875rem;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 0.125rem;
}

.summary-subtitle {
    font-size: 0.75rem;
    color: #718096;
}

.summary-progress {
    position: relative;
}

.progress-ring {
    position: relative;
    width: 48px;
    height: 48px;
}

.progress-ring svg {
    transform: rotate(-90deg);
}

.progress-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 0.75rem;
    font-weight: 600;
    color: #667eea;
}

/* Modern Tree Table */
.explorer-content {
    padding: 0;
}

.table-container {
    overflow-x: auto;
    max-height: 70vh;
    border-radius: 0 0 20px 20px;
}

.modern-tree-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.875rem;
    background: white;
}

.table-head-modern {
    background: linear-gradient(135deg, #f8fafc 0%, #edf2f7 100%);
    position: sticky;
    top: 0;
    z-index: 10;
}

.header-row {
    border-bottom: 2px solid #e2e8f0;
}

.header-row th {
    padding: 1rem 0.75rem;
    text-align: left;
    font-weight: 600;
    color: #2d3748;
    border-bottom: 2px solid #e2e8f0;
    position: relative;
}

.column-header {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.header-icon {
    font-size: 1.125rem;
    opacity: 0.7;
}

.header-text {
    display: flex;
    flex-direction: column;
    gap: 0.125rem;
}

.header-title {
    font-size: 0.875rem;
    font-weight: 600;
    color: #2d3748;
    line-height: 1;
}

.header-subtitle {
    font-size: 0.75rem;
    color: #718096;
    font-weight: 400;
    line-height: 1;
}

/* Table Body */
.table-body-modern {
    background: white;
}

.data-row {
    border-bottom: 1px solid #f1f5f9;
    transition: all 0.2s ease;
}

.data-row:hover {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

.data-row.level-1 {
    background: white;
}

.data-row.level-2 {
    background: #fafbfc;
}

.data-row.level-3 {
    background: #f8fafc;
}

/* Location Cell */
.location-cell {
    padding: 1rem 0.75rem;
    min-width: 300px;
}

.location-content {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.location-controls {
    display: flex;
    align-items: center;
}

.location-controls .expand-btn {
    width: 28px;
    height: 28px;
    border: none;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.875rem;
}

.location-controls .expand-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.location-controls .expand-btn.expanded {
    transform: rotate(90deg);
    background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
}

.location-controls .expand-btn.expanded:hover {
    background: linear-gradient(135deg, #38a169 0%, #2f855a 100%);
}

.location-spacer {
    width: 28px;
    height: 28px;
}

.location-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    flex: 1;
}

.location-avatar {
    flex-shrink: 0;
}

.avatar-circle {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    font-weight: 600;
    color: white;
    text-transform: uppercase;
}

.avatar-circle.subcounty {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.avatar-circle.parish {
    background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
}

.avatar-circle.village {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.location-details {
    flex: 1;
}

.location-name {
    font-size: 0.875rem;
    font-weight: 600;
    color: #1a202c;
    line-height: 1.2;
    margin-bottom: 0.125rem;
}

.location-type {
    font-size: 0.75rem;
    color: #718096;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-weight: 500;
}

/* Agent-specific styling adjustments */
.agents-cell {
    text-align: center;
    padding: 1rem 0.75rem;
}

.agents-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.25rem;
}

.agents-count {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1rem;
    font-weight: 600;
}

.total-agents {
    color: #2d3748;
}

.agents-separator {
    color: #cbd5e0;
    font-weight: 400;
}

.active-agents {
    color: #48bb78;
}

.agents-label {
    font-size: 0.75rem;
    color: #718096;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Other cells */
.submissions-cell,
.completion-cell,
.last-submission-cell,
.actions-cell {
    padding: 1rem 0.75rem;
    text-align: center;
}

.submissions-info,
.completion-info,
.last-submission-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.25rem;
}

.submissions-count,
.completion-percentage {
    font-size: 1rem;
    font-weight: 600;
    color: #1a202c;
    line-height: 1;
}

.submissions-label,
.completion-label {
    font-size: 0.75rem;
    color: #718096;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.completion-bar {
    width: 60px;
    height: 4px;
    background: #e2e8f0;
    border-radius: 2px;
    overflow: hidden;
    margin-top: 0.25rem;
}

.completion-fill {
    height: 100%;
    background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
    border-radius: 2px;
    transition: width 0.3s ease;
}

.submission-time {
    font-size: 0.875rem;
    font-weight: 600;
    color: #1a202c;
    line-height: 1;
}

.submission-relative {
    font-size: 0.75rem;
    color: #718096;
}

.no-submission {
    font-size: 0.875rem;
    color: #9ca3af;
    font-style: italic;
}

/* Action Buttons */
.action-buttons {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
}

.action-btn {
    width: 32px;
    height: 32px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.875rem;
}

.view-btn {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    color: white;
}

.view-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.export-btn {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
}

.export-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

/* Empty State */
.empty-row {
    background: white;
}

.empty-cell {
    padding: 4rem 2rem;
    text-align: center;
}

.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
    max-width: 400px;
    margin: 0 auto;
}

.empty-icon {
    font-size: 4rem;
    opacity: 0.5;
}

.empty-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #374151;
}

.empty-subtitle {
    font-size: 0.875rem;
    color: #6b7280;
    text-align: center;
    line-height: 1.5;
}

/* Enhanced Pagination */
.pagination-modern {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 2rem;
    background: linear-gradient(135deg, #f8fafc 0%, #edf2f7 100%);
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    flex-wrap: wrap;
    gap: 1rem;
}

.pagination-info-modern {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.info-stats {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.stats-icon {
    font-size: 1.25rem;
    color: #667eea;
}

.stats-content {
    display: flex;
    flex-direction: column;
    gap: 0.125rem;
}

.stats-primary {
    font-size: 0.875rem;
    font-weight: 600;
    color: #2d3748;
    line-height: 1;
}

.stats-secondary {
    font-size: 0.75rem;
    color: #718096;
    line-height: 1;
}

.pagination-controls-modern {
    display: flex;
    align-items: center;
}

/* Focus States */
.location-controls .expand-btn:focus,
.simple-view-controls .simple-btn:focus,
.control-btn:focus,
.action-btn:focus,
.modern-select:focus,
.modern-input:focus {
    outline: 2px solid #667eea;
    outline-offset: 2px;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .hero-content {
        flex-direction: column;
        text-align: center;
        gap: 1.5rem;
    }

    .hero-stats {
        justify-content: center;
    }

    .hero-actions {
        justify-content: center;
    }

    .metrics-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1rem;
    }

    .compact-title-inline {
        justify-content: center;
        flex-wrap: wrap;
        gap: 0.5rem;
    }

    .compact-controls {
        justify-content: center;
        gap: 1rem;
    }

    .simple-view-controls {
        gap: 0.375rem;
    }

    .simple-view-controls .simple-btn {
        padding: 0.375rem 0.75rem;
        font-size: 0.8rem;
    }

    .simple-view-controls .simple-btn i {
        font-size: 0.8rem;
    }
}

@media (max-width: 768px) {
    .modern-dashboard {
        padding: 0;
    }

    .hero-header {
        padding: 1.5rem 0;
        margin-bottom: 1rem;
    }

    .hero-content {
        padding: 0 1rem;
    }

    .hero-title {
        font-size: 2rem;
    }

    .hero-description {
        font-size: 1rem;
    }

    .hero-stats {
        gap: 1rem;
    }

    .hero-stat-number {
        font-size: 1.5rem;
    }

    .smart-filters,
    .analytics-dashboard,
    .data-explorer {
        margin: 0 1rem 1rem 1rem;
    }

    .filters-header,
    .filters-content {
        padding: 1rem;
    }

    .form-row {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }

    .metrics-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .metric-card {
        padding: 0.75rem;
    }

    .explorer-header-compact {
        flex-direction: column;
        align-items: stretch;
        gap: 0.75rem;
    }

    .compact-title-inline {
        gap: 0.5rem;
    }

    .compact-icon {
        width: 28px;
        height: 28px;
        font-size: 0.875rem;
    }

    .compact-main-inline {
        font-size: 1rem;
    }

    .compact-count-inline {
        font-size: 0.75rem;
        padding: 0.2rem 0.6rem;
    }

    .compact-separator {
        display: none;
    }

    .simple-view-controls {
        gap: 0.25rem;
    }

    .simple-view-controls .simple-btn {
        padding: 0.375rem 0.625rem;
        font-size: 0.75rem;
    }

    .simple-view-controls .simple-btn span {
        display: none;
    }

    .simple-view-controls .simple-btn i {
        font-size: 0.875rem;
    }

    .table-container {
        max-height: 60vh;
    }

    .modern-tree-table {
        font-size: 0.8rem;
    }

    .header-row th {
        padding: 0.75rem 0.5rem;
    }

    .location-cell,
    .agents-cell,
    .submissions-cell,
    .completion-cell,
    .last-submission-cell,
    .actions-cell {
        padding: 0.75rem 0.5rem;
    }

    .location-content {
        gap: 0.5rem;
    }

    .location-info {
        gap: 0.5rem;
    }

    .avatar-circle {
        width: 32px;
        height: 32px;
        font-size: 0.7rem;
    }

    .location-name {
        font-size: 0.8rem;
    }

    .location-type {
        font-size: 0.7rem;
    }

    .agents-count,
    .submissions-count,
    .completion-percentage {
        font-size: 0.875rem;
    }

    .agents-label,
    .submissions-label,
    .completion-label {
        font-size: 0.7rem;
    }

    .completion-bar {
        width: 50px;
        height: 3px;
    }

    .submission-time {
        font-size: 0.8rem;
    }

    .submission-relative {
        font-size: 0.7rem;
    }

    .action-buttons {
        gap: 0.25rem;
    }

    .action-btn {
        width: 28px;
        height: 28px;
        font-size: 0.8rem;
    }

    .pagination-modern {
        flex-direction: column;
        gap: 1rem;
        padding: 1rem;
    }

    .pagination-info-modern {
        justify-content: center;
    }
}

/* Print Styles */
@media print {
    .hero-header,
    .smart-filters,
    .analytics-dashboard {
        display: none;
    }

    .data-explorer {
        box-shadow: none;
        border: 1px solid #ccc;
    }

    .modern-tree-table {
        font-size: 0.75rem;
    }

    .data-row:hover {
        background: white;
    }
}
</style>
@endsection

<script>
// Tree table functionality (same as polling station report)
window.expandedRows = new Set();
window.loadedChildren = new Map();

// Export function for agent report
window.exportAgentReport = function() {
    console.log('Export agent report clicked');

    // Get current filter values
    const form = document.getElementById('reportForm');
    const formData = new FormData(form);

    // Build query string
    const params = new URLSearchParams();
    for (let [key, value] of formData.entries()) {
        if (value) {
            params.append(key, value);
        }
    }

    // Create download URL
    const exportUrl = '/reports/agent-submissions/export?' + params.toString();

    // Trigger download
    window.location.href = exportUrl;
};

// View agent details function
window.viewAgentDetails = function(type, name, id) {
    console.log('View agent details:', type, name, id);
    // Implementation for viewing agent details modal
    alert('Agent details for ' + name + ' (Feature coming soon)');
};

// Export location data function
window.exportLocationData = function(locationId) {
    console.log('Export location data:', locationId);
    // Implementation for exporting specific location data
    alert('Export location data (Feature coming soon)');
};

// Clear filters function
window.clearFilters = function() {
    window.location.href = '{{ route("reports.agent-submissions") }}';
};

// Toggle filters function
window.toggleFilters = function() {
    const content = document.getElementById('filtersContent');
    const button = document.querySelector('.toggle-btn i');

    if (content.style.display === 'none') {
        content.style.display = 'block';
        button.className = 'bi bi-chevron-up';
    } else {
        content.style.display = 'none';
        button.className = 'bi bi-chevron-down';
    }
};

// Refresh report function
window.refreshReport = function() {
    window.location.reload();
};

// Tree table expand/collapse functionality
window.toggleTreeRow = function(rowId) {
    console.log('Toggle tree row:', rowId);
    const row = document.querySelector(`tr[data-id="${rowId}"]`);
    if (!row) {
        console.error('Row not found:', rowId);
        return;
    }

    const isExpanded = row.dataset.expanded === 'true';
    const toggleBtn = row.querySelector('.expand-btn');

    if (isExpanded) {
        // Collapse row
        row.dataset.expanded = 'false';
        toggleBtn.classList.remove('expanded');
        expandedRows.delete(rowId);

        // Hide child rows
        hideChildRows(rowId);
    } else {
        // Expand row
        row.dataset.expanded = 'true';
        toggleBtn.classList.add('expanded');
        expandedRows.add(rowId);

        // Show/load child rows
        showChildRows(rowId);
    }
};

// Expand all rows function
window.expandAllRows = function() {
    console.log('Expand all rows');
    const allRows = document.querySelectorAll('tr[data-has-children="true"]');
    allRows.forEach(row => {
        const rowId = row.dataset.id;
        if (row.dataset.expanded !== 'true') {
            toggleTreeRow(rowId);
        }
    });
};

// Collapse all rows function
window.collapseAllRows = function() {
    console.log('Collapse all rows');
    const allRows = document.querySelectorAll('tr[data-has-children="true"]');
    allRows.forEach(row => {
        const rowId = row.dataset.id;
        if (row.dataset.expanded === 'true') {
            toggleTreeRow(rowId);
        }
    });
};

// Helper functions for child row management
function hideChildRows(parentId) {
    const childRows = document.querySelectorAll(`tr[data-parent-id="${parentId}"]`);
    childRows.forEach(row => {
        row.style.display = 'none';
        // Also hide grandchildren if they exist
        const childId = row.dataset.id;
        hideChildRows(childId);
    });
}

function showChildRows(parentId) {
    const childRows = document.querySelectorAll(`tr[data-parent-id="${parentId}"]`);
    childRows.forEach(row => {
        row.style.display = 'table-row';
    });
}

// Keyboard shortcuts
document.addEventListener('keydown', function(e) {
    if (e.ctrlKey && e.key === 'e') {
        e.preventDefault();
        expandAllRows();
    } else if (e.ctrlKey && e.key === 'c') {
        e.preventDefault();
        collapseAllRows();
    }
});

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    console.log('Agent submissions report loaded');

    // Initialize filters toggle state
    const filtersContent = document.getElementById('filtersContent');
    if (filtersContent) {
        filtersContent.style.display = 'block'; // Show filters by default
    }
});
</script>
@endsection
