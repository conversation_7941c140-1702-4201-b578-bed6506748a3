@extends('layouts.app')

@section('title', 'Agent Submission Statistics')

@section('content')
<div class="container-fluid">
    <div class="modern-dashboard">
        <!-- Compact Hero Header -->
        <div class="hero-header">
            <div class="hero-background">
                <div class="hero-pattern"></div>
                <div class="hero-gradient"></div>
            </div>
            <div class="hero-content">
                <div class="hero-main">
                    <div class="hero-badge">
                        <div class="pulse"></div>
                        Live Report
                    </div>
                    <h1 class="hero-title">
                        Agent Submission
                        <span class="hero-highlight">Analytics</span>
                    </h1>
                    <p class="hero-description">Comprehensive agent performance and submission tracking</p>
                    <div class="hero-stats">
                        <div class="hero-stat">
                            <div class="hero-stat-number">{{ number_format($summaryStats['total_agents']) }}</div>
                            <div class="hero-stat-label">Total Agents</div>
                        </div>
                        <div class="hero-stat">
                            <div class="hero-stat-number">{{ number_format($summaryStats['active_agents']) }}</div>
                            <div class="hero-stat-label">Active Agents</div>
                        </div>
                        <div class="hero-stat">
                            <div class="hero-stat-number">{{ $summaryStats['completion_rate'] }}%</div>
                            <div class="hero-stat-label">Completion Rate</div>
                        </div>
                    </div>
                </div>
                <div class="hero-actions">
                    <button class="hero-btn hero-btn-primary" onclick="refreshReport()">
                        <i class="bi bi-arrow-clockwise"></i>
                        <span>Refresh</span>
                    </button>
                    <button class="hero-btn hero-btn-outline" onclick="exportAgentReport()">
                        <i class="bi bi-download"></i>
                        <span>Export</span>
                    </button>
                </div>
            </div>
        </div>

        <!-- Compact Smart Filters -->
        <div class="smart-filters">
            <div class="filters-header">
                <div class="filters-title">
                    <i class="bi bi-funnel"></i>
                    <span>Smart Filters</span>
                </div>
                <button class="toggle-btn" onclick="toggleFilters()">
                    <i class="bi bi-chevron-down"></i>
                </button>
            </div>
            <div class="filters-content" id="filtersContent">
                <form method="GET" action="{{ route('reports.agent-submissions') }}" class="modern-form" id="reportForm">
                    <div class="form-grid">
                        <div class="form-section">
                            <div class="section-title">
                                <i class="bi bi-geo-alt"></i>
                                <span>Location Filters</span>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label class="form-label">District</label>
                                    <select name="district" class="modern-select" onchange="this.form.submit()">
                                        <option value="">All Districts</option>
                                        @foreach($filterOptions['districts'] as $district)
                                            <option value="{{ $district }}" {{ $filters['district'] == $district ? 'selected' : '' }}>
                                                {{ $district }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">County</label>
                                    <select name="county" class="modern-select" onchange="this.form.submit()">
                                        <option value="">All Counties</option>
                                        @foreach($filterOptions['counties'] as $county)
                                            <option value="{{ $county }}" {{ $filters['county'] == $county ? 'selected' : '' }}>
                                                {{ $county }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">Subcounty</label>
                                    <select name="subcounty" class="modern-select" onchange="this.form.submit()">
                                        <option value="">All Subcounties</option>
                                        @foreach($filterOptions['subcounties'] as $subcounty)
                                            <option value="{{ $subcounty }}" {{ $filters['subcounty'] == $subcounty ? 'selected' : '' }}>
                                                {{ $subcounty }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">Parish</label>
                                    <select name="parish" class="modern-select" onchange="this.form.submit()">
                                        <option value="">All Parishes</option>
                                        @foreach($filterOptions['parishes'] as $parish)
                                            <option value="{{ $parish }}" {{ $filters['parish'] == $parish ? 'selected' : '' }}>
                                                {{ $parish }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="form-section">
                            <div class="section-title">
                                <i class="bi bi-calendar-range"></i>
                                <span>Date Range</span>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label class="form-label">From Date</label>
                                    <input type="date" name="date_from" class="modern-input" value="{{ $filters['date_from'] }}" onchange="this.form.submit()">
                                </div>
                                <div class="form-group">
                                    <label class="form-label">To Date</label>
                                    <input type="date" name="date_to" class="modern-input" value="{{ $filters['date_to'] }}" onchange="this.form.submit()">
                                </div>
                                <div class="form-group">
                                    <label class="form-label">Per Page</label>
                                    <select name="per_page" class="modern-select" onchange="this.form.submit()">
                                        <option value="10" {{ $filters['per_page'] == 10 ? 'selected' : '' }}>10</option>
                                        <option value="25" {{ $filters['per_page'] == 25 ? 'selected' : '' }}>25</option>
                                        <option value="50" {{ $filters['per_page'] == 50 ? 'selected' : '' }}>50</option>
                                        <option value="100" {{ $filters['per_page'] == 100 ? 'selected' : '' }}>100</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">&nbsp;</label>
                                    <button type="button" class="action-btn clear-btn" onclick="clearFilters()">
                                        <i class="bi bi-x-circle"></i>
                                        Clear All
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Compact Analytics Dashboard -->
        <div class="analytics-dashboard">
            <div class="metrics-grid">
                <div class="metric-card metric-primary">
                    <div class="metric-header">
                        <div class="metric-icon">👥</div>
                        <div class="metric-trend positive">
                            <i class="bi bi-arrow-up"></i>
                            <span>{{ $summaryStats['completion_rate'] }}%</span>
                        </div>
                    </div>
                    <div class="metric-body">
                        <div class="metric-value">{{ number_format($summaryStats['total_agents']) }}</div>
                        <div class="metric-label">Total Agents</div>
                        <div class="metric-subtitle">Registered in system</div>
                    </div>
                    <div class="metric-footer">
                        <div class="metric-progress">
                            <div class="progress-bar" style="width: 100%"></div>
                        </div>
                    </div>
                </div>

                <div class="metric-card metric-success">
                    <div class="metric-header">
                        <div class="metric-icon">✅</div>
                        <div class="metric-trend positive">
                            <i class="bi bi-arrow-up"></i>
                            <span>Active</span>
                        </div>
                    </div>
                    <div class="metric-body">
                        <div class="metric-value">{{ number_format($summaryStats['active_agents']) }}</div>
                        <div class="metric-label">Active Agents</div>
                        <div class="metric-subtitle">With submissions</div>
                    </div>
                    <div class="metric-footer">
                        <div class="metric-progress">
                            @php
                                $activePercentage = $summaryStats['total_agents'] > 0 ? ($summaryStats['active_agents'] / $summaryStats['total_agents']) * 100 : 0;
                            @endphp
                            <div class="progress-bar" style="width: {{ $activePercentage }}%"></div>
                        </div>
                    </div>
                </div>

                <div class="metric-card metric-warning">
                    <div class="metric-header">
                        <div class="metric-icon">📊</div>
                        <div class="metric-trend neutral">
                            <i class="bi bi-bar-chart"></i>
                            <span>Avg</span>
                        </div>
                    </div>
                    <div class="metric-body">
                        <div class="metric-value">{{ $summaryStats['average_submissions'] }}</div>
                        <div class="metric-label">Avg Submissions</div>
                        <div class="metric-subtitle">Per active agent</div>
                    </div>
                    <div class="metric-footer">
                        <div class="metric-progress">
                            <div class="progress-bar" style="width: 75%"></div>
                        </div>
                    </div>
                </div>

                <div class="metric-card metric-info">
                    <div class="metric-header">
                        <div class="metric-icon">📈</div>
                        <div class="metric-trend positive">
                            <i class="bi bi-arrow-up"></i>
                            <span>Total</span>
                        </div>
                    </div>
                    <div class="metric-body">
                        <div class="metric-value">{{ number_format($summaryStats['total_submissions']) }}</div>
                        <div class="metric-label">Total Submissions</div>
                        <div class="metric-subtitle">All time count</div>
                    </div>
                    <div class="metric-footer">
                        <div class="metric-progress">
                            <div class="progress-bar" style="width: 90%"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Data Explorer -->
        <div class="data-explorer">
            <div class="explorer-header-compact">
                <div class="compact-title-inline">
                    <div class="compact-icon">
                        <i class="bi bi-people"></i>
                    </div>
                    <h3 class="compact-main-inline">Agent Data Explorer</h3>
                    <span class="compact-separator">•</span>
                    <span class="compact-count-inline">{{ count($reportData['data']) }} subcounties</span>
                </div>
                <div class="compact-controls">
                    <div class="simple-view-controls">
                        <button class="simple-btn expand-btn" onclick="expandAllRows()" title="Expand All (Ctrl+E)">
                            <i class="bi bi-arrows-expand"></i>
                            <span>Expand</span>
                        </button>
                        <button class="simple-btn collapse-btn" onclick="collapseAllRows()" title="Collapse All (Ctrl+C)">
                            <i class="bi bi-arrows-collapse"></i>
                            <span>Collapse</span>
                        </button>
                    </div>
                    <div class="explorer-summary">
                        <div class="summary-card">
                            <div class="summary-icon">
                                <i class="bi bi-graph-up"></i>
                            </div>
                            <div class="summary-content">
                                <div class="summary-value">{{ $summaryStats['completion_rate'] }}%</div>
                                <div class="summary-label">Completion</div>
                                <div class="summary-subtitle">Overall rate</div>
                            </div>
                            <div class="summary-progress">
                                <div class="progress-ring">
                                    <svg width="48" height="48" viewBox="0 0 48 48">
                                        <circle cx="24" cy="24" r="20" fill="none" stroke="#e2e8f0" stroke-width="4"/>
                                        <circle cx="24" cy="24" r="20" fill="none" stroke="#667eea" stroke-width="4" 
                                                stroke-dasharray="{{ 2 * pi() * 20 }}" 
                                                stroke-dashoffset="{{ 2 * pi() * 20 * (1 - $summaryStats['completion_rate'] / 100) }}"
                                                stroke-linecap="round" 
                                                transform="rotate(-90 24 24)"/>
                                    </svg>
                                    <div class="progress-text">{{ number_format($summaryStats['completion_rate'], 0) }}%</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Modern Tree Table -->
            <div class="explorer-content">
                <div class="table-container">
                    <table class="modern-tree-table" id="hierarchicalTable">
                        <thead class="table-head-modern">
                            <tr class="header-row">
                                <th class="col-location">
                                    <div class="column-header">
                                        <div class="header-icon">🌍</div>
                                        <div class="header-text">
                                            <span class="header-title">Location</span>
                                            <span class="header-subtitle">Administrative area</span>
                                        </div>
                                    </div>
                                </th>
                                <th class="col-agents">
                                    <div class="column-header">
                                        <div class="header-icon">👥</div>
                                        <div class="header-text">
                                            <span class="header-title">Agents</span>
                                            <span class="header-subtitle">Total / Active</span>
                                        </div>
                                    </div>
                                </th>
                                <th class="col-submissions">
                                    <div class="column-header">
                                        <div class="header-icon">📊</div>
                                        <div class="header-text">
                                            <span class="header-title">Submissions</span>
                                            <span class="header-subtitle">Total count</span>
                                        </div>
                                    </div>
                                </th>
                                <th class="col-completion">
                                    <div class="column-header">
                                        <div class="header-icon">📈</div>
                                        <div class="header-text">
                                            <span class="header-title">Completion</span>
                                            <span class="header-subtitle">Rate %</span>
                                        </div>
                                    </div>
                                </th>
                                <th class="col-last-submission">
                                    <div class="column-header">
                                        <div class="header-icon">⏰</div>
                                        <div class="header-text">
                                            <span class="header-title">Last Activity</span>
                                            <span class="header-subtitle">Recent submission</span>
                                        </div>
                                    </div>
                                </th>
                                <th class="col-actions">
                                    <div class="column-header">
                                        <div class="header-icon">⚡</div>
                                        <div class="header-text">
                                            <span class="header-title">Actions</span>
                                            <span class="header-subtitle">Quick tools</span>
                                        </div>
                                    </div>
                                </th>
                            </tr>
                        </thead>
                        <tbody class="table-body-modern">
                            @forelse($reportData['data'] as $subcounty)
                                <tr class="data-row modern-row level-1"
                                    data-id="{{ $subcounty['id'] }}"
                                    data-level="1"
                                    data-type="subcounty"
                                    data-name="{{ $subcounty['name'] }}"
                                    data-has-children="{{ $subcounty['has_children'] ? 'true' : 'false' }}"
                                    data-expanded="false">

                                    <td class="location-cell">
                                        <div class="location-content">
                                            @if($subcounty['has_children'])
                                                <div class="location-controls">
                                                    <button class="expand-btn" onclick="toggleTreeRow('{{ $subcounty['id'] }}')" title="Expand/Collapse">
                                                        <i class="bi bi-chevron-right"></i>
                                                    </button>
                                                </div>
                                            @else
                                                <div class="location-spacer"></div>
                                            @endif
                                            <div class="location-info">
                                                <div class="location-avatar">
                                                    <div class="avatar-circle subcounty">
                                                        {{ strtoupper(substr($subcounty['name'], 0, 2)) }}
                                                    </div>
                                                </div>
                                                <div class="location-details">
                                                    <div class="location-name">{{ $subcounty['name'] }}</div>
                                                    <div class="location-type">Subcounty</div>
                                                </div>
                                            </div>
                                        </div>
                                    </td>

                                    <td class="agents-cell">
                                        <div class="agents-info">
                                            <div class="agents-count">
                                                <span class="total-agents">{{ $subcounty['total_agents'] }}</span>
                                                <span class="agents-separator">/</span>
                                                <span class="active-agents">{{ $subcounty['active_agents'] }}</span>
                                            </div>
                                            <div class="agents-label">Total / Active</div>
                                        </div>
                                    </td>

                                    <td class="submissions-cell">
                                        <div class="submissions-info">
                                            <div class="submissions-count">{{ number_format($subcounty['total_submissions']) }}</div>
                                            <div class="submissions-label">Submissions</div>
                                        </div>
                                    </td>

                                    <td class="completion-cell">
                                        <div class="completion-info">
                                            <div class="completion-percentage">{{ $subcounty['completion_rate'] }}%</div>
                                            <div class="completion-bar">
                                                <div class="completion-fill" style="width: {{ $subcounty['completion_rate'] }}%"></div>
                                            </div>
                                        </div>
                                    </td>

                                    <td class="last-submission-cell">
                                        <div class="last-submission-info">
                                            @if($subcounty['last_submission'])
                                                <div class="submission-time">{{ \Carbon\Carbon::parse($subcounty['last_submission'])->format('M j, Y') }}</div>
                                                <div class="submission-relative">{{ \Carbon\Carbon::parse($subcounty['last_submission'])->diffForHumans() }}</div>
                                            @else
                                                <div class="no-submission">No submissions</div>
                                            @endif
                                        </div>
                                    </td>

                                    <td class="actions-cell">
                                        <div class="action-buttons">
                                            <button class="action-btn view-btn" onclick="viewAgentDetails('{{ $subcounty['type'] }}', '{{ $subcounty['name'] }}', '{{ $subcounty['id'] }}')" title="View Details">
                                                <i class="bi bi-eye"></i>
                                            </button>
                                            <button class="action-btn export-btn" onclick="exportLocationData('{{ $subcounty['id'] }}')" title="Export Data">
                                                <i class="bi bi-download"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            @empty
                                <tr class="empty-row">
                                    <td colspan="6" class="empty-cell">
                                        <div class="empty-state">
                                            <div class="empty-icon">📊</div>
                                            <div class="empty-title">No Agent Data Available</div>
                                            <div class="empty-subtitle">Try adjusting your filters or check back later</div>
                                        </div>
                                    </td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Include the same CSS from the polling station report -->
<style>
/* Use the same CSS framework as the polling station report */
@import url('{{ asset("css/reports/polling-station-submission.css") }}');

/* Agent-specific styling adjustments */
.agents-cell {
    text-align: center;
    padding: 1rem 0.75rem;
}

.agents-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.25rem;
}

.agents-count {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1rem;
    font-weight: 600;
}

.total-agents {
    color: #2d3748;
}

.agents-separator {
    color: #cbd5e0;
    font-weight: 400;
}

.active-agents {
    color: #48bb78;
}

.agents-label {
    font-size: 0.75rem;
    color: #718096;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.completion-bar {
    width: 60px;
    height: 4px;
    background: #e2e8f0;
    border-radius: 2px;
    overflow: hidden;
    margin-top: 0.25rem;
}

.completion-fill {
    height: 100%;
    background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
    border-radius: 2px;
    transition: width 0.3s ease;
}

/* Hero header adjustments for agent report */
.hero-title {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-highlight {
    background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Compact icon for agent report */
.compact-icon i {
    color: #48bb78;
}
</style>

<script>
// Tree table functionality (same as polling station report)
window.expandedRows = new Set();
window.loadedChildren = new Map();

// Export function for agent report
window.exportAgentReport = function() {
    console.log('Export agent report clicked');

    // Get current filter values
    const form = document.getElementById('reportForm');
    const formData = new FormData(form);

    // Build query string
    const params = new URLSearchParams();
    for (let [key, value] of formData.entries()) {
        if (value) {
            params.append(key, value);
        }
    }

    // Create download URL
    const exportUrl = '/reports/agent-submissions/export?' + params.toString();

    // Trigger download
    window.location.href = exportUrl;
};

// View agent details function
window.viewAgentDetails = function(type, name, id) {
    console.log('View agent details:', type, name, id);
    // Implementation for viewing agent details modal
    alert('Agent details for ' + name + ' (Feature coming soon)');
};

// Export location data function
window.exportLocationData = function(locationId) {
    console.log('Export location data:', locationId);
    // Implementation for exporting specific location data
    alert('Export location data (Feature coming soon)');
};

// Clear filters function
window.clearFilters = function() {
    window.location.href = '{{ route("reports.agent-submissions") }}';
};

// Toggle filters function
window.toggleFilters = function() {
    const content = document.getElementById('filtersContent');
    const button = document.querySelector('.toggle-btn i');

    if (content.style.display === 'none') {
        content.style.display = 'block';
        button.className = 'bi bi-chevron-up';
    } else {
        content.style.display = 'none';
        button.className = 'bi bi-chevron-down';
    }
};

// Refresh report function
window.refreshReport = function() {
    window.location.reload();
};

// Tree table expand/collapse functionality
window.toggleTreeRow = function(rowId) {
    console.log('Toggle tree row:', rowId);
    const row = document.querySelector(`tr[data-id="${rowId}"]`);
    if (!row) {
        console.error('Row not found:', rowId);
        return;
    }

    const isExpanded = row.dataset.expanded === 'true';
    const toggleBtn = row.querySelector('.expand-btn');

    if (isExpanded) {
        // Collapse row
        row.dataset.expanded = 'false';
        toggleBtn.classList.remove('expanded');
        expandedRows.delete(rowId);

        // Hide child rows
        hideChildRows(rowId);
    } else {
        // Expand row
        row.dataset.expanded = 'true';
        toggleBtn.classList.add('expanded');
        expandedRows.add(rowId);

        // Show/load child rows
        showChildRows(rowId);
    }
};

// Expand all rows function
window.expandAllRows = function() {
    console.log('Expand all rows');
    const allRows = document.querySelectorAll('tr[data-has-children="true"]');
    allRows.forEach(row => {
        const rowId = row.dataset.id;
        if (row.dataset.expanded !== 'true') {
            toggleTreeRow(rowId);
        }
    });
};

// Collapse all rows function
window.collapseAllRows = function() {
    console.log('Collapse all rows');
    const allRows = document.querySelectorAll('tr[data-has-children="true"]');
    allRows.forEach(row => {
        const rowId = row.dataset.id;
        if (row.dataset.expanded === 'true') {
            toggleTreeRow(rowId);
        }
    });
};

// Helper functions for child row management
function hideChildRows(parentId) {
    const childRows = document.querySelectorAll(`tr[data-parent-id="${parentId}"]`);
    childRows.forEach(row => {
        row.style.display = 'none';
        // Also hide grandchildren if they exist
        const childId = row.dataset.id;
        hideChildRows(childId);
    });
}

function showChildRows(parentId) {
    const childRows = document.querySelectorAll(`tr[data-parent-id="${parentId}"]`);
    childRows.forEach(row => {
        row.style.display = 'table-row';
    });
}

// Keyboard shortcuts
document.addEventListener('keydown', function(e) {
    if (e.ctrlKey && e.key === 'e') {
        e.preventDefault();
        expandAllRows();
    } else if (e.ctrlKey && e.key === 'c') {
        e.preventDefault();
        collapseAllRows();
    }
});

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    console.log('Agent submissions report loaded');

    // Initialize filters toggle state
    const filtersContent = document.getElementById('filtersContent');
    if (filtersContent) {
        filtersContent.style.display = 'block'; // Show filters by default
    }
});
</script>
@endsection
