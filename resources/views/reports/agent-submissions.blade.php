@extends('layouts.app')

@section('title', 'Agent Submission Statistics')

@section('content')
<div class="container-fluid">
    <div class="modern-dashboard">
        <!-- Ultra Compact Header -->
        <div class="ultra-compact-header">
            <div class="compact-header-content">
                <div class="header-left">
                    <div class="header-icon">
                        <i class="bi bi-people-fill"></i>
                    </div>
                    <div class="header-info">
                        <h1 class="header-title">Agent Analytics</h1>
                        <div class="header-stats">
                            <span class="stat-item">{{ number_format($summaryStats['total_agents']) }} Total</span>
                            <span class="stat-separator">•</span>
                            <span class="stat-item">{{ number_format($summaryStats['active_agents']) }} Active</span>
                            <span class="stat-separator">•</span>
                            <span class="stat-item">{{ $summaryStats['completion_rate'] }}% Rate</span>
                        </div>
                    </div>
                </div>
                <div class="header-actions">
                    <button class="compact-btn" onclick="refreshReport()" title="Refresh">
                        <i class="bi bi-arrow-clockwise"></i>
                    </button>
                    <button class="compact-btn" onclick="exportAgentReport()" title="Export">
                        <i class="bi bi-download"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Ultra Compact Filters -->
        <div class="ultra-compact-filters">
            <form method="GET" action="{{ route('reports.agent-submissions') }}" class="inline-form" id="reportForm">
                <div class="filter-row">
                    <input type="text" name="agent_search" class="compact-input agent-search"
                           value="{{ $filters['agent_search'] ?? '' }}"
                           placeholder="🔍 Search agents..."
                           onchange="this.form.submit()"
                           title="Search by agent name or phone">

                    <select name="subcounty" class="compact-select" onchange="this.form.submit()">
                        <option value="">All Subcounties</option>
                        @foreach($filterOptions['subcounties'] as $subcounty)
                            <option value="{{ $subcounty }}" {{ $filters['subcounty'] == $subcounty ? 'selected' : '' }}>{{ $subcounty }}</option>
                        @endforeach
                    </select>

                    <select name="parish" class="compact-select" onchange="this.form.submit()">
                        <option value="">All Parishes</option>
                        @foreach($filterOptions['parishes'] as $parish)
                            <option value="{{ $parish }}" {{ $filters['parish'] == $parish ? 'selected' : '' }}>{{ $parish }}</option>
                        @endforeach
                    </select>

                    <select name="village" class="compact-select" onchange="this.form.submit()">
                        <option value="">All Villages</option>
                        @foreach($filterOptions['villages'] as $village)
                            <option value="{{ $village }}" {{ $filters['village'] == $village ? 'selected' : '' }}>{{ $village }}</option>
                        @endforeach
                    </select>

                    <input type="date" name="date_from" class="compact-input" value="{{ $filters['date_from'] }}" onchange="this.form.submit()" placeholder="From" title="From Date">

                    <input type="date" name="date_to" class="compact-input" value="{{ $filters['date_to'] }}" onchange="this.form.submit()" placeholder="To" title="To Date">

                    <select name="per_page" class="compact-select" onchange="this.form.submit()" title="Items per page">
                        <option value="25" {{ $filters['per_page'] == 25 ? 'selected' : '' }}>25</option>
                        <option value="50" {{ $filters['per_page'] == 50 ? 'selected' : '' }}>50</option>
                        <option value="100" {{ $filters['per_page'] == 100 ? 'selected' : '' }}>100</option>
                    </select>

                    <button type="button" class="clear-filters-btn" onclick="clearFilters()" title="Clear All Filters">
                        <i class="bi bi-x-circle"></i>
                    </button>
                </div>
            </form>
        </div>



        <!-- Data Explorer -->
        <div class="data-explorer">
            <div class="explorer-header-compact">
                <div class="compact-title-inline">
                    <div class="compact-icon">
                        <i class="bi bi-people"></i>
                    </div>
                    <h3 class="compact-main-inline">Agent Data Explorer</h3>
                    <span class="compact-separator">•</span>
                    <span class="compact-count-inline">{{ count($reportData['data']) }} subcounties</span>
                </div>
                <div class="compact-controls">
                    <div class="simple-view-controls">
                        <button class="simple-btn expand-btn" onclick="expandAllRows()" title="Expand All (Ctrl+E)">
                            <i class="bi bi-arrows-expand"></i>
                            <span>Expand</span>
                        </button>
                        <button class="simple-btn collapse-btn" onclick="collapseAllRows()" title="Collapse All (Ctrl+C)">
                            <i class="bi bi-arrows-collapse"></i>
                            <span>Collapse</span>
                        </button>
                    </div>
                    <div class="explorer-summary">
                        <div class="summary-card">
                            <div class="summary-icon">
                                <i class="bi bi-graph-up"></i>
                            </div>
                            <div class="summary-content">
                                <div class="summary-value">{{ $summaryStats['completion_rate'] }}%</div>
                                <div class="summary-label">Completion</div>
                                <div class="summary-subtitle">Overall rate</div>
                            </div>
                            <div class="summary-progress">
                                <div class="progress-ring">
                                    <svg width="48" height="48" viewBox="0 0 48 48">
                                        <circle cx="24" cy="24" r="20" fill="none" stroke="#e2e8f0" stroke-width="4"/>
                                        <circle cx="24" cy="24" r="20" fill="none" stroke="#667eea" stroke-width="4" 
                                                stroke-dasharray="{{ 2 * pi() * 20 }}" 
                                                stroke-dashoffset="{{ 2 * pi() * 20 * (1 - $summaryStats['completion_rate'] / 100) }}"
                                                stroke-linecap="round" 
                                                transform="rotate(-90 24 24)"/>
                                    </svg>
                                    <div class="progress-text">{{ number_format($summaryStats['completion_rate'], 0) }}%</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Modern Tree Table -->
            <div class="explorer-content">
                <div class="table-container">
                    <table class="modern-tree-table" id="hierarchicalTable">
                        <thead class="table-head-modern">
                            <tr class="header-row">
                                <th class="col-location">
                                    <div class="column-header">
                                        <div class="header-icon">🌍</div>
                                        <div class="header-text">
                                            <span class="header-title">Location</span>
                                            <span class="header-subtitle">Administrative area</span>
                                        </div>
                                    </div>
                                </th>
                                <th class="col-agents">
                                    <div class="column-header">
                                        <div class="header-icon">👥</div>
                                        <div class="header-text">
                                            <span class="header-title">Agents</span>
                                            <span class="header-subtitle">Total / Active</span>
                                        </div>
                                    </div>
                                </th>
                                <th class="col-submissions">
                                    <div class="column-header">
                                        <div class="header-icon">📊</div>
                                        <div class="header-text">
                                            <span class="header-title">Submissions</span>
                                            <span class="header-subtitle">Total count</span>
                                        </div>
                                    </div>
                                </th>
                                <th class="col-completion">
                                    <div class="column-header">
                                        <div class="header-icon">📈</div>
                                        <div class="header-text">
                                            <span class="header-title">Completion</span>
                                            <span class="header-subtitle">Rate %</span>
                                        </div>
                                    </div>
                                </th>
                                <th class="col-last-submission">
                                    <div class="column-header">
                                        <div class="header-icon">⏰</div>
                                        <div class="header-text">
                                            <span class="header-title">Last Activity</span>
                                            <span class="header-subtitle">Recent submission</span>
                                        </div>
                                    </div>
                                </th>
                                <th class="col-actions">
                                    <div class="column-header">
                                        <div class="header-icon">⚡</div>
                                        <div class="header-text">
                                            <span class="header-title">Actions</span>
                                            <span class="header-subtitle">Quick tools</span>
                                        </div>
                                    </div>
                                </th>
                            </tr>
                        </thead>
                        <tbody class="table-body-modern">
                            @forelse($reportData['data'] as $subcounty)
                                <tr class="data-row modern-row level-1"
                                    data-id="{{ $subcounty['id'] }}"
                                    data-level="1"
                                    data-type="subcounty"
                                    data-name="{{ $subcounty['name'] }}"
                                    data-has-children="{{ $subcounty['has_children'] ? 'true' : 'false' }}"
                                    data-expanded="false">

                                    <td class="location-cell">
                                        <div class="location-content">
                                            @if($subcounty['has_children'])
                                                <div class="location-controls">
                                                    <button class="expand-btn" onclick="toggleTreeRow('{{ $subcounty['id'] }}')" title="Expand/Collapse">
                                                        <i class="bi bi-chevron-right"></i>
                                                    </button>
                                                </div>
                                            @else
                                                <div class="location-spacer"></div>
                                            @endif
                                            <div class="location-info">
                                                <div class="location-avatar">
                                                    <div class="avatar-circle subcounty">
                                                        {{ strtoupper(substr($subcounty['name'], 0, 2)) }}
                                                    </div>
                                                </div>
                                                <div class="location-details">
                                                    <div class="location-name">{{ $subcounty['name'] }}</div>
                                                    <div class="location-type">Subcounty</div>
                                                </div>
                                            </div>
                                        </div>
                                    </td>

                                    <td class="agents-cell">
                                        <div class="agents-info">
                                            <div class="agents-count">
                                                <span class="total-agents">{{ $subcounty['total_agents'] }}</span>
                                                <span class="agents-separator">/</span>
                                                <span class="active-agents">{{ $subcounty['active_agents'] }}</span>
                                            </div>
                                            <div class="agents-label">Total / Active</div>
                                        </div>
                                    </td>

                                    <td class="submissions-cell">
                                        <div class="submissions-info">
                                            <div class="submissions-count">{{ number_format($subcounty['total_submissions']) }}</div>
                                            <div class="submissions-label">Submissions</div>
                                        </div>
                                    </td>

                                    <td class="completion-cell">
                                        <div class="completion-info">
                                            <div class="completion-percentage">{{ $subcounty['completion_rate'] }}%</div>
                                            <div class="completion-bar">
                                                <div class="completion-fill" style="width: {{ $subcounty['completion_rate'] }}%"></div>
                                            </div>
                                        </div>
                                    </td>

                                    <td class="last-submission-cell">
                                        <div class="last-submission-info">
                                            @if($subcounty['last_submission'])
                                                <div class="submission-time">{{ \Carbon\Carbon::parse($subcounty['last_submission'])->format('M j, Y') }}</div>
                                                <div class="submission-relative">{{ \Carbon\Carbon::parse($subcounty['last_submission'])->diffForHumans() }}</div>
                                            @else
                                                <div class="no-submission">No submissions</div>
                                            @endif
                                        </div>
                                    </td>

                                    <td class="actions-cell">
                                        <div class="action-buttons">
                                            <button class="action-btn view-btn" onclick="viewAgentDetails('{{ $subcounty['type'] }}', '{{ $subcounty['name'] }}', '{{ $subcounty['id'] }}')" title="View Details">
                                                <i class="bi bi-eye"></i>
                                            </button>
                                            <button class="action-btn export-btn" onclick="exportLocationData('{{ $subcounty['id'] }}')" title="Export Data">
                                                <i class="bi bi-download"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>

                                {{-- Parish Level Rows (Hidden by default) --}}
                                @if(isset($subcounty['children']) && count($subcounty['children']) > 0)
                                    @foreach($subcounty['children'] as $parish)
                                        <tr class="data-row modern-row level-2"
                                            data-id="{{ $parish['id'] }}"
                                            data-level="2"
                                            data-type="parish"
                                            data-name="{{ $parish['name'] }}"
                                            data-parent-id="{{ $subcounty['id'] }}"
                                            data-has-children="{{ $parish['has_children'] ? 'true' : 'false' }}"
                                            data-expanded="false"
                                            style="display: none;">

                                            <td class="location-cell">
                                                <div class="location-content">
                                                    @if($parish['has_children'])
                                                        <div class="location-controls">
                                                            <button class="expand-btn" onclick="toggleTreeRow('{{ $parish['id'] }}')" title="Expand/Collapse">
                                                                <i class="bi bi-chevron-right"></i>
                                                            </button>
                                                        </div>
                                                    @else
                                                        <div class="location-spacer"></div>
                                                    @endif
                                                    <div class="location-info" style="margin-left: 2rem;">
                                                        <div class="location-avatar">
                                                            <div class="avatar-circle parish">
                                                                {{ strtoupper(substr($parish['name'], 0, 2)) }}
                                                            </div>
                                                        </div>
                                                        <div class="location-details">
                                                            <div class="location-name">{{ $parish['name'] }}</div>
                                                            <div class="location-type">Parish</div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </td>

                                            <td class="agents-cell">
                                                <div class="agents-info">
                                                    <div class="agents-count">
                                                        <span class="total-agents">{{ $parish['total_agents'] }}</span>
                                                        <span class="agents-separator">/</span>
                                                        <span class="active-agents">{{ $parish['active_agents'] }}</span>
                                                    </div>
                                                    <div class="agents-label">Total / Active</div>
                                                </div>
                                            </td>

                                            <td class="submissions-cell">
                                                <div class="submissions-info">
                                                    <div class="submissions-count">{{ number_format($parish['total_submissions']) }}</div>
                                                    <div class="submissions-label">Submissions</div>
                                                </div>
                                            </td>

                                            <td class="completion-cell">
                                                <div class="completion-info">
                                                    <div class="completion-percentage">{{ $parish['completion_rate'] }}%</div>
                                                    <div class="completion-bar">
                                                        <div class="completion-fill" style="width: {{ $parish['completion_rate'] }}%"></div>
                                                    </div>
                                                </div>
                                            </td>

                                            <td class="last-submission-cell">
                                                <div class="last-submission-info">
                                                    @if($parish['last_submission'])
                                                        <div class="submission-time">{{ \Carbon\Carbon::parse($parish['last_submission'])->format('M j, Y') }}</div>
                                                        <div class="submission-relative">{{ \Carbon\Carbon::parse($parish['last_submission'])->diffForHumans() }}</div>
                                                    @else
                                                        <div class="no-submission">No submissions</div>
                                                    @endif
                                                </div>
                                            </td>

                                            <td class="actions-cell">
                                                <div class="action-buttons">
                                                    <button class="action-btn view-btn" onclick="viewAgentDetails('{{ $parish['type'] }}', '{{ $parish['name'] }}', '{{ $parish['id'] }}')" title="View Details">
                                                        <i class="bi bi-eye"></i>
                                                    </button>
                                                    <button class="action-btn export-btn" onclick="exportLocationData('{{ $parish['id'] }}')" title="Export Data">
                                                        <i class="bi bi-download"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>

                                        {{-- Village Level Rows (Hidden by default) --}}
                                        @if(isset($parish['children']) && count($parish['children']) > 0)
                                            @foreach($parish['children'] as $village)
                                                <tr class="data-row modern-row level-3"
                                                    data-id="{{ $village['id'] }}"
                                                    data-level="3"
                                                    data-type="village"
                                                    data-name="{{ $village['name'] }}"
                                                    data-parent-id="{{ $parish['id'] }}"
                                                    data-has-children="false"
                                                    data-expanded="false"
                                                    style="display: none;">

                                                    <td class="location-cell">
                                                        <div class="location-content">
                                                            <div class="location-spacer"></div>
                                                            <div class="location-info" style="margin-left: 4rem;">
                                                                <div class="location-avatar">
                                                                    <div class="avatar-circle village">
                                                                        {{ strtoupper(substr($village['name'], 0, 2)) }}
                                                                    </div>
                                                                </div>
                                                                <div class="location-details">
                                                                    <div class="location-name">{{ $village['name'] }}</div>
                                                                    <div class="location-type">Village</div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </td>

                                                    <td class="agents-cell">
                                                        <div class="agents-info">
                                                            <div class="agents-count">
                                                                <span class="total-agents">{{ $village['total_agents'] }}</span>
                                                                <span class="agents-separator">/</span>
                                                                <span class="active-agents">{{ $village['active_agents'] }}</span>
                                                            </div>
                                                            <div class="agents-label">Total / Active</div>
                                                        </div>
                                                    </td>

                                                    <td class="submissions-cell">
                                                        <div class="submissions-info">
                                                            <div class="submissions-count">{{ number_format($village['total_submissions']) }}</div>
                                                            <div class="submissions-label">Submissions</div>
                                                        </div>
                                                    </td>

                                                    <td class="completion-cell">
                                                        <div class="completion-info">
                                                            <div class="completion-percentage">{{ $village['completion_rate'] }}%</div>
                                                            <div class="completion-bar">
                                                                <div class="completion-fill" style="width: {{ $village['completion_rate'] }}%"></div>
                                                            </div>
                                                        </div>
                                                    </td>

                                                    <td class="last-submission-cell">
                                                        <div class="last-submission-info">
                                                            @if($village['last_submission'])
                                                                <div class="submission-time">{{ \Carbon\Carbon::parse($village['last_submission'])->format('M j, Y') }}</div>
                                                                <div class="submission-relative">{{ \Carbon\Carbon::parse($village['last_submission'])->diffForHumans() }}</div>
                                                            @else
                                                                <div class="no-submission">No submissions</div>
                                                            @endif
                                                        </div>
                                                    </td>

                                                    <td class="actions-cell">
                                                        <div class="action-buttons">
                                                            <button class="action-btn view-btn" onclick="viewAgentDetails('{{ $village['type'] }}', '{{ $village['name'] }}', '{{ $village['id'] }}')" title="View Details">
                                                                <i class="bi bi-eye"></i>
                                                            </button>
                                                            <button class="action-btn export-btn" onclick="exportLocationData('{{ $village['id'] }}')" title="Export Data">
                                                                <i class="bi bi-download"></i>
                                                            </button>
                                                        </div>
                                                    </td>
                                                </tr>
                                            @endforeach
                                        @endif
                                    @endforeach
                                @endif
                            @empty
                                <tr class="empty-row">
                                    <td colspan="6" class="empty-cell">
                                        <div class="empty-state">
                                            <div class="empty-icon">📊</div>
                                            <div class="empty-title">No Agent Data Available</div>
                                            <div class="empty-subtitle">Try adjusting your filters or check back later</div>
                                        </div>
                                    </td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>

                <!-- Enhanced Tree Pagination -->
                <div class="tree-pagination">
                    <div class="pagination-info">
                        <div class="info-content">
                            <div class="info-icon">
                                <i class="bi bi-layers"></i>
                            </div>
                            <div class="info-details">
                                <div class="info-primary">
                                    Showing {{ $reportData['pagination']->firstItem() ?? 0 }} to {{ $reportData['pagination']->lastItem() ?? 0 }}
                                    of {{ $reportData['pagination']->total() }} subcounties
                                </div>
                                <div class="info-secondary">
                                    Page {{ $reportData['pagination']->currentPage() }} of {{ $reportData['pagination']->lastPage() }}
                                    • {{ $reportData['pagination']->perPage() }} per page
                                </div>
                            </div>
                        </div>
                        <div class="tree-stats">
                            <div class="stat-item">
                                <div class="stat-icon">🏛️</div>
                                <div class="stat-text">
                                    <span class="stat-number" id="visibleSubcounties">{{ count($reportData['data']) }}</span>
                                    <span class="stat-label">Subcounties</span>
                                </div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-icon">🏘️</div>
                                <div class="stat-text">
                                    <span class="stat-number" id="visibleParishes">0</span>
                                    <span class="stat-label">Parishes</span>
                                </div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-icon">🏠</div>
                                <div class="stat-text">
                                    <span class="stat-number" id="visibleVillages">0</span>
                                    <span class="stat-label">Villages</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="pagination-controls">
                        <div class="page-size-control">
                            <label for="pageSizeSelect" class="page-size-label">Show:</label>
                            <select id="pageSizeSelect" class="page-size-select" onchange="changePageSize(this.value)">
                                <option value="10" {{ $reportData['pagination']->perPage() == 10 ? 'selected' : '' }}>10</option>
                                <option value="25" {{ $reportData['pagination']->perPage() == 25 ? 'selected' : '' }}>25</option>
                                <option value="50" {{ $reportData['pagination']->perPage() == 50 ? 'selected' : '' }}>50</option>
                                <option value="100" {{ $reportData['pagination']->perPage() == 100 ? 'selected' : '' }}>100</option>
                            </select>
                            <span class="page-size-suffix">subcounties</span>
                        </div>

                        <div class="pagination-nav">
                            {{ $reportData['pagination']->appends(request()->query())->links('pagination::bootstrap-4') }}
                        </div>

                        <div class="pagination-actions">
                            <button class="pagination-btn" onclick="goToFirstPage()"
                                    {{ $reportData['pagination']->currentPage() == 1 ? 'disabled' : '' }}
                                    title="First Page">
                                <i class="bi bi-chevron-double-left"></i>
                            </button>
                            <button class="pagination-btn" onclick="goToLastPage()"
                                    {{ $reportData['pagination']->currentPage() == $reportData['pagination']->lastPage() ? 'disabled' : '' }}
                                    title="Last Page">
                                <i class="bi bi-chevron-double-right"></i>
                            </button>
                            <button class="pagination-btn refresh-btn" onclick="refreshCurrentPage()" title="Refresh Page">
                                <i class="bi bi-arrow-clockwise"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section('styles')
<style>
/* Modern Dashboard Base */
.modern-dashboard {
    min-height: 100vh;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    padding: 0;
    margin: 0;
}

/* Ultra Compact Header */
.ultra-compact-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 0.75rem 0;
    margin-bottom: 0.5rem;
}

.compact-header-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 1rem;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.header-icon {
    width: 36px;
    height: 36px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.125rem;
    backdrop-filter: blur(10px);
}

.header-info {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.header-title {
    font-size: 1.25rem;
    font-weight: 700;
    margin: 0;
    line-height: 1;
}

.header-stats {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.8rem;
    opacity: 0.9;
}

.stat-item {
    font-weight: 500;
}

.stat-separator {
    opacity: 0.6;
}

.header-actions {
    display: flex;
    gap: 0.5rem;
}

.compact-btn {
    width: 36px;
    height: 36px;
    background: rgba(255, 255, 255, 0.2);
    border: none;
    border-radius: 8px;
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    transition: all 0.2s ease;
    backdrop-filter: blur(10px);
}

.compact-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.05);
}

/* Ultra Compact Filters */
.ultra-compact-filters {
    background: white;
    margin: 0 1rem 0.5rem 1rem;
    padding: 0.75rem 1rem;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.inline-form {
    width: 100%;
}

.filter-row {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    flex-wrap: wrap;
}

.compact-select,
.compact-input {
    padding: 0.5rem 0.75rem;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    font-size: 0.875rem;
    background: white;
    transition: all 0.2s ease;
    outline: none;
    min-width: 120px;
}

.compact-select:focus,
.compact-input:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
}

.compact-input {
    min-width: 140px;
}

.agent-search {
    min-width: 180px;
    background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
    border: 2px solid #e2e8f0;
    font-weight: 500;
    position: relative;
}

.agent-search:focus {
    border-color: #48bb78;
    box-shadow: 0 0 0 3px rgba(72, 187, 120, 0.1);
    background: white;
}

.agent-search::placeholder {
    color: #9ca3af;
    font-weight: 400;
}

.clear-filters-btn {
    width: 32px;
    height: 32px;
    background: #fee2e2;
    border: 1px solid #fca5a5;
    border-radius: 8px;
    color: #dc2626;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.875rem;
    transition: all 0.2s ease;
}

.clear-filters-btn:hover {
    background: #fecaca;
    transform: scale(1.05);
}



/* Data Explorer */
.data-explorer {
    background: white;
    border-radius: 12px;
    margin: 0 1rem 1rem 1rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(0, 0, 0, 0.05);
    overflow: hidden;
}

.explorer-container {
    display: flex;
    flex-direction: column;
}

/* Compact Explorer Header */
.explorer-header-compact {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.5rem;
    background: linear-gradient(135deg, #f8fafc 0%, #edf2f7 100%);
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    flex-wrap: wrap;
    gap: 1rem;
}

/* Inline Compact Title */
.compact-title-inline {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.compact-icon {
    width: 32px;
    height: 32px;
    background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1rem;
    flex-shrink: 0;
}

.compact-main-inline {
    font-size: 1.125rem;
    font-weight: 700;
    color: #2d3748;
    margin: 0;
    line-height: 1;
    white-space: nowrap;
}

.compact-separator {
    color: #cbd5e0;
    font-weight: 400;
    font-size: 1rem;
    margin: 0 0.25rem;
}

.compact-count-inline {
    font-size: 0.875rem;
    color: #64748b;
    font-weight: 500;
    background: rgba(100, 116, 139, 0.1);
    padding: 0.25rem 0.75rem;
    border-radius: 16px;
    white-space: nowrap;
}

.compact-controls {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    flex-wrap: wrap;
}

/* Simplified View Controls */
.simple-view-controls {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.simple-view-controls .simple-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 0.875rem;
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    font-size: 0.875rem;
    font-weight: 500;
    color: #4a5568;
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap;
    text-decoration: none;
}

.simple-view-controls .simple-btn:hover {
    background: #f7fafc;
    border-color: #cbd5e0;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.simple-view-controls .simple-btn:active {
    transform: translateY(0);
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

.simple-view-controls .simple-btn i {
    font-size: 0.875rem;
    color: #667eea;
}

.simple-view-controls .simple-btn.expand-btn:hover {
    border-color: #10b981;
    color: #059669;
}

.simple-view-controls .simple-btn.expand-btn:hover i {
    color: #10b981;
}

.simple-view-controls .simple-btn.collapse-btn:hover {
    border-color: #f59e0b;
    color: #d97706;
}

.simple-view-controls .simple-btn.collapse-btn:hover i {
    color: #f59e0b;
}

/* Explorer Summary */
.explorer-summary {
    display: flex;
    gap: 1rem;
}

.summary-card {
    display: flex;
    align-items: center;
    gap: 1rem;
    background: white;
    padding: 1rem;
    border-radius: 12px;
    border: 1px solid #e2e8f0;
    min-width: 200px;
}

.summary-icon {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.25rem;
    flex-shrink: 0;
}

.summary-content {
    flex: 1;
}

.summary-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1a202c;
    line-height: 1;
    margin-bottom: 0.25rem;
}

.summary-label {
    font-size: 0.875rem;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 0.125rem;
}

.summary-subtitle {
    font-size: 0.75rem;
    color: #718096;
}

.summary-progress {
    position: relative;
}

.progress-ring {
    position: relative;
    width: 48px;
    height: 48px;
}

.progress-ring svg {
    transform: rotate(-90deg);
}

.progress-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 0.75rem;
    font-weight: 600;
    color: #667eea;
}

/* Modern Tree Table */
.explorer-content {
    padding: 0;
}

.table-container {
    overflow-x: auto;
    max-height: 70vh;
    border-radius: 0 0 20px 20px;
}

.modern-tree-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.875rem;
    background: white;
}

.table-head-modern {
    background: linear-gradient(135deg, #f8fafc 0%, #edf2f7 100%);
    position: sticky;
    top: 0;
    z-index: 10;
}

.header-row {
    border-bottom: 2px solid #e2e8f0;
}

.header-row th {
    padding: 1rem 0.75rem;
    text-align: left;
    font-weight: 600;
    color: #2d3748;
    border-bottom: 2px solid #e2e8f0;
    position: relative;
}

.column-header {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.header-icon {
    font-size: 1.125rem;
    opacity: 0.7;
}

.header-text {
    display: flex;
    flex-direction: column;
    gap: 0.125rem;
}

.header-title {
    font-size: 0.875rem;
    font-weight: 600;
    color: #2d3748;
    line-height: 1;
}

.header-subtitle {
    font-size: 0.75rem;
    color: #718096;
    font-weight: 400;
    line-height: 1;
}

/* Table Body */
.table-body-modern {
    background: white;
}

.data-row {
    border-bottom: 1px solid #f1f5f9;
    transition: all 0.2s ease;
}

.data-row:hover {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

.data-row.level-1 {
    background: white;
    border-left: 4px solid #667eea;
}

.data-row.level-2 {
    background: #fafbfc;
    border-left: 4px solid #48bb78;
}

.data-row.level-3 {
    background: #f8fafc;
    border-left: 4px solid #f59e0b;
}

/* Level indentation */
.level-2 .location-info {
    margin-left: 2rem;
}

.level-3 .location-info {
    margin-left: 4rem;
}

/* Expand button states */
.expand-btn.expanded i {
    transform: rotate(90deg);
}

.expand-btn i {
    transition: transform 0.2s ease;
}

/* Location Cell */
.location-cell {
    padding: 1rem 0.75rem;
    min-width: 300px;
}

.location-content {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.location-controls {
    display: flex;
    align-items: center;
}

.location-controls .expand-btn {
    width: 28px;
    height: 28px;
    border: none;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.875rem;
}

.location-controls .expand-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.location-controls .expand-btn.expanded {
    transform: rotate(90deg);
    background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
}

.location-controls .expand-btn.expanded:hover {
    background: linear-gradient(135deg, #38a169 0%, #2f855a 100%);
}

.location-spacer {
    width: 28px;
    height: 28px;
}

.location-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    flex: 1;
}

.location-avatar {
    flex-shrink: 0;
}

.avatar-circle {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    font-weight: 600;
    color: white;
    text-transform: uppercase;
}

.avatar-circle.subcounty {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.avatar-circle.parish {
    background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
}

.avatar-circle.village {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.location-details {
    flex: 1;
}

.location-name {
    font-size: 0.875rem;
    font-weight: 600;
    color: #1a202c;
    line-height: 1.2;
    margin-bottom: 0.125rem;
}

.location-type {
    font-size: 0.75rem;
    color: #718096;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-weight: 500;
}

/* Agent-specific styling adjustments */
.agents-cell {
    text-align: center;
    padding: 1rem 0.75rem;
}

.agents-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.25rem;
}

.agents-count {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1rem;
    font-weight: 600;
}

.total-agents {
    color: #2d3748;
}

.agents-separator {
    color: #cbd5e0;
    font-weight: 400;
}

.active-agents {
    color: #48bb78;
}

.agents-label {
    font-size: 0.75rem;
    color: #718096;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Other cells */
.submissions-cell,
.completion-cell,
.last-submission-cell,
.actions-cell {
    padding: 1rem 0.75rem;
    text-align: center;
}

.submissions-info,
.completion-info,
.last-submission-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.25rem;
}

.submissions-count,
.completion-percentage {
    font-size: 1rem;
    font-weight: 600;
    color: #1a202c;
    line-height: 1;
}

.submissions-label,
.completion-label {
    font-size: 0.75rem;
    color: #718096;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.completion-bar {
    width: 60px;
    height: 4px;
    background: #e2e8f0;
    border-radius: 2px;
    overflow: hidden;
    margin-top: 0.25rem;
}

.completion-fill {
    height: 100%;
    background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
    border-radius: 2px;
    transition: width 0.3s ease;
}

.submission-time {
    font-size: 0.875rem;
    font-weight: 600;
    color: #1a202c;
    line-height: 1;
}

.submission-relative {
    font-size: 0.75rem;
    color: #718096;
}

.no-submission {
    font-size: 0.875rem;
    color: #9ca3af;
    font-style: italic;
}

/* Action Buttons */
.action-buttons {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
}

.action-btn {
    width: 32px;
    height: 32px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.875rem;
}

.view-btn {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    color: white;
}

.view-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.export-btn {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
}

.export-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

/* Empty State */
.empty-row {
    background: white;
}

.empty-cell {
    padding: 4rem 2rem;
    text-align: center;
}

.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
    max-width: 400px;
    margin: 0 auto;
}

.empty-icon {
    font-size: 4rem;
    opacity: 0.5;
}

.empty-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #374151;
}

.empty-subtitle {
    font-size: 0.875rem;
    color: #6b7280;
    text-align: center;
    line-height: 1.5;
}

/* Enhanced Tree Pagination */
.tree-pagination {
    background: linear-gradient(135deg, #f8fafc 0%, #edf2f7 100%);
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    padding: 1rem 1.5rem;
}

.pagination-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.info-content {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.info-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.125rem;
}

.info-details {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.info-primary {
    font-size: 0.875rem;
    font-weight: 600;
    color: #2d3748;
    line-height: 1;
}

.info-secondary {
    font-size: 0.75rem;
    color: #718096;
    line-height: 1;
}

.tree-stats {
    display: flex;
    gap: 1.5rem;
    align-items: center;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 0.75rem;
    background: white;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
    min-width: 80px;
}

.stat-icon {
    font-size: 1rem;
    line-height: 1;
}

.stat-text {
    display: flex;
    flex-direction: column;
    gap: 0.125rem;
}

.stat-number {
    font-size: 0.875rem;
    font-weight: 700;
    color: #1a202c;
    line-height: 1;
}

.stat-label {
    font-size: 0.7rem;
    color: #718096;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    line-height: 1;
}

.pagination-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.page-size-control {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
}

.page-size-label {
    color: #4a5568;
    font-weight: 500;
}

.page-size-select {
    padding: 0.375rem 0.75rem;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    font-size: 0.875rem;
    background: white;
    color: #2d3748;
    cursor: pointer;
    transition: all 0.2s ease;
}

.page-size-select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
}

.page-size-suffix {
    color: #718096;
    font-weight: 400;
}

.pagination-nav {
    display: flex;
    align-items: center;
}

.pagination-actions {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.pagination-btn {
    width: 36px;
    height: 36px;
    border: 1px solid #e2e8f0;
    background: white;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    color: #4a5568;
    font-size: 0.875rem;
}

.pagination-btn:hover:not(:disabled) {
    background: #f7fafc;
    border-color: #cbd5e0;
    color: #2d3748;
    transform: translateY(-1px);
}

.pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.pagination-btn.refresh-btn {
    background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
    border-color: #48bb78;
    color: white;
}

.pagination-btn.refresh-btn:hover:not(:disabled) {
    background: linear-gradient(135deg, #38a169 0%, #2f855a 100%);
    border-color: #38a169;
    transform: translateY(-1px);
}

/* Focus States */
.location-controls .expand-btn:focus,
.simple-view-controls .simple-btn:focus,
.control-btn:focus,
.action-btn:focus,
.modern-select:focus,
.modern-input:focus {
    outline: 2px solid #667eea;
    outline-offset: 2px;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .compact-header-content {
        flex-direction: column;
        text-align: center;
        gap: 0.75rem;
    }

    .header-left {
        justify-content: center;
    }

    .filter-row {
        justify-content: center;
    }

    .compact-title-inline {
        justify-content: center;
        flex-wrap: wrap;
        gap: 0.5rem;
    }

    .compact-controls {
        justify-content: center;
        gap: 1rem;
    }

    .simple-view-controls {
        gap: 0.375rem;
    }

    .simple-view-controls .simple-btn {
        padding: 0.375rem 0.75rem;
        font-size: 0.8rem;
    }

    .simple-view-controls .simple-btn i {
        font-size: 0.8rem;
    }
}

@media (max-width: 768px) {
    .modern-dashboard {
        padding: 0;
    }

    .ultra-compact-header {
        padding: 0.5rem 0;
        margin-bottom: 0.25rem;
    }

    .compact-header-content {
        padding: 0 0.75rem;
        gap: 0.5rem;
    }

    .header-icon {
        width: 32px;
        height: 32px;
        font-size: 1rem;
    }

    .header-title {
        font-size: 1.125rem;
    }

    .header-stats {
        font-size: 0.75rem;
        gap: 0.375rem;
    }

    .compact-btn {
        width: 32px;
        height: 32px;
        font-size: 0.875rem;
    }

    .ultra-compact-filters,
    .data-explorer {
        margin: 0 0.75rem 0.5rem 0.75rem;
    }

    .filter-row {
        gap: 0.5rem;
    }

    .compact-select,
    .compact-input {
        padding: 0.375rem 0.5rem;
        font-size: 0.8rem;
        min-width: 100px;
    }

    .compact-input {
        min-width: 110px;
    }

    .agent-search {
        min-width: 140px;
        font-size: 0.8rem;
    }

    .clear-filters-btn {
        width: 28px;
        height: 28px;
        font-size: 0.8rem;
    }

    .tree-stats {
        gap: 0.75rem;
    }

    .stat-item {
        min-width: 60px;
        padding: 0.25rem 0.375rem;
    }

    .stat-icon {
        font-size: 0.875rem;
    }

    .stat-number {
        font-size: 0.75rem;
    }

    .stat-label {
        font-size: 0.6rem;
    }

    .page-size-control {
        font-size: 0.8rem;
    }

    .page-size-select {
        padding: 0.25rem 0.5rem;
        font-size: 0.8rem;
    }

    .pagination-btn {
        width: 32px;
        height: 32px;
        font-size: 0.8rem;
    }

    .info-icon {
        width: 36px;
        height: 36px;
        font-size: 1rem;
    }

    .info-primary {
        font-size: 0.8rem;
    }

    .info-secondary {
        font-size: 0.7rem;
    }

    .explorer-header-compact {
        flex-direction: column;
        align-items: stretch;
        gap: 0.75rem;
    }

    .compact-title-inline {
        gap: 0.5rem;
    }

    .compact-icon {
        width: 28px;
        height: 28px;
        font-size: 0.875rem;
    }

    .compact-main-inline {
        font-size: 1rem;
    }

    .compact-count-inline {
        font-size: 0.75rem;
        padding: 0.2rem 0.6rem;
    }

    .compact-separator {
        display: none;
    }

    .simple-view-controls {
        gap: 0.25rem;
    }

    .simple-view-controls .simple-btn {
        padding: 0.375rem 0.625rem;
        font-size: 0.75rem;
    }

    .simple-view-controls .simple-btn span {
        display: none;
    }

    .simple-view-controls .simple-btn i {
        font-size: 0.875rem;
    }

    .table-container {
        max-height: 60vh;
    }

    .modern-tree-table {
        font-size: 0.8rem;
    }

    .header-row th {
        padding: 0.75rem 0.5rem;
    }

    .location-cell,
    .agents-cell,
    .submissions-cell,
    .completion-cell,
    .last-submission-cell,
    .actions-cell {
        padding: 0.75rem 0.5rem;
    }

    .location-content {
        gap: 0.5rem;
    }

    .location-info {
        gap: 0.5rem;
    }

    .avatar-circle {
        width: 32px;
        height: 32px;
        font-size: 0.7rem;
    }

    .location-name {
        font-size: 0.8rem;
    }

    .location-type {
        font-size: 0.7rem;
    }

    .agents-count,
    .submissions-count,
    .completion-percentage {
        font-size: 0.875rem;
    }

    .agents-label,
    .submissions-label,
    .completion-label {
        font-size: 0.7rem;
    }

    .completion-bar {
        width: 50px;
        height: 3px;
    }

    .submission-time {
        font-size: 0.8rem;
    }

    .submission-relative {
        font-size: 0.7rem;
    }

    .action-buttons {
        gap: 0.25rem;
    }

    .action-btn {
        width: 28px;
        height: 28px;
        font-size: 0.8rem;
    }

    .tree-pagination {
        padding: 0.75rem 1rem;
    }

    .pagination-info {
        flex-direction: column;
        gap: 0.75rem;
        margin-bottom: 0.75rem;
    }

    .info-content {
        justify-content: center;
    }

    .tree-stats {
        justify-content: center;
        gap: 1rem;
    }

    .stat-item {
        min-width: 70px;
        padding: 0.375rem 0.5rem;
    }

    .stat-number {
        font-size: 0.8rem;
    }

    .stat-label {
        font-size: 0.65rem;
    }

    .pagination-controls {
        flex-direction: column;
        gap: 0.75rem;
    }

    .page-size-control {
        justify-content: center;
    }

    .pagination-nav {
        justify-content: center;
    }

    .pagination-actions {
        justify-content: center;
    }
}

/* Print Styles */
@media print {
    .hero-header,
    .smart-filters,
    .analytics-dashboard {
        display: none;
    }

    .data-explorer {
        box-shadow: none;
        border: 1px solid #ccc;
    }

    .modern-tree-table {
        font-size: 0.75rem;
    }

    .data-row:hover {
        background: white;
    }
}
</style>
@endsection

<script>
// Tree table functionality (same as polling station report)
window.expandedRows = new Set();
window.loadedChildren = new Map();

// Export function for agent report
window.exportAgentReport = function() {
    console.log('Export agent report clicked');

    // Get current filter values
    const form = document.getElementById('reportForm');
    const formData = new FormData(form);

    // Build query string
    const params = new URLSearchParams();
    for (let [key, value] of formData.entries()) {
        if (value) {
            params.append(key, value);
        }
    }

    // Create download URL
    const exportUrl = '/reports/agent-submissions/export?' + params.toString();

    // Trigger download
    window.location.href = exportUrl;
};

// View agent details function
window.viewAgentDetails = function(type, name, id) {
    console.log('View agent details:', type, name, id);
    // Implementation for viewing agent details modal
    alert('Agent details for ' + name + ' (Feature coming soon)');
};

// Export location data function
window.exportLocationData = function(locationId) {
    console.log('Export location data:', locationId);
    // Implementation for exporting specific location data
    alert('Export location data (Feature coming soon)');
};

// Clear filters function
window.clearFilters = function() {
    window.location.href = '{{ route("reports.agent-submissions") }}';
};

// Agent search functionality
window.setupAgentSearch = function() {
    const agentSearchInput = document.querySelector('.agent-search');
    if (!agentSearchInput) return;

    let searchTimeout;

    // Add real-time search with debouncing
    agentSearchInput.addEventListener('input', function(e) {
        clearTimeout(searchTimeout);

        // Show loading state
        this.style.background = 'linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%)';

        searchTimeout = setTimeout(() => {
            // Reset background
            this.style.background = '';

            // Submit form if there's a value or if cleared
            if (this.value.length >= 2 || this.value.length === 0) {
                document.getElementById('reportForm').submit();
            }
        }, 800); // 800ms delay for real-time search
    });

    // Handle Enter key
    agentSearchInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            clearTimeout(searchTimeout);
            document.getElementById('reportForm').submit();
        }
    });

    // Add search icon animation
    agentSearchInput.addEventListener('focus', function() {
        this.style.borderColor = '#48bb78';
        this.style.boxShadow = '0 0 0 3px rgba(72, 187, 120, 0.1)';
    });

    agentSearchInput.addEventListener('blur', function() {
        this.style.borderColor = '#e2e8f0';
        this.style.boxShadow = 'none';
    });
};

// Toggle filters function
window.toggleFilters = function() {
    const content = document.getElementById('filtersContent');
    const button = document.querySelector('.toggle-btn i');

    if (content.style.display === 'none') {
        content.style.display = 'block';
        button.className = 'bi bi-chevron-up';
    } else {
        content.style.display = 'none';
        button.className = 'bi bi-chevron-down';
    }
};

// Refresh report function
window.refreshReport = function() {
    window.location.reload();
};

// Tree table expand/collapse functionality
window.toggleTreeRow = function(rowId) {
    console.log('Toggle tree row:', rowId);
    const row = document.querySelector(`tr[data-id="${rowId}"]`);
    if (!row) {
        console.error('Row not found:', rowId);
        return;
    }

    const isExpanded = row.dataset.expanded === 'true';
    const toggleBtn = row.querySelector('.expand-btn');

    if (!toggleBtn) {
        console.error('Toggle button not found for row:', rowId);
        return;
    }

    if (isExpanded) {
        // Collapse row
        row.dataset.expanded = 'false';
        toggleBtn.classList.remove('expanded');
        expandedRows.delete(rowId);

        // Hide child rows
        hideChildRows(rowId);

        console.log('Collapsed row:', rowId);
    } else {
        // Expand row
        row.dataset.expanded = 'true';
        toggleBtn.classList.add('expanded');
        expandedRows.add(rowId);

        // Show child rows
        showChildRows(rowId);

        console.log('Expanded row:', rowId);
    }
};

// Expand all rows function
window.expandAllRows = function() {
    console.log('Expand all rows');
    const allRows = document.querySelectorAll('tr[data-has-children="true"]');
    console.log('Found rows with children:', allRows.length);

    allRows.forEach(row => {
        const rowId = row.dataset.id;
        const isExpanded = row.dataset.expanded === 'true';
        console.log('Row:', rowId, 'Expanded:', isExpanded);

        if (!isExpanded) {
            toggleTreeRow(rowId);
        }
    });
};

// Collapse all rows function
window.collapseAllRows = function() {
    console.log('Collapse all rows');
    const allRows = document.querySelectorAll('tr[data-has-children="true"]');
    console.log('Found rows with children:', allRows.length);

    // Collapse in reverse order (deepest first) to avoid issues
    const rowsArray = Array.from(allRows);
    rowsArray.reverse().forEach(row => {
        const rowId = row.dataset.id;
        const isExpanded = row.dataset.expanded === 'true';
        console.log('Row:', rowId, 'Expanded:', isExpanded);

        if (isExpanded) {
            toggleTreeRow(rowId);
        }
    });
};

// Helper functions for child row management
function hideChildRows(parentId) {
    const childRows = document.querySelectorAll(`tr[data-parent-id="${parentId}"]`);
    childRows.forEach(row => {
        row.style.display = 'none';
        // Reset expanded state
        row.dataset.expanded = 'false';
        const expandBtn = row.querySelector('.expand-btn');
        if (expandBtn) {
            expandBtn.classList.remove('expanded');
        }
        // Also hide grandchildren if they exist
        const childId = row.dataset.id;
        hideChildRows(childId);
    });
}

function showChildRows(parentId) {
    const childRows = document.querySelectorAll(`tr[data-parent-id="${parentId}"]`);
    childRows.forEach(row => {
        row.style.display = 'table-row';
        // Only show immediate children, not grandchildren
        // Grandchildren will be shown when their parent is expanded
    });
}

// Keyboard shortcuts
document.addEventListener('keydown', function(e) {
    if (e.ctrlKey && e.key === 'e') {
        e.preventDefault();
        expandAllRows();
    } else if (e.ctrlKey && e.key === 'c') {
        e.preventDefault();
        collapseAllRows();
    }
});

// Pagination functions
window.changePageSize = function(newSize) {
    const url = new URL(window.location);
    url.searchParams.set('per_page', newSize);
    url.searchParams.set('page', 1); // Reset to first page
    window.location.href = url.toString();
};

window.goToFirstPage = function() {
    const url = new URL(window.location);
    url.searchParams.set('page', 1);
    window.location.href = url.toString();
};

window.goToLastPage = function() {
    const lastPage = {{ $reportData['pagination']->lastPage() }};
    const url = new URL(window.location);
    url.searchParams.set('page', lastPage);
    window.location.href = url.toString();
};

window.refreshCurrentPage = function() {
    window.location.reload();
};

// Update visible row counts
window.updateTreeStats = function() {
    const visibleParishes = document.querySelectorAll('tr[data-level="2"]:not([style*="display: none"])').length;
    const visibleVillages = document.querySelectorAll('tr[data-level="3"]:not([style*="display: none"])').length;

    const parishCounter = document.getElementById('visibleParishes');
    const villageCounter = document.getElementById('visibleVillages');

    if (parishCounter) parishCounter.textContent = visibleParishes;
    if (villageCounter) villageCounter.textContent = visibleVillages;
};

// Override the original toggle function to update stats
const originalToggleTreeRow = window.toggleTreeRow;
window.toggleTreeRow = function(rowId) {
    originalToggleTreeRow(rowId);
    // Update stats after toggle
    setTimeout(updateTreeStats, 100);
};

// Override expand/collapse all to update stats
const originalExpandAllRows = window.expandAllRows;
window.expandAllRows = function() {
    originalExpandAllRows();
    // Update stats after expand all
    setTimeout(updateTreeStats, 200);
};

const originalCollapseAllRows = window.collapseAllRows;
window.collapseAllRows = function() {
    originalCollapseAllRows();
    // Update stats after collapse all
    setTimeout(updateTreeStats, 200);
};

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    console.log('Agent submissions report loaded');

    // Debug: Check tree structure
    const allRows = document.querySelectorAll('tr[data-id]');
    const rowsWithChildren = document.querySelectorAll('tr[data-has-children="true"]');
    const expandButtons = document.querySelectorAll('.expand-btn');

    console.log('Total rows:', allRows.length);
    console.log('Rows with children:', rowsWithChildren.length);
    console.log('Expand buttons:', expandButtons.length);

    // Log row structure
    allRows.forEach(row => {
        console.log('Row:', {
            id: row.dataset.id,
            level: row.dataset.level,
            type: row.dataset.type,
            hasChildren: row.dataset.hasChildren,
            parentId: row.dataset.parentId
        });
    });

    // Initialize tree stats
    updateTreeStats();

    // Initialize agent search functionality
    setupAgentSearch();

    // Initialize filters toggle state (if exists)
    const filtersContent = document.getElementById('filtersContent');
    if (filtersContent) {
        filtersContent.style.display = 'block'; // Show filters by default
    }
});
</script>
@endsection
