@extends('layouts.app')

@section('styles')
<style>
@media print {
    /* Hide everything except the table when printing */
    .navbar,
    .main-content > .container > *:not(.print-content),
    .btn,
    .alert,
    .card-header,
    .d-flex.justify-content-between,
    .mt-4.d-flex.justify-content-between {
        display: none !important;
    }

    /* Show only the table content */
    .print-content {
        display: block !important;
    }

    /* Print-specific table styling */
    .table {
        font-size: 12px;
        border-collapse: collapse;
        width: 100%;
    }

    .table th,
    .table td {
        border: 1px solid #000;
        padding: 8px;
        text-align: left;
    }

    .table th {
        background-color: #f8f9fa;
        font-weight: bold;
    }

    /* Hide badges and auto-generated indicators in print */
    .badge,
    .text-warning,
    .text-muted {
        display: none !important;
    }

    /* Simplify input groups for print */
    .input-group {
        display: inline !important;
    }

    .input-group .form-control {
        border: none !important;
        background: transparent !important;
        padding: 0 !important;
        font-weight: bold;
    }

    .input-group .btn {
        display: none !important;
    }

    /* Add print title */
    .print-content::before {
        content: "Import Results - Polling Stations and Agents";
        display: block;
        font-size: 18px;
        font-weight: bold;
        text-align: center;
        margin-bottom: 20px;
        border-bottom: 2px solid #000;
        padding-bottom: 10px;
    }

    /* Page break settings */
    .table {
        page-break-inside: auto;
    }

    .table tr {
        page-break-inside: avoid;
        page-break-after: auto;
    }

    .table thead {
        display: table-header-group;
    }

    .table tfoot {
        display: table-footer-group;
    }
}
</style>
@endsection

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Compact Header -->
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h1 class="h4 mb-0">
                    <i class="bi bi-check-circle text-success me-2"></i>Import Results
                </h1>
                <div class="btn-group btn-group-sm">
                    <a href="{{ route('polling_villages.import') }}" class="btn btn-outline-success">
                        <i class="bi bi-upload"></i> New Import
                    </a>
                    <a href="{{ route('polling_villages.index') }}" class="btn btn-outline-secondary">
                        <i class="bi bi-arrow-left"></i> Back to Villages
                    </a>
                </div>
            </div>

            <!-- Import Summary -->
            <div class="card mb-4">
                <div class="card-header bg-light py-2">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-clipboard-check text-primary me-2"></i>Import Summary
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body p-3 text-center">
                                    <h3 class="mb-0">{{ $success }}</h3>
                                    <div class="small">Successful Imports</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-danger text-white">
                                <div class="card-body p-3 text-center">
                                    <h3 class="mb-0">{{ $failed }}</h3>
                                    <div class="small">Failed Imports</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body p-3 text-center">
                                    <h3 class="mb-0">{{ count($stations) }}</h3>
                                    <div class="small">Stations Processed</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-dark">
                                <div class="card-body p-3 text-center">
                                    <h3 class="mb-0">{{ count($errors) }}</h3>
                                    <div class="small">Errors</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    @if(count($errors) > 0)
                        <div class="mt-4">
                            <h6 class="text-danger"><i class="bi bi-exclamation-triangle me-2"></i>Errors</h6>
                            <div class="alert alert-danger">
                                <ul class="mb-0">
                                    @foreach($errors as $error)
                                        <li>{{ $error }}</li>
                                    @endforeach
                                </ul>
                            </div>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Imported Stations & Agents -->
            <div class="card">
                <div class="card-header bg-light py-2">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-people text-success me-2"></i>Imported Stations & Agents
                    </h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        <strong>Important:</strong> Save or print this page to keep the agent passwords. They will not be shown again!
                    </div>

                    <div class="table-responsive print-content">
                        <table class="table table-sm table-striped table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>#</th>
                                    <th>Station Name</th>
                                    <th>Location</th>
                                    <th>Agent Name</th>
                                    <th>Phone Number</th>
                                    <th>Login Credentials</th>
                                    <th>Import Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($stations as $index => $item)
                                    <tr>
                                        <td>{{ $index + 1 }}</td>
                                        <td>
                                            <strong>{{ $item['station']->name }}</strong>
                                            @if($item['station']->code)
                                                <span class="badge bg-secondary">{{ $item['station']->code }}</span>
                                            @endif
                                        </td>
                                        <td>
                                            <small>
                                                {{ $item['station']->village }}, 
                                                {{ $item['station']->parish }}, 
                                                {{ $item['station']->subcounty }}
                                            </small>
                                        </td>
                                        <td>
                                            {{ $item['user']->name }}
                                            @if(isset($item['user']->name_generated) && $item['user']->name_generated)
                                                <span class="badge bg-warning text-dark ms-1" title="Name was auto-generated">Auto</span>
                                            @endif
                                        </td>
                                        <td>
                                            {{ $item['user']->phone_number }}
                                            @if(isset($item['user']->phone_generated) && $item['user']->phone_generated)
                                                <span class="badge bg-warning text-dark ms-1" title="Phone number was auto-generated">Auto</span>
                                            @endif
                                        </td>
                                        <td>
                                            @if($item['password'])
                                                <div class="input-group input-group-sm">
                                                    <input type="text" class="form-control form-control-sm" value="{{ $item['password'] }}" readonly>
                                                    <button class="btn btn-outline-secondary copy-btn" type="button" data-password="{{ $item['password'] }}">
                                                        <i class="bi bi-clipboard"></i>
                                                    </button>
                                                </div>
                                                <small class="text-muted">
                                                    Phone: {{ $item['user']->phone_number }}
                                                    @php
                                                        $autoGenerated = [];
                                                        if(isset($item['user']->name_generated) && $item['user']->name_generated) {
                                                            $autoGenerated[] = 'Name';
                                                        }
                                                        if(isset($item['user']->phone_generated) && $item['user']->phone_generated) {
                                                            $autoGenerated[] = 'Phone';
                                                        }
                                                        if(!empty($autoGenerated)) {
                                                            $autoGenerated[] = 'Password';
                                                        }
                                                    @endphp
                                                    @if(!empty($autoGenerated))
                                                        <span class="text-warning">({{ implode(' & ', $autoGenerated) }} Auto-Generated)</span>
                                                    @endif
                                                </small>
                                            @else
                                                <span class="badge bg-secondary">Existing User</span>
                                            @endif
                                        </td>
                                        <td>
                                            @php
                                                $mergeAction = $item['merge_action'] ?? 'created_new_agent';
                                                $mergeDetails = $item['merge_details'] ?? null;
                                                $warning = $item['warning'] ?? null;
                                            @endphp

                                            @if($mergeAction === 'created_new_agent')
                                                <span class="badge bg-success">New Agent</span>
                                            @elseif($mergeAction === 'existing_agent_found')
                                                <span class="badge bg-info">Existing Agent</span>
                                            @elseif($mergeAction === 'merged_with_similar')
                                                <span class="badge bg-warning text-dark">Merged</span>
                                                @if($mergeDetails)
                                                    <small class="d-block text-muted mt-1">
                                                        @foreach($mergeDetails as $detail)
                                                            {{ $detail }}<br>
                                                        @endforeach
                                                    </small>
                                                @endif
                                            @elseif($mergeAction === 'used_primary_agent')
                                                <span class="badge bg-secondary">Used Primary</span>
                                                @if($warning)
                                                    <small class="d-block text-warning mt-1">{{ $warning }}</small>
                                                @endif
                                            @else
                                                <span class="badge bg-light text-dark">{{ ucfirst(str_replace('_', ' ', $mergeAction)) }}</span>
                                            @endif
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>

                    <div class="mt-4 d-flex justify-content-between">
                        <a href="{{ route('polling_villages.index') }}" class="btn btn-outline-secondary">
                            <i class="bi bi-arrow-left me-1"></i>Back to Villages
                        </a>
                        <button class="btn btn-primary" onclick="window.print()">
                            <i class="bi bi-printer me-1"></i>Print Results
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Copy password to clipboard
    const copyButtons = document.querySelectorAll('.copy-btn');
    copyButtons.forEach(button => {
        button.addEventListener('click', function() {
            const password = this.getAttribute('data-password');
            navigator.clipboard.writeText(password).then(() => {
                // Change button text temporarily
                const originalHTML = this.innerHTML;
                this.innerHTML = '<i class="bi bi-check"></i>';
                this.classList.add('btn-success');
                this.classList.remove('btn-outline-secondary');
                
                setTimeout(() => {
                    this.innerHTML = originalHTML;
                    this.classList.remove('btn-success');
                    this.classList.add('btn-outline-secondary');
                }, 2000);
            });
        });
    });
});
</script>
@endpush

<style>
@media print {
    .btn-group, .btn, .copy-btn {
        display: none !important;
    }
    
    .card {
        border: 1px solid #ddd !important;
        margin-bottom: 20px !important;
    }
    
    .card-header {
        background-color: #f8f9fa !important;
        color: #000 !important;
    }
    
    .table {
        width: 100% !important;
        border-collapse: collapse !important;
    }
    
    .table th, .table td {
        border: 1px solid #ddd !important;
        padding: 8px !important;
    }
}
</style>
@endsection
