@extends('layouts.app')

@section('content')
<style>
    .page-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1.5rem;
    }
    
    .page-title {
        font-size: 1.5rem;
        font-weight: 600;
        color: #333;
        margin-bottom: 0;
    }
    
    .card-custom {
        border: none;
        border-radius: 12px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        overflow: hidden;
    }
    
    .card-header-custom {
        background: #fff;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        padding: 1.25rem 1.5rem;
    }
    
    .card-body-custom {
        padding: 1.5rem;
    }
    
    .btn-add {
        background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
        border: none;
        color: #333;
        font-weight: 600;
        padding: 0.6rem 1.25rem;
        border-radius: 8px;
        display: flex;
        align-items: center;
        box-shadow: 0 4px 10px rgba(255, 215, 0, 0.3);
        transition: all 0.3s;
    }

    .btn-add:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 15px rgba(255, 215, 0, 0.4);
        color: #333;
        background: linear-gradient(135deg, #FFC107 0%, #FF8C00 100%);
    }
    
    .btn-add i {
        margin-right: 8px;
        font-size: 1rem;
    }
    
    .table-custom {
        width: 100%;
        border-collapse: separate;
        border-spacing: 0 8px;
        margin-top: 0.5rem;
    }
    
    .table-custom thead th {
        border: none;
        background-color: #f8f9fa;
        color: #6c757d;
        font-weight: 500;
        padding: 0.75rem 1.25rem;
        font-size: 0.85rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }
    
    .table-custom tbody tr {
        background-color: #fff;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.03);
        border-radius: 8px;
        transition: all 0.2s;
    }
    
    .table-custom tbody tr:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.07);
    }
    
    .table-custom td {
        padding: 1rem 1.25rem;
        vertical-align: middle;
        border: none;
    }
    
    .table-custom td:first-child {
        border-top-left-radius: 8px;
        border-bottom-left-radius: 8px;
    }
    
    .table-custom td:last-child {
        border-top-right-radius: 8px;
        border-bottom-right-radius: 8px;
    }
    
    .station-name {
        font-weight: 500;
        color: #333;
    }
    
    .constituency-badge {
        background-color: rgba(255, 215, 0, 0.15);
        color: #FF8C00;
        font-size: 0.8rem;
        padding: 0.35rem 0.75rem;
        border-radius: 30px;
        font-weight: 500;
        display: inline-block;
    }
    
    .action-buttons {
        display: flex;
        gap: 8px;
    }
    
    .btn-action {
        border: none;
        padding: 0.4rem 0.75rem;
        border-radius: 6px;
        font-weight: 500;
        font-size: 0.85rem;
        display: flex;
        align-items: center;
        transition: all 0.2s;
    }
    
    .btn-action i {
        margin-right: 5px;
        font-size: 0.9rem;
    }
    
    .btn-agents {
        background-color: rgba(25, 135, 84, 0.1);
        color: #198754;
    }
    
    .btn-agents:hover {
        background-color: #198754;
        color: white;
    }
    
    .btn-edit {
        background-color: rgba(13, 110, 253, 0.1);
        color: #0d6efd;
    }
    
    .btn-edit:hover {
        background-color: #0d6efd;
        color: white;
    }
    
    .btn-delete {
        background-color: rgba(220, 53, 69, 0.1);
        color: #dc3545;
    }
    
    .btn-delete:hover {
        background-color: #dc3545;
        color: white;
    }
    
    .empty-state {
        text-align: center;
        padding: 3rem 1rem;
    }
    
    .empty-state-icon {
        font-size: 3rem;
        color: #dee2e6;
        margin-bottom: 1rem;
    }
    
    .empty-state-text {
        color: #6c757d;
        font-weight: 500;
        margin-bottom: 1.5rem;
    }
    


    .location-info {
        line-height: 1.3;
    }

    .location-info small {
        font-size: 0.8rem;
    }

    /* Stats Cards */
    .stats-card {
        transition: all 0.3s ease;
        border-radius: 12px;
        overflow: hidden;
    }

    .stats-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.1) !important;
    }

    .stats-icon {
        width: 45px;
        height: 45px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.2rem;
    }

    .stats-icon.bg-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    }

    .stats-icon.bg-success {
        background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%) !important;
    }

    .stats-icon.bg-warning {
        background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%) !important;
    }

    .stats-icon.bg-info {
        background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%) !important;
    }

    /* Filter section styling */
    .form-select-sm:focus {
        border-color: #FFD700;
        box-shadow: 0 0 0 0.2rem rgba(255, 215, 0, 0.25);
    }

    /* Card View Styling */
    .station-card-item {
        transition: all 0.3s ease;
        border-radius: 12px;
        overflow: hidden;
    }

    .station-card-item:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15) !important;
    }

    .station-card-item .card-title {
        color: #333;
        font-size: 1rem;
    }

    .station-card-item .btn-sm {
        padding: 0.25rem 0.5rem;
        font-size: 0.8rem;
        border-radius: 6px;
    }

    /* View toggle buttons */
    .btn-group .btn.active {
        background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
        border-color: #FFD700;
        color: #333;
    }

    /* Compact Evidence and Votes styling */
    .btn-xs {
        padding: 2px 6px;
        font-size: 0.7rem;
        line-height: 1.2;
    }

    .view-evidence-btn {
        transition: all 0.2s ease;
    }

    .view-evidence-btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(13, 110, 253, 0.3);
    }

    /* Compact table styling */
    .table-custom td {
        padding: 0.75rem 1rem;
        vertical-align: middle;
        border: none;
    }

    .station-icon, .agent-icon {
        flex-shrink: 0;
    }

    .location-info {
        line-height: 1.3;
    }
</style>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="page-header">
        <div>
            <h4 class="page-title">Polling Stations</h4>
            <p class="text-muted">Manage all polling stations in the election</p>
        </div>
        <div class="d-flex gap-2">
            
            <button type="button" class="btn btn-add" data-bs-toggle="modal" data-bs-target="#add_pollingstation">
                <i class="bi bi-plus-circle"></i> Add Polling Station
            </button>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card stats-card border-0 shadow-sm">
                <div class="card-body p-3">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon bg-primary">
                            <i class="bi bi-building text-white"></i>
                        </div>
                        <div class="ms-3">
                            <h5 class="mb-0 fw-bold">{{ $polling_stations->count() }}</h5>
                            <small class="text-muted">Total Stations</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stats-card border-0 shadow-sm">
                <div class="card-body p-3">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon bg-success">
                            <i class="bi bi-geo-alt text-white"></i>
                        </div>
                        <div class="ms-3">
                            <h5 class="mb-0 fw-bold" id="stationsWithCoordsCard">{{ $polling_stations->whereNotNull('latitude')->whereNotNull('longitude')->count() }}</h5>
                            <small class="text-muted">With Coordinates</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stats-card border-0 shadow-sm">
                <div class="card-body p-3">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon bg-warning">
                            <i class="bi bi-people text-white"></i>
                        </div>
                        <div class="ms-3">
                            <h5 class="mb-0 fw-bold">{{ $polling_stations->sum(function($station) { return $station->agents->count(); }) }}</h5>
                            <small class="text-muted">Total Agents</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stats-card border-0 shadow-sm">
                <div class="card-body p-3">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon bg-info">
                            <i class="bi bi-geo text-white"></i>
                        </div>
                        <div class="ms-3">
                            <h5 class="mb-0 fw-bold">{{ $polling_stations->whereNotNull('district')->groupBy('district')->count() }}</h5>
                            <small class="text-muted">Districts</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Enhanced Filters Section -->
    <div class="card card-custom mb-4">
        <div class="card-header-custom">
            <div class="d-flex justify-content-between align-items-center">
                <div class="d-flex align-items-center">
                    <i class="bi bi-funnel me-2 text-warning"></i>
                    <span class="fw-medium">Advanced Filters</span>
                    <span class="badge bg-info ms-2">{{ $polling_stations->total() }} stations found</span>
                </div>
                <div class="d-flex gap-2">
                    <a href="{{ route('polling_stations.index') }}" class="btn btn-sm btn-outline-secondary">
                        <i class="bi bi-x-circle me-1"></i>Clear All
                    </a>
                </div>
            </div>
        </div>
        <div class="card-body-custom">
            <form method="GET" action="{{ route('polling_stations.index') }}" id="filtersForm">
                <div class="row g-3">
                    <!-- Search -->
                    <div class="col-md-3">
                        <label class="form-label small fw-bold">
                            <i class="bi bi-search me-1"></i>Search
                        </label>
                        <input type="text" name="search" class="form-control form-control-sm"
                               placeholder="Search stations, locations..."
                               value="{{ $currentFilters['search'] ?? '' }}">
                    </div>

                    <!-- District Filter -->
                    <div class="col-md-2">
                        <label class="form-label small fw-bold">
                            <i class="bi bi-geo-alt me-1"></i>District
                        </label>
                        <select name="district" class="form-select form-select-sm" id="districtFilter">
                            <option value="">All Districts</option>
                            @foreach($districts as $district)
                                <option value="{{ $district }}" {{ ($currentFilters['district'] ?? '') == $district ? 'selected' : '' }}>
                                    {{ $district }}
                                </option>
                            @endforeach
                        </select>
                    </div>

                    <!-- County Filter -->
                    <div class="col-md-2">
                        <label class="form-label small fw-bold">
                            <i class="bi bi-map me-1"></i>County
                        </label>
                        <select name="county" class="form-select form-select-sm">
                            <option value="">All Counties</option>
                            @foreach($counties as $county)
                                <option value="{{ $county }}" {{ ($currentFilters['county'] ?? '') == $county ? 'selected' : '' }}>
                                    {{ $county }}
                                </option>
                            @endforeach
                        </select>
                    </div>

                    <!-- Subcounty Filter -->
                    <div class="col-md-2">
                        <label class="form-label small fw-bold">
                            <i class="bi bi-building me-1"></i>Subcounty
                        </label>
                        <select name="subcounty" class="form-select form-select-sm">
                            <option value="">All Subcounties</option>
                            @foreach($subcounties as $subcounty)
                                <option value="{{ $subcounty }}" {{ ($currentFilters['subcounty'] ?? '') == $subcounty ? 'selected' : '' }}>
                                    {{ $subcounty }}
                                </option>
                            @endforeach
                        </select>
                    </div>

                    <!-- Coordinates Filter -->
                    <div class="col-md-2">
                        <label class="form-label small fw-bold">
                            <i class="bi bi-geo me-1"></i>Coordinates
                        </label>
                        <select name="coordinates" class="form-select form-select-sm">
                            <option value="">All Stations</option>
                            <option value="with" {{ ($currentFilters['coordinates'] ?? '') == 'with' ? 'selected' : '' }}>With Coordinates</option>
                            <option value="without" {{ ($currentFilters['coordinates'] ?? '') == 'without' ? 'selected' : '' }}>Without Coordinates</option>
                        </select>
                    </div>

                    <!-- Agents Filter -->
                    <div class="col-md-1">
                        <label class="form-label small fw-bold">
                            <i class="bi bi-people me-1"></i>Agents
                        </label>
                        <select name="agents" class="form-select form-select-sm">
                            <option value="">All</option>
                            <option value="with" {{ ($currentFilters['agents'] ?? '') == 'with' ? 'selected' : '' }}>With</option>
                            <option value="without" {{ ($currentFilters['agents'] ?? '') == 'without' ? 'selected' : '' }}>Without</option>
                        </select>
                    </div>
                </div>

                <div class="row g-3 mt-2">
                    <!-- Results Filter -->
                    <div class="col-md-2">
                        <label class="form-label small fw-bold">
                            <i class="bi bi-check-circle me-1"></i>Results
                        </label>
                        <select name="results" class="form-select form-select-sm">
                            <option value="">All Stations</option>
                            <option value="submitted" {{ ($currentFilters['results'] ?? '') == 'submitted' ? 'selected' : '' }}>Submitted</option>
                            <option value="pending" {{ ($currentFilters['results'] ?? '') == 'pending' ? 'selected' : '' }}>Pending</option>
                        </select>
                    </div>

                    <!-- Evidence Filter -->
                    <div class="col-md-2">
                        <label class="form-label small fw-bold">
                            <i class="bi bi-file-earmark me-1"></i>Evidence
                        </label>
                        <select name="evidence" class="form-select form-select-sm">
                            <option value="">All Stations</option>
                            <option value="with" {{ ($currentFilters['evidence'] ?? '') == 'with' ? 'selected' : '' }}>With Evidence</option>
                            <option value="without" {{ ($currentFilters['evidence'] ?? '') == 'without' ? 'selected' : '' }}>Without Evidence</option>
                        </select>
                    </div>

                    <!-- Sort By -->
                    <div class="col-md-2">
                        <label class="form-label small fw-bold">
                            <i class="bi bi-sort-down me-1"></i>Sort By
                        </label>
                        <select name="sort_by" class="form-select form-select-sm">
                            <option value="name" {{ ($currentFilters['sort_by'] ?? 'name') == 'name' ? 'selected' : '' }}>Name</option>
                            <option value="district" {{ ($currentFilters['sort_by'] ?? '') == 'district' ? 'selected' : '' }}>District</option>
                            <option value="county" {{ ($currentFilters['sort_by'] ?? '') == 'county' ? 'selected' : '' }}>County</option>
                            <option value="created_at" {{ ($currentFilters['sort_by'] ?? '') == 'created_at' ? 'selected' : '' }}>Date Created</option>
                        </select>
                    </div>

                    <!-- Sort Order -->
                    <div class="col-md-2">
                        <label class="form-label small fw-bold">
                            <i class="bi bi-arrow-up-down me-1"></i>Order
                        </label>
                        <select name="sort_order" class="form-select form-select-sm">
                            <option value="asc" {{ ($currentFilters['sort_order'] ?? 'asc') == 'asc' ? 'selected' : '' }}>Ascending</option>
                            <option value="desc" {{ ($currentFilters['sort_order'] ?? '') == 'desc' ? 'selected' : '' }}>Descending</option>
                        </select>
                    </div>

                    <!-- Per Page -->
                    <div class="col-md-2">
                        <label class="form-label small fw-bold">
                            <i class="bi bi-list me-1"></i>Per Page
                        </label>
                        <select name="per_page" class="form-select form-select-sm">
                            <option value="10" {{ ($currentFilters['per_page'] ?? 20) == 10 ? 'selected' : '' }}>10</option>
                            <option value="20" {{ ($currentFilters['per_page'] ?? 20) == 20 ? 'selected' : '' }}>20</option>
                            <option value="50" {{ ($currentFilters['per_page'] ?? 20) == 50 ? 'selected' : '' }}>50</option>
                            <option value="100" {{ ($currentFilters['per_page'] ?? 20) == 100 ? 'selected' : '' }}>100</option>
                        </select>
                    </div>

                    <!-- Action Buttons -->
                    <div class="col-md-2 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary btn-sm w-100">
                            <i class="bi bi-funnel me-1"></i>Apply Filters
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    
    <!-- Main Content Card -->
    <div class="card card-custom" id="listViewContainer">
        
        <div class="card-body-custom p-4">
            @if(count($polling_stations) > 0)
            <div class="table-responsive">
                <table class="table table-custom">
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th>Location</th>
                            <th>Agent</th>
                            <th class="text-center">Evidence</th>
                            <th class="text-center">Votes</th>
                            <th class="text-center">Status</th>
                            <th class="text-end">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach ($polling_stations as $polling_station)
                            @php
                                // Check if agent has submitted any votes and get the latest submission time
                                $latestVote = null;
                                $hasSubmittedResults = false;
                                $stationSpoiledVotes = 0;
                                $evidenceCount = 0;
                                $totalStationVotes = 0;

                                if($polling_station->agent) {
                                    $latestVote = \App\Models\Vote::where('agent_id', $polling_station->agent->id)
                                        ->orderBy('created_at', 'desc')
                                        ->first();
                                    $hasSubmittedResults = $latestVote !== null;

                                    // Get latest 2 evidence records for this agent
                                    $latestEvidence = \App\Models\Eveidence::where('agent_id', $polling_station->agent->id)
                                        ->latest('created_at')
                                        ->limit(2)
                                        ->get();
                                    $evidenceCount = $latestEvidence->count();

                                    // Calculate total votes for this station across all positions
                                    foreach ($positions as $position) {
                                        $totalStationVotes += $position->totalStationVotes($polling_station->id);
                                    }
                                }

                                // Get spoiled votes for this polling station
                                $stationSpoiledVotes = isset($spoiledVotesByStation[$polling_station->id]) ? $spoiledVotesByStation[$polling_station->id]->total : 0;
                            @endphp
                            <tr data-station-id="{{ $polling_station->id }}">
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="station-icon me-2 rounded-circle d-flex align-items-center justify-content-center"
                                             style="width: 32px; height: 32px; background-color: rgba(13, 110, 253, 0.1);">
                                            <i class="bi bi-building text-primary"></i>
                                        </div>
                                        <div>
                                            <div class="station-name fw-bold">{{ $polling_station->name }}</div>
                                            <div class="small text-muted">Code: {{ $polling_station->code ?? 'N/A' }}</div>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div class="location-info">
                                        @if($polling_station->district)
                                            <div class="fw-bold text-primary" style="font-size: 0.85rem;">{{ $polling_station->district }}</div>
                                        @endif
                                        @if($polling_station->county || $polling_station->subcounty)
                                            <small class="text-muted">
                                                {{ $polling_station->county }}{{ $polling_station->county && $polling_station->subcounty ? ' • ' : '' }}{{ $polling_station->subcounty }}
                                            </small>
                                        @endif
                                    </div>
                                </td>
                                <td>
                                    @if($polling_station->agent)
                                        <div class="d-flex align-items-center">
                                            <div class="agent-icon me-2 rounded-circle d-flex align-items-center justify-content-center"
                                                 style="width: 28px; height: 28px; background-color: rgba(108, 117, 125, 0.1);">
                                                <i class="bi bi-person text-secondary"></i>
                                            </div>
                                            <div>
                                                <div style="font-size: 0.85rem;">{{ $polling_station->agent->user->name }}</div>
                                                <div class="small text-muted">
                                                    <i class="bi bi-telephone me-1"></i>
                                                    {{ $polling_station->agent->user->phone_number }}
                                                </div>
                                            </div>
                                        </div>
                                    @else
                                        <span class="text-muted small">No agent assigned</span>
                                    @endif
                                </td>
                                <td class="text-center">
                                    @if($polling_station->agent && $evidenceCount > 0)
                                        <div class="d-flex flex-column align-items-center">
                                            <span class="badge bg-info mb-1">{{ $evidenceCount }}</span>
                                            <button type="button" class="btn btn-xs btn-outline-primary"
                                                    onclick="showEvidenceModal({{ $polling_station->id }}, {{ $polling_station->agent->id }})"
                                                    title="View Evidence">
                                                <i class="bi bi-eye"></i>
                                            </button>
                                        </div>
                                    @else
                                        <span class="text-muted small">--</span>
                                    @endif
                                </td>
                                <td class="text-center">
                                    @if($totalStationVotes > 0)
                                        <div class="d-flex flex-column align-items-center">
                                            <span class="fw-bold text-success">{{ number_format($totalStationVotes) }}</span>
                                            @if($stationSpoiledVotes > 0)
                                                <small class="text-danger">{{ $stationSpoiledVotes }} spoiled</small>
                                            @endif
                                        </div>
                                    @else
                                        <span class="text-muted small">--</span>
                                    @endif
                                </td>
                                <td class="text-center">
                                    @if($polling_station->agent)
                                        @if($hasSubmittedResults)
                                            <span class="badge bg-success"><i class="bi bi-check-circle me-1"></i> Submitted</span>
                                        @else
                                            <span class="badge bg-warning text-dark"><i class="bi bi-exclamation-triangle me-1"></i> Pending</span>
                                        @endif
                                    @else
                                        <span class="badge bg-secondary"><i class="bi bi-dash-circle me-1"></i> No Agent</span>
                                    @endif
                                </td>
                                <td>
                                    <div class="action-buttons justify-content-end">
                                        <a href="{{ route('polling_stations.show',$polling_station->id) }}" class="btn btn-action btn-agents">
                                            <i class="bi bi-people"></i>
                                        </a>
                                        <a href="{{ route('polling_stations.edit',$polling_station->id) }}" class="btn btn-action btn-edit">
                                            <i class="bi bi-pencil"></i>
                                        </a>
                                        @if($polling_station->agents->count() == 0)
                                            <form action="{{ route('polling_stations.destroy',$polling_station->id) }}" method="post" class="d-inline">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" class="btn btn-action btn-delete" onclick="return confirm('Are you sure you want to delete this polling station?')">
                                                    <i class="bi bi-trash"></i>
                                                </button>
                                            </form>
                                        @endif
                                    </div>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>

            <!-- Card View (Hidden by default) -->
            <div id="cardViewContainer" style="display: none;">
                @if(count($polling_stations) > 0)
                <div class="row g-3">
                    @foreach ($polling_stations as $polling_station)
                    <div class="col-md-6 col-lg-4 station-card"
                         data-name="{{ strtolower($polling_station->name) }}"
                         data-district="{{ $polling_station->district ?? '' }}"
                         data-county="{{ $polling_station->county ?? '' }}"
                         data-has-coordinates="{{ ($polling_station->latitude && $polling_station->longitude) ? 'true' : 'false' }}"
                         data-agents-count="{{ $polling_station->agents->count() }}">
                        <div class="card station-card-item border-0 shadow-sm h-100">
                            <div class="card-body p-3">
                                <div class="d-flex justify-content-between align-items-start mb-2">
                                    <h6 class="card-title mb-0 fw-bold">{{ $polling_station->name }}</h6>
                                    @if($polling_station->latitude && $polling_station->longitude)
                                        <span class="badge bg-success">
                                            <i class="bi bi-geo-alt"></i>
                                        </span>
                                    @else
                                        <span class="badge bg-light text-muted">
                                            <i class="bi bi-geo-alt"></i>
                                        </span>
                                    @endif
                                </div>

                                <!-- Location Info -->
                                @if($polling_station->district || $polling_station->county || $polling_station->subcounty)
                                <div class="location-info mb-2">
                                    @if($polling_station->district)
                                        <div class="text-primary fw-bold small">{{ $polling_station->district }} District</div>
                                    @endif
                                    @if($polling_station->county)
                                        <small class="text-muted">{{ $polling_station->county }}</small>
                                    @endif
                                    @if($polling_station->subcounty)
                                        <small class="text-muted"> • {{ $polling_station->subcounty }}</small>
                                    @endif
                                    @if($polling_station->village)
                                        <div><small class="badge bg-light text-dark mt-1">{{ $polling_station->village }}</small></div>
                                    @endif
                                </div>
                                @elseif($polling_station->constituency)
                                <div class="mb-2">
                                    <span class="constituency-badge">{{ $polling_station->constituency }}</span>
                                </div>
                                @endif

                                <!-- Stats -->
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <small class="text-muted">
                                        <i class="bi bi-people me-1"></i>{{ $polling_station->agents->count() }} agents
                                    </small>
                                    @if($polling_station->latitude && $polling_station->longitude)
                                        <small class="text-success">
                                            <i class="bi bi-geo-alt me-1"></i>Mapped
                                        </small>
                                    @else
                                        <small class="text-muted">
                                            <i class="bi bi-geo-alt me-1"></i>No coordinates
                                        </small>
                                    @endif
                                </div>

                                <!-- Actions -->
                                <div class="d-flex gap-1">
                                    <a href="{{ route('polling_stations.show',$polling_station->id) }}" class="btn btn-sm btn-outline-success flex-fill">
                                        <i class="bi bi-people"></i> Agents
                                    </a>
                                    <a href="{{ route('polling_stations.edit',$polling_station->id) }}" class="btn btn-sm btn-outline-primary">
                                        <i class="bi bi-pencil"></i>
                                    </a>
                                    @if($polling_station->agents->count() == 0)
                                        <form action="{{ route('polling_stations.destroy',$polling_station->id) }}" method="post" class="d-inline">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="btn btn-sm btn-outline-danger" onclick="return confirm('Are you sure?')">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                        </form>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                    @endforeach
                </div>
                @else
                <div class="empty-state">
                    <div class="empty-state-icon">
                        <i class="bi bi-building"></i>
                    </div>
                    <h5 class="empty-state-text">No polling stations found</h5>
                    <a href="{{ route('polling_stations.create') }}" class="btn btn-add">
                        <i class="bi bi-plus-circle"></i> Add Your First Polling Station
                    </a>
                </div>
                @endif
            </div>

            @else
            <div class="empty-state">
                <div class="empty-state-icon">
                    <i class="bi bi-building"></i>
                </div>
                <h5 class="empty-state-text">No polling stations found</h5>
                <button type="button" class="btn btn-add" data-bs-toggle="modal" data-bs-target="#add_pollingstation">
                    <i class="bi bi-plus-circle"></i> Add Your First Polling Station
                </button>
            </div>
            @endif

            <!-- Pagination Controls -->
            @if($polling_stations->hasPages())
            <div class="d-flex justify-content-between align-items-center mt-4 pt-3 border-top">
                <div class="showing-entries">
                    <small class="text-muted">
                        Showing {{ $polling_stations->firstItem() ?? 0 }} to {{ $polling_stations->lastItem() ?? 0 }}
                        of {{ $polling_stations->total() }} stations
                    </small>
                </div>
                <div class="pagination-container">
                    {{ $polling_stations->links('pagination::bootstrap-4') }}
                </div>
            </div>
            @endif
        </div>
    </div>
</div>





<style>
    .coordinates-badge {
        cursor: pointer;
        transition: all 0.2s;
    }
    .coordinates-badge:hover {
        background-color: #198754 !important;
        box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        transform: scale(1.05);
    }

    .loading-overlay.show {
        display: flex;
    }
</style>

<script>
// Simple evidence modal functions - defined globally
function showEvidenceModal(stationId, agentId) {
    console.log('showEvidenceModal called with stationId:', stationId, 'agentId:', agentId);

    // Check if modal exists
    const modalElement = document.getElementById('evidenceModal');
    if (!modalElement) {
        console.error('Evidence modal element not found!');
        alert('Evidence modal not found! Please refresh the page.');
        return;
    }

    try {
        // Show modal
        const modal = new bootstrap.Modal(modalElement);
        modal.show();
        console.log('Modal shown successfully');

        // Load evidence
        loadEvidence(stationId);
    } catch (error) {
        console.error('Error in showEvidenceModal:', error);
        alert('Error showing modal: ' + error.message);
    }
}

function loadEvidence(stationId) {
    console.log('loadEvidence called for stationId:', stationId);

    const content = document.getElementById('evidenceContent');
    if (!content) {
        console.error('evidenceContent element not found!');
        return;
    }

    content.innerHTML = '<div class="text-center py-4"><div class="spinner-border"></div><p class="mt-2">Loading...</p></div>';

    console.log('Fetching evidence from:', `/api/station-evidence/${stationId}`);

    fetch(`/api/station-evidence/${stationId}`)
        .then(response => {
            console.log('Response status:', response.status);
            return response.json();
        })
        .then(data => {
            console.log('Evidence data received:', data);
            if (data.success && data.evidence.length > 0) {
                displayEvidence(data.evidence);
            } else {
                content.innerHTML = '<div class="text-center py-4"><p class="text-muted">No evidence found</p></div>';
            }
        })
        .catch(error => {
            console.error('Error loading evidence:', error);
            content.innerHTML = '<div class="text-center py-4"><p class="text-danger">Error loading evidence: ' + error.message + '</p></div>';
        });
}

function displayEvidence(evidence) {
    const content = document.getElementById('evidenceContent');
    let html = '<div class="row">';

    evidence.forEach(item => {
        html += `
            <div class="col-md-6 mb-3">
                <div class="card">
                    <div class="card-body">
                        <h6>${item.file_name || 'Evidence File'}</h6>
                        <p class="small text-muted">${new Date(item.created_at).toLocaleString()}</p>
                        <div class="d-flex gap-2">
                            <a href="/files/${item.file_url}" target="_blank" class="btn btn-sm btn-primary">
                                <i class="bi bi-eye"></i> View
                            </a>
                            <a href="/files/${item.file_url}" download class="btn btn-sm btn-success">
                                <i class="bi bi-download"></i> Download
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        `;
    });

    html += '</div>';
    content.innerHTML = html;
}

document.addEventListener('DOMContentLoaded', function() {
        // Initialize stations data from DOM
        let stationsData = [];

        // Extract data from table rows
        const tableRows = document.querySelectorAll('tbody tr');
        tableRows.forEach((row, index) => {
            const nameCell = row.querySelector('td:first-child');
            const locationCell = row.querySelector('td:nth-child(2)');
            const agentsCell = row.querySelector('td:nth-child(3)');

            if (nameCell && locationCell) {
                const nameText = nameCell.textContent.trim();
                const locationText = locationCell.textContent.trim();
                const agentsText = agentsCell ? agentsCell.textContent.trim() : '';

                // Parse location (District, County format)
                const locationParts = locationText.split(',').map(part => part.trim());
                const district = locationParts[0] || '';
                const county = locationParts[1] || '';

                // Check for coordinates (basic check)
                const hasCoordinates = row.querySelector('.text-success') !== null;

                // Count agents
                const agentsCount = agentsText.includes('No agent') ? 0 : 1;

                stationsData.push({
                    name: nameText,
                    district: district,
                    county: county,
                    latitude: hasCoordinates ? 1 : null,
                    longitude: hasCoordinates ? 1 : null,
                    agentsCount: agentsCount,
                    tableRow: row,
                    cardElement: null // Will be set if card view is implemented
                });
            }
        });

        // View switching functionality
        const tableViewBtn = document.getElementById('tableView');
        const cardViewBtn = document.getElementById('cardView');
        const tableContainer = document.querySelector('.table-responsive');
        const cardContainer = document.getElementById('cardViewContainer');

        // Set initial view (table)
        tableViewBtn.classList.add('active');

        tableViewBtn.addEventListener('click', function() {
            tableContainer.style.display = 'block';
            cardContainer.style.display = 'none';
            tableViewBtn.classList.add('active');
            cardViewBtn.classList.remove('active');
        });

        cardViewBtn.addEventListener('click', function() {
            tableContainer.style.display = 'none';
            cardContainer.style.display = 'block';
            cardViewBtn.classList.add('active');
            tableViewBtn.classList.remove('active');
        });

        // Enhanced server-side filtering functionality
        const filtersForm = document.getElementById('filtersForm');
        const searchInput = document.querySelector('input[name="search"]');
        const districtFilter = document.getElementById('districtFilter');
        const countyFilter = document.querySelector('select[name="county"]');
        const coordinatesFilter = document.querySelector('select[name="coordinates"]');
        const agentsFilter = document.querySelector('select[name="agents"]');
        const clearFiltersBtn = document.getElementById('clearFilters');

        // Auto-submit form when filters change (with debouncing)
        let filterTimeout;

        function debounceFilter(callback, delay = 500) {
            clearTimeout(filterTimeout);
            filterTimeout = setTimeout(callback, delay);
        }

        // Auto-submit on filter change
        if (filtersForm) {
            const filterInputs = filtersForm.querySelectorAll('select, input[name="search"]');

            filterInputs.forEach(input => {
                if (input.name === 'search') {
                    // Debounce search input
                    input.addEventListener('input', function() {
                        debounceFilter(() => {
                            showLoadingOverlay();
                            filtersForm.submit();
                        });
                    });
                } else {
                    // Immediate submit for select filters
                    input.addEventListener('change', function() {
                        showLoadingOverlay();
                        filtersForm.submit();
                    });
                }
            });
        }

        // Loading overlay functions
        function showLoadingOverlay() {
            let overlay = document.querySelector('.loading-overlay');
            if (!overlay) {
                overlay = document.createElement('div');
                overlay.className = 'loading-overlay';
                overlay.innerHTML = `
                    <div class="text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <div class="mt-2">Applying filters...</div>
                    </div>
                `;
                overlay.style.cssText = `
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(255, 255, 255, 0.8);
                    display: flex;
                    z-index: 9999;
                    align-items: center;
                    justify-content: center;
                `;
                document.body.appendChild(overlay);
            }
            overlay.style.display = 'flex';
        }

        function hideLoadingOverlay() {
            const overlay = document.querySelector('.loading-overlay');
            if (overlay) {
                overlay.style.display = 'none';
            }
        }

        // Hide loading overlay when page loads
        hideLoadingOverlay();

        // Performance optimization: Lazy load images
        const images = document.querySelectorAll('img[data-src]');
        const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.src = img.dataset.src;
                    img.removeAttribute('data-src');
                    observer.unobserve(img);
                }
            });
        });

        images.forEach(img => imageObserver.observe(img));

        // Apply filters function
        function applyFilters() {
            let visibleCount = 0;
            const searchTerm = searchInput ? searchInput.value.toLowerCase() : '';
            const selectedDistrict = districtFilter ? districtFilter.value : '';
            const selectedCounty = countyFilter ? countyFilter.value : '';
            const selectedCoordinates = coordinatesFilter ? coordinatesFilter.value : '';
            const selectedAgents = agentsFilter ? agentsFilter.value : '';

            stationsData.forEach(station => {
                let visible = true;

                // Search filter
                if (searchTerm && !station.name.toLowerCase().includes(searchTerm)) {
                    visible = false;
                }

                // District filter
                if (selectedDistrict && station.district !== selectedDistrict) {
                    visible = false;
                }

                // County filter
                if (selectedCounty && station.county !== selectedCounty) {
                    visible = false;
                }

                // Coordinates filter
                if (selectedCoordinates === 'with' && (!station.latitude || !station.longitude)) {
                    visible = false;
                } else if (selectedCoordinates === 'without' && station.latitude && station.longitude) {
                    visible = false;
                } else if (selectedCoordinates === 'invalid' && station.latitude && station.longitude && isWithinUganda(station.latitude, station.longitude)) {
                    visible = false;
                }

                // Agents filter
                if (selectedAgents === 'with' && station.agentsCount === 0) {
                    visible = false;
                } else if (selectedAgents === 'without' && station.agentsCount > 0) {
                    visible = false;
                }

                // Apply visibility to both table row and card
                if (station.tableRow) {
                    station.tableRow.style.display = visible ? '' : 'none';
                }
                if (station.cardElement) {
                    station.cardElement.style.display = visible ? '' : 'none';
                }

                if (visible) visibleCount++;
            });

            // Update visible count
            updateVisibleCount(visibleCount);
        }

        // Update county filter based on district selection
        function updateCountyFilter(selectedDistrict) {
            countyFilter.innerHTML = '<option value="">All Counties</option>';

            if (selectedDistrict) {
                const counties = new Set();
                stationsData.forEach(station => {
                    if (station.district === selectedDistrict && station.county) {
                        counties.add(station.county);
                    }
                });

                counties.forEach(county => {
                    const option = document.createElement('option');
                    option.value = county;
                    option.textContent = county;
                    countyFilter.appendChild(option);
                });

                countyFilter.disabled = counties.size === 0;
            } else {
                countyFilter.disabled = true;
            }
        }

        // Update visible count display
        function updateVisibleCount(count) {
            const totalCount = stationsData.length;
            const countDisplay = document.querySelector('.card-header-custom .fw-medium');
            if (countDisplay) {
                countDisplay.innerHTML = `All Polling Stations <span class="badge bg-info ms-2">${count} of ${totalCount} shown</span>`;
            }
        }

        // Event listeners with error handling
        if (searchInput) {
            searchInput.addEventListener('keyup', applyFilters);
        } else {
            console.error('Search input not found');
        }

        if (districtFilter) {
            districtFilter.addEventListener('change', function() {
                updateCountyFilter(this.value);
                if (countyFilter) countyFilter.value = ''; // Reset county selection
                applyFilters();
            });
        }

        if (countyFilter) {
            countyFilter.addEventListener('change', applyFilters);
        }

        if (coordinatesFilter) {
            coordinatesFilter.addEventListener('change', applyFilters);
        }

        if (agentsFilter) {
            agentsFilter.addEventListener('change', applyFilters);
        }

        // Clear filters
        if (clearFiltersBtn) {
            clearFiltersBtn.addEventListener('click', function() {
                if (searchInput) searchInput.value = '';
                if (districtFilter) districtFilter.value = '';
                if (countyFilter) countyFilter.value = '';
                if (coordinatesFilter) coordinatesFilter.value = '';
                if (agentsFilter) agentsFilter.value = '';
                updateCountyFilter('');
                applyFilters();
            });
        }

        // Initialize
        console.log('Stations data loaded:', stationsData.length);
        updateVisibleCount(stationsData.length);

        // Debug: Log first few stations data
        if (stationsData.length > 0) {
            console.log('Sample station data:', stationsData.slice(0, 3));
        }

        // Handle evidence viewing - using event delegation
        document.body.addEventListener('click', function(e) {
            console.log('Click detected on:', e.target);

            if (e.target.closest('.view-evidence-btn')) {
                e.preventDefault();
                e.stopPropagation();

                const btn = e.target.closest('.view-evidence-btn');
                const stationId = btn.dataset.stationId;
                const agentId = btn.dataset.agentId;

                console.log('Evidence button clicked:', { stationId, agentId, btn });

                // Check if modal exists
                const modalElement = document.getElementById('evidenceModal');
                if (!modalElement) {
                    console.error('Evidence modal not found!');
                    alert('Evidence modal not found! Please refresh the page.');
                    return;
                }

                console.log('Modal found, showing...');

                // Show evidence modal
                try {
                    const modal = new bootstrap.Modal(modalElement);
                    modal.show();
                    console.log('Modal shown successfully');

                    // Load evidence data
                    loadStationEvidence(stationId, agentId);
                } catch (error) {
                    console.error('Error showing modal:', error);
                    alert('Error showing modal: ' + error.message);
                }
            }
        });

        function loadStationEvidence(stationId, agentId) {
            const modalContent = document.getElementById('evidenceModalContent');
            modalContent.innerHTML = `
                <div class="text-center py-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2">Loading evidence for station ${stationId}...</p>
                </div>
            `;

            console.log('Making AJAX request to:', `/api/station-evidence/${stationId}`);

            // Make AJAX request to load evidence
            fetch(`/api/station-evidence/${stationId}`)
                .then(response => {
                    console.log('Response status:', response.status);
                    console.log('Response headers:', response.headers);

                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }

                    return response.json();
                })
                .then(data => {
                    console.log('Evidence data received:', data);

                    if (data.success) {
                        displayEvidence(data.evidence, data.station_name);
                    } else {
                        modalContent.innerHTML = `
                            <div class="text-center py-5">
                                <i class="bi bi-exclamation-triangle text-warning" style="font-size: 2rem;"></i>
                                <h5 class="mt-3">Failed to load evidence</h5>
                                <p class="text-muted">${data.message || 'Unknown error'}</p>
                            </div>
                        `;
                    }
                })
                .catch(error => {
                    console.error('Error loading evidence:', error);
                    modalContent.innerHTML = `
                        <div class="text-center py-5">
                            <i class="bi bi-exclamation-triangle text-danger" style="font-size: 2rem;"></i>
                            <h5 class="mt-3">Error loading evidence</h5>
                            <p class="text-muted">${error.message}</p>
                            <button class="btn btn-sm btn-outline-primary mt-2" onclick="loadStationEvidence(${stationId}, ${agentId})">
                                <i class="bi bi-arrow-clockwise me-1"></i>Retry
                            </button>
                        </div>
                    `;
                });
        }

        function displayEvidence(evidence, stationName = '') {
            const modalContent = document.getElementById('evidenceModalContent');
            const modalTitle = document.getElementById('evidenceModalLabel');

            // Update modal title with station name
            if (stationName) {
                modalTitle.innerHTML = `<i class="bi bi-file-earmark-text me-2"></i>Evidence - ${stationName}`;
            }

            if (evidence.length === 0) {
                modalContent.innerHTML = `
                    <div class="text-center py-5">
                        <i class="bi bi-file-earmark text-muted" style="font-size: 3rem;"></i>
                        <h5 class="mt-3">No Evidence Found</h5>
                        <p class="text-muted">No evidence files have been uploaded for this station yet.</p>
                    </div>
                `;
                return;
            }

            let html = `
                <div class="mb-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <h6 class="mb-0">Evidence Files (${evidence.length})</h6>
                        <span class="badge bg-info">${evidence.length} file${evidence.length !== 1 ? 's' : ''}</span>
                    </div>
                </div>
                <div class="row">
            `;

            evidence.forEach((item, index) => {
                const fileExtension = item.file_url.split('.').pop().toLowerCase();
                const isImage = ['jpg', 'jpeg', 'png', 'gif', 'webp'].includes(fileExtension);
                const isPdf = fileExtension === 'pdf';

                html += `
                    <div class="col-md-6 mb-3">
                        <div class="card h-100 border-0 shadow-sm">
                            <div class="card-body">
                                <div class="d-flex align-items-start">
                                    <div class="me-3">
                                        <div class="file-icon d-flex align-items-center justify-content-center rounded"
                                             style="width: 40px; height: 40px; background-color: ${isImage ? '#e3f2fd' : isPdf ? '#fff3e0' : '#f3e5f5'};">
                                            <i class="bi ${isImage ? 'bi-image' : isPdf ? 'bi-file-pdf' : 'bi-file-earmark'}"
                                               style="color: ${isImage ? '#1976d2' : isPdf ? '#f57c00' : '#7b1fa2'}; font-size: 1.2rem;"></i>
                                        </div>
                                    </div>
                                    <div class="flex-grow-1">
                                        <h6 class="card-title mb-1" style="font-size: 0.9rem;">${item.file_name || 'Evidence File'}</h6>
                                        <p class="card-text small text-muted mb-2">
                                            <i class="bi bi-clock me-1"></i>
                                            ${new Date(item.created_at).toLocaleString()}
                                        </p>
                                        <div class="d-flex gap-1">
                                            <a href="/files/${item.file_url}" target="_blank" class="btn btn-sm btn-primary">
                                                <i class="bi bi-eye me-1"></i>View
                                            </a>
                                            <a href="/files/${item.file_url}" download class="btn btn-sm btn-success">
                                                <i class="bi bi-download me-1"></i>Download
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            });
            html += '</div>';

            modalContent.innerHTML = html;
        };

        // Global function for evidence button click
        window.handleEvidenceClick = function(button) {
            console.log('handleEvidenceClick called with:', button);
            const stationId = button.dataset.stationId;
            const agentId = button.dataset.agentId;

            console.log('Loading evidence for station:', stationId, 'agent:', agentId);

            window.loadStationEvidence(stationId, agentId);
        };

        // Also listen for modal show event
        const evidenceModal = document.getElementById('evidenceModal');
        if (evidenceModal) {
            evidenceModal.addEventListener('show.bs.modal', function (event) {
                console.log('Modal show event triggered');
                const button = event.relatedTarget;
                if (button && button.dataset.stationId) {
                    const stationId = button.dataset.stationId;
                    const agentId = button.dataset.agentId;
                    console.log('Loading evidence from modal event for station:', stationId);
                    loadStationEvidence(stationId, agentId);
                }
            });
        }

        // Function to check if coordinates are within Uganda
        function isWithinUganda(lat, lng) {
            // Uganda's approximate bounding box
            const ugandaBounds = {
                minLat: -1.5, // Southern border
                maxLat: 4.3,  // Northern border
                minLng: 29.5, // Western border
                maxLng: 35.0  // Eastern border
            };

            return lat >= ugandaBounds.minLat &&
                   lat <= ugandaBounds.maxLat &&
                   lng >= ugandaBounds.minLng &&
                   lng <= ugandaBounds.maxLng;
        }








        // Clean setup - no complex event listeners needed

    });
</script>

<style>
.bg-gradient-primary {
    background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
}

.form-control:focus, .form-select:focus {
    border-color: #FFD700;
    box-shadow: 0 0 0 0.2rem rgba(255, 215, 0, 0.25);
}

.btn-primary {
    background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
    border: none;
    font-weight: 600;
    color: #333;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #FFC107 0%, #FF8C00 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(255, 215, 0, 0.4);
    color: #333;
}

.btn-warning {
    background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
    border: none;
    color: #333;
    font-weight: 600;
}

.btn-warning:hover {
    background: linear-gradient(135deg, #FFC107 0%, #FF8C00 100%);
    color: #333;
}

.text-primary {
    color: #FF8C00 !important;
}

.text-warning {
    color: #FF8C00 !important;
}

.alert-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

.bg-light {
    background: linear-gradient(135deg, #fffef7 0%, #f8f9fa 100%) !important;
    border-radius: 8px;
}
</style>

@endsection

<!-- Simple Evidence Modal -->
<div class="modal fade" id="evidenceModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Station Evidence</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="evidenceContent">
                <!-- Evidence will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- Add Polling Station Modal -->
@include('polling_stations.partials.add_modal')
