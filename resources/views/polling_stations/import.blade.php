@extends('layouts.app')

@section('styles')
<style>
.import-container {
    max-width: 900px;
    margin: 0 auto;
}

.import-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    color: white;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.import-header h1 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 0;
}



.upload-zone {
    border: 2px dashed #e9ecef;
    border-radius: 12px;
    padding: 2rem 1.5rem;
    text-align: center;
    transition: all 0.3s ease;
    background: #f8f9fa;
    cursor: pointer;
    user-select: none;
}

.upload-zone:hover {
    border-color: #28a745;
    background: #f0fff4;
}

.upload-zone.dragover {
    border-color: #28a745;
    background: #e8f5e8;
    transform: scale(1.02);
}

.upload-icon {
    font-size: 2rem;
    color: #6c757d;
    margin-bottom: 0.5rem;
    transition: all 0.3s ease;
}

.upload-zone:hover .upload-icon {
    color: #28a745;
    transform: scale(1.1);
}

.sample-code {
    background: #2d3748;
    color: #e2e8f0;
    border-radius: 8px;
    padding: 1rem;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.85rem;
    line-height: 1.5;
    overflow-x: auto;
}



.btn-modern {
    border-radius: 8px;
    padding: 0.75rem 2rem;
    font-weight: 500;
    transition: all 0.3s ease;
    border: none;
}

.btn-modern:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.alert-modern {
    border: none;
    border-radius: 10px;
    padding: 1rem 1.5rem;
    margin-bottom: 1.5rem;
}



@media (max-width: 768px) {
    .import-header {
        padding: 1.5rem;
        text-align: center;
    }

    .import-header h1 {
        font-size: 1.5rem;
    }

    .upload-zone {
        padding: 2rem 1rem;
    }

    .feature-card {
        margin-bottom: 1rem;
    }

    .d-flex.justify-content-between {
        flex-direction: column;
        gap: 1rem;
    }

    .d-flex.justify-content-between .btn {
        width: 100%;
    }
}
</style>
@endsection

@section('content')
<div class="container import-container">
    <!-- Compact Header -->
    <div class="import-header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1><i class="bi bi-cloud-upload me-2"></i>Import Stations & Agents</h1>
            </div>
            <a href="{{ route('polling_villages.index') }}" class="btn btn-light btn-modern">
                <i class="bi bi-arrow-left me-2"></i>Back
            </a>
        </div>
    </div>

    <!-- Quick Info -->
    <div class="alert alert-modern alert-info mb-4">
        <div class="d-flex align-items-start">
            <i class="bi bi-info-circle-fill me-3 fs-5"></i>
            <div class="flex-grow-1">
                <strong>Required CSV Columns (exact names):</strong>
                <br><code>name, district, county, subcounty, parish, village, agent_name, agent_phone</code>
                <br><small class="text-muted">Creates stations and agent accounts automatically. Column names must match exactly.</small>
                <br><small class="text-muted"><strong>Phone formats:</strong> +256XXXXXXXXX, 0XXXXXXXXX, 7XXXXXXXX, 7XXXXXXXXX, 8XXXXXXXX, or 9XXXXXXXX</small>
                <br><small class="text-muted"><strong>Auto-generation:</strong> Missing agent_name becomes "Missing name", missing agent_phone gets auto-generated number and password "agent123"</small>
            </div>
            <div class="d-flex gap-2">
                <button class="btn btn-sm btn-outline-success" type="button" id="downloadSampleBtn" onclick="downloadSampleWithFeedback()">
                    <i class="bi bi-download"></i> Download Sample
                </button>
                <button class="btn btn-sm btn-outline-primary" type="button" data-bs-toggle="collapse" data-bs-target="#sampleCollapse">
                    <i class="bi bi-eye"></i> View Sample
                </button>
            </div>
        </div>
        <div class="collapse mt-3" id="sampleCollapse">
            <div class="sample-code">name,district,county,subcounty,parish,village,agent_name,agent_phone
"Kampala Central Station","Kampala","Kampala Central","Central Division","Central Parish","Kampala Village","John Doe","+256700123456"
"Entebbe Airport Station","Wakiso","Entebbe","Entebbe Municipality","Airport Parish","Airport Village","Jane Smith","0700789012"
"Jinja Main Station","Jinja","Jinja","Jinja Central","Main Parish","Central Village","Bob Johnson","700345678"
"Mbarara Town Station","Mbarara","Mbarara","Mbarara Municipality","Town Parish","Central Village","Alice Brown","7545943075"
"Gulu Central Station","Gulu","Gulu","Gulu Municipality","Central Parish","Town Village","David Wilson",""
"Arua Main Station","Arua","Arua","Arua Municipality","Central Parish","Main Village","","256700567890"</div>
            <small class="text-muted mt-2 d-block">
                <strong>Phone formats accepted:</strong> +256XXXXXXXXX, 0XXXXXXXXX, 7XXXXXXXX, 7XXXXXXXXX, 8XXXXXXXX, 9XXXXXXXX<br>
                <strong>Missing data:</strong> Empty agent_name becomes "Missing name", empty agent_phone gets auto-generated number and password "agent123"
            </small>
        </div>
    </div>

    <!-- Import Form -->
    <div class="card feature-card">
        <div class="card-body p-4">
            @if(session('error'))
                <div class="alert alert-modern alert-danger alert-dismissible fade show" role="alert">
                    <div class="d-flex align-items-center">
                        <i class="bi bi-exclamation-triangle-fill me-3 fs-4"></i>
                        <div>
                            <strong>Import Error</strong>
                            <p class="mb-0">{{ session('error') }}</p>
                        </div>
                    </div>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            <form action="{{ route('polling_villages.import.process') }}" method="POST" enctype="multipart/form-data" id="importForm">
                @csrf
                <div class="upload-zone" id="dropZone">
                    <input type="file" class="@error('import_file') is-invalid @enderror" id="import_file" name="import_file" accept=".csv,.txt" style="position: absolute; left: -9999px; opacity: 0;">
                    <div class="upload-icon">
                        <i class="bi bi-cloud-arrow-up"></i>
                    </div>
                    <h6 class="mb-2">Drop CSV file here or</h6>
                    <label for="import_file" class="btn btn-success btn-modern" style="margin: 0; cursor: pointer;">
                        <i class="bi bi-file-earmark-spreadsheet me-2"></i>Choose File
                    </label>
                    <small class="text-muted d-block mt-2">Max: 10MB</small>

                    @error('import_file')
                        <div class="alert alert-danger mt-3 mb-0">{{ $message }}</div>
                    @enderror

                    <div id="fileInfo" class="mt-3 d-none">
                        <div class="alert alert-success mb-0 py-2">
                            <div class="d-flex align-items-center">
                                <i class="bi bi-file-earmark-check me-2"></i>
                                <span class="flex-grow-1" id="fileName">filename.csv</span>
                                <button type="button" class="btn btn-sm btn-outline-danger" id="removeFile">
                                    <i class="bi bi-x"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="d-flex justify-content-between mt-4">
                    <a href="{{ route('polling_villages.index') }}" class="btn btn-outline-secondary btn-modern">
                        <i class="bi bi-x-circle me-2"></i>Cancel
                    </a>
                    <button type="submit" class="btn btn-success btn-modern">
                        <i class="bi bi-upload me-2"></i>Import Stations & Agents
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

@endsection

@section('scripts')
<script>
    // Global function for file dialog - attach to window object
    window.openFileDialog = function() {
        const fileInput = document.getElementById('import_file');
        if (fileInput) {
            fileInput.click();
        }
    };

    // Function to download sample CSV
    window.downloadSampleCSV = function() {
        try {
            console.log('Download sample CSV function called');

            const csvContent = `name,district,county,subcounty,parish,village,agent_name,agent_phone
"Kampala Central Station","Kampala","Kampala Central","Central Division","Central Parish","Kampala Village","John Doe","+256700123456"
"Entebbe Airport Station","Wakiso","Entebbe","Entebbe Municipality","Airport Parish","Airport Village","Jane Smith","0700789012"
"Jinja Main Station","Jinja","Jinja","Jinja Central","Main Parish","Central Village","Bob Johnson","700345678"
"Mbarara Town Station","Mbarara","Mbarara","Mbarara Municipality","Town Parish","Central Village","Alice Brown","7545943075"
"Gulu Central Station","Gulu","Gulu","Gulu Municipality","Central Parish","Town Village","David Wilson",""
"Arua Main Station","Arua","Arua","Arua Municipality","Central Parish","Main Village","","256700567890"
"Lira Central Station","Lira","Lira","Lira Municipality","Central Parish","Town Village","",""`;

            // Try modern approach first
            if (window.Blob && window.URL && window.URL.createObjectURL) {
                const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
                const link = document.createElement('a');
                const url = URL.createObjectURL(blob);
                link.setAttribute('href', url);
                link.setAttribute('download', 'polling_stations_sample.csv');
                link.style.visibility = 'hidden';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                // Clean up the URL object
                setTimeout(() => URL.revokeObjectURL(url), 100);

                console.log('CSV download initiated successfully');
            } else {
                // Fallback for older browsers
                const dataUri = 'data:text/csv;charset=utf-8,' + encodeURIComponent(csvContent);
                const link = document.createElement('a');
                link.setAttribute('href', dataUri);
                link.setAttribute('download', 'polling_stations_sample.csv');
                link.style.visibility = 'hidden';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                console.log('CSV download initiated using fallback method');
            }

        } catch (error) {
            console.error('Error downloading CSV:', error);
            alert('Error downloading sample CSV. Please try again or contact support.');
        }
    };

    // Function with visual feedback
    window.downloadSampleWithFeedback = function() {
        const button = document.getElementById('downloadSampleBtn');
        const originalContent = button.innerHTML;

        // Show loading state
        button.innerHTML = '<i class="bi bi-hourglass-split"></i> Downloading...';
        button.disabled = true;

        // Call the download function
        downloadSampleCSV();

        // Show success state
        setTimeout(() => {
            button.innerHTML = '<i class="bi bi-check-circle"></i> Downloaded!';
            button.className = 'btn btn-sm btn-success';
        }, 100);

        // Reset button after 2 seconds
        setTimeout(() => {
            button.innerHTML = originalContent;
            button.className = 'btn btn-sm btn-outline-success';
            button.disabled = false;
        }, 2000);
    };

    document.addEventListener('DOMContentLoaded', function() {
        const dropZone = document.getElementById('dropZone');
        const fileInput = document.getElementById('import_file');
        const browseButton = document.getElementById('browseButton');
        const fileInfo = document.getElementById('fileInfo');
        const fileName = document.getElementById('fileName');
        const removeFile = document.getElementById('removeFile');
        const importForm = document.getElementById('importForm');

        // Note: Using label for="import_file" approach for reliable file input triggering
        // The label automatically handles the click to open file dialog

        // Also make the entire upload zone clickable (except for the button)
        if (dropZone) {
            dropZone.addEventListener('click', function(e) {
                // Don't trigger if clicking on the browse button itself
                if (!e.target.closest('#browseButton')) {
                    openFileDialog();
                }
            });
        }

        // Handle file selection
        fileInput.addEventListener('change', function() {
            if (fileInput.files.length > 0) {
                showFileInfo(fileInput.files[0].name);
            }
        });

        // Handle drag and drop
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            dropZone.addEventListener(eventName, preventDefaults, false);
        });

        function preventDefaults(e) {
            e.preventDefault();
            e.stopPropagation();
        }

        ['dragenter', 'dragover'].forEach(eventName => {
            dropZone.addEventListener(eventName, highlight, false);
        });

        ['dragleave', 'drop'].forEach(eventName => {
            dropZone.addEventListener(eventName, unhighlight, false);
        });

        function highlight() {
            dropZone.classList.add('dragover');
        }

        function unhighlight() {
            dropZone.classList.remove('dragover');
        }

        dropZone.addEventListener('drop', handleDrop, false);

        function handleDrop(e) {
            const dt = e.dataTransfer;
            const files = dt.files;

            if (files.length > 0) {
                fileInput.files = files;
                showFileInfo(files[0].name);
            }
        }

        function showFileInfo(name) {
            fileName.textContent = name;
            fileInfo.classList.remove('d-none');
        }

        // Remove selected file
        removeFile.addEventListener('click', function() {
            fileInput.value = '';
            fileInfo.classList.add('d-none');
        });

        // Handle form submission
        importForm.addEventListener('submit', function(e) {
            // Check if file is selected
            if (!fileInput.files.length) {
                e.preventDefault();

                // Remove any existing error alerts
                const existingAlert = document.querySelector('.import-error-alert');
                if (existingAlert) {
                    existingAlert.remove();
                }

                // Create and show error message
                const errorDiv = document.createElement('div');
                errorDiv.className = 'alert alert-danger alert-dismissible fade show import-error-alert';
                errorDiv.innerHTML = `
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    <strong>Error:</strong> Please select a CSV file to import.
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                `;

                // Insert error before the form
                importForm.parentNode.insertBefore(errorDiv, importForm);

                // Scroll to error and highlight upload zone
                errorDiv.scrollIntoView({ behavior: 'smooth', block: 'center' });
                dropZone.style.borderColor = '#dc3545';
                dropZone.style.backgroundColor = '#f8d7da';

                // Reset upload zone styling after 3 seconds
                setTimeout(function() {
                    dropZone.style.borderColor = '';
                    dropZone.style.backgroundColor = '';
                }, 3000);

                return false;
            }

            // Validate file type
            const fileName = fileInput.files[0].name.toLowerCase();
            if (!fileName.endsWith('.csv') && !fileName.endsWith('.txt')) {
                e.preventDefault();

                const existingAlert = document.querySelector('.import-error-alert');
                if (existingAlert) {
                    existingAlert.remove();
                }

                const errorDiv = document.createElement('div');
                errorDiv.className = 'alert alert-danger alert-dismissible fade show import-error-alert';
                errorDiv.innerHTML = `
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    <strong>Error:</strong> Please select a valid CSV file (.csv or .txt extension).
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                `;

                importForm.parentNode.insertBefore(errorDiv, importForm);
                errorDiv.scrollIntoView({ behavior: 'smooth', block: 'center' });

                return false;
            }

            // Remove any existing alerts
            const existingAlert = document.querySelector('.import-error-alert');
            if (existingAlert) {
                existingAlert.remove();
            }

            // Show loading state
            const submitButton = importForm.querySelector('button[type="submit"]');
            const originalText = submitButton.innerHTML;
            submitButton.disabled = true;
            submitButton.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>Processing Import...';

            // Show processing message with progress indicator
            const processingDiv = document.createElement('div');
            processingDiv.className = 'alert alert-info import-processing-alert';
            processingDiv.innerHTML = `
                <div class="d-flex align-items-center">
                    <div class="spinner-border spinner-border-sm me-3" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <div>
                        <strong>Processing Import:</strong> Please wait while we import your data. This may take several minutes for large files...
                        <br><small class="text-muted">Do not close this page or navigate away during import.</small>
                    </div>
                </div>
            `;
            importForm.parentNode.insertBefore(processingDiv, importForm);

            // Re-enable button after 5 minutes as fallback (for large imports)
            setTimeout(function() {
                submitButton.disabled = false;
                submitButton.innerHTML = originalText;
                const processingAlert = document.querySelector('.import-processing-alert');
                if (processingAlert) {
                    processingAlert.remove();
                }
            }, 300000); // 5 minutes
        });
    });
</script>
@endsection
