<!-- Add Polling Station Modal -->
<div class="modal fade" id="add_pollingstation" tabindex="-1" aria-labelledby="addPollingStationModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header bg-gradient-primary text-white">
                <h5 class="modal-title fw-bold" id="addPollingStationModalLabel">
                    <i class="bi bi-plus-circle me-2"></i>Add New Polling Station
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form action="{{ route('polling_stations.store') }}" method="post" id="pollingStationModalForm">
                    @csrf

                    <!-- Station Name -->
                    <div class="mb-3">
                        <label for="modal_name" class="form-label fw-bold text-primary">
                            <i class="bi bi-building me-1"></i>Station Name <span class="text-danger">*</span>
                        </label>
                        <input type="text" name="name" id="modal_name"
                               class="form-control"
                               placeholder="Enter polling station name"
                               required>
                        <div class="invalid-feedback"></div>
                    </div>

                    <!-- Administrative Location -->
                    <div class="card mb-3 border-0 bg-light">
                        <div class="card-body p-3">
                            <h6 class="text-primary fw-bold mb-3">
                                <i class="bi bi-geo-alt me-2"></i>Location Details
                            </h6>
                            
                            <div class="row g-2">
                                <div class="col-md-6">
                                    <label for="modal_district" class="form-label small fw-bold">District <span class="text-danger">*</span></label>
                                    <select name="district" id="modal_district" class="form-select" required>
                                        <option value="">Select District</option>
                                    </select>
                                    <div class="invalid-feedback"></div>
                                </div>
                                <div class="col-md-6">
                                    <label for="modal_county" class="form-label small fw-bold">County <span class="text-danger">*</span></label>
                                    <select name="county" id="modal_county" class="form-select" required>
                                        <option value="">Select County</option>
                                    </select>
                                    <div class="invalid-feedback"></div>
                                </div>
                            </div>
                            
                            <div class="row g-2 mt-1">
                                <div class="col-md-6">
                                    <label for="modal_subcounty" class="form-label small fw-bold">Subcounty <span class="text-danger">*</span></label>
                                    <select name="subcounty" id="modal_subcounty" class="form-select" required>
                                        <option value="">Select Subcounty</option>
                                    </select>
                                    <div class="invalid-feedback"></div>
                                </div>
                                <div class="col-md-6">
                                    <label for="modal_parish" class="form-label small fw-bold">Parish <span class="text-danger">*</span></label>
                                    <select name="parish" id="modal_parish" class="form-select" required>
                                        <option value="">Select Parish</option>
                                    </select>
                                    <div class="invalid-feedback"></div>
                                </div>
                            </div>
                            
                            <div class="mt-2">
                                <label for="modal_village" class="form-label small fw-bold">Village <span class="text-danger">*</span></label>
                                <input type="text" name="village" id="modal_village" 
                                       class="form-control" 
                                       placeholder="Enter village name" required>
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Coordinates Section -->
                    <div class="card mb-3 border-0 bg-light">
                        <div class="card-body p-3">
                            <h6 class="text-primary fw-bold mb-2">
                                <i class="bi bi-geo me-2"></i>Coordinates (Optional)
                            </h6>
                            <p class="text-muted small mb-3">Add GPS coordinates to help locate this polling station on the map</p>
                            
                            <!-- Address Search -->
                            <div class="mb-3">
                                <label for="modal_address_search" class="form-label small">Find coordinates by address</label>
                                <div class="input-group">
                                    <input type="text" id="modal_address_search" class="form-control" 
                                           placeholder="Enter address to search coordinates">
                                    <button type="button" class="btn btn-outline-secondary" id="modal_search_coordinates">
                                        <i class="bi bi-search"></i> Search
                                    </button>
                                </div>
                            </div>

                            <!-- Manual Input -->
                            <div class="row g-2">
                                <div class="col-6">
                                    <label for="modal_latitude" class="form-label small">Latitude</label>
                                    <input type="number" step="any" id="modal_latitude" name="latitude"
                                           class="form-control form-control-sm"
                                           placeholder="0.3476">
                                    <div class="invalid-feedback"></div>
                                </div>
                                <div class="col-6">
                                    <label for="modal_longitude" class="form-label small">Longitude</label>
                                    <input type="number" step="any" id="modal_longitude" name="longitude"
                                           class="form-control form-control-sm"
                                           placeholder="32.5825">
                                    <div class="invalid-feedback"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="alert alert-danger d-none" id="modal_error_alert">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        <span id="modal_error_message"></span>
                    </div>

                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
                    <i class="bi bi-x-lg me-1"></i>Cancel
                </button>
                <button type="submit" form="pollingStationModalForm" class="btn btn-primary">
                    <i class="bi bi-check-lg me-1"></i>Create Station
                </button>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Uganda location data (same as in create form)
    const ugandaData = {
        "Oyam": {
            "Aber": {
                "Aber": ["Aber TC", "Acaba", "Adwari", "Agweng", "Akidi", "Akoromit", "Aleka", "Alito", "Amugu", "Anyara", "Aputi", "Atanga", "Awere", "Bala", "Gulu", "Kidi", "Laminogulu", "Lapul", "Loro", "Lukwor", "Ngai", "Ogwette", "Okwang", "Ongako", "Otwal", "Owalo", "Paicho", "Palenga", "Patongo", "Puranga", "Teboke", "Wii"],
                "Acet": ["Acet", "Adwong", "Agwata", "Akworo", "Alango", "Alito", "Amugu", "Anyara", "Aputi", "Atanga", "Awere", "Bala", "Gulu", "Kidi", "Laminogulu", "Lapul", "Loro", "Lukwor", "Ngai", "Ogwette", "Okwang", "Ongako", "Otwal", "Owalo", "Paicho", "Palenga", "Patongo", "Puranga", "Teboke", "Wii"]
            }
        },
        "Bulambuli": {
            "Bulambuli County": {
                "Bulambuli": ["Bulambuli Central", "Bulambuli East", "Bulambuli West"],
                "Bulegeni": ["Bulegeni Central", "Bulegeni North", "Bulegeni South"],
                "Buwalasi": ["Buwalasi Central", "Buwalasi East", "Buwalasi West"],
                "Lwakhakha": ["Lwakhakha Central", "Lwakhakha North", "Lwakhakha South"],
                "Masaba": ["Masaba Central", "Masaba East", "Masaba West"],
                "Nabweya": ["Nabweya Central", "Nabweya North", "Nabweya South"]
            }
        }
        // Add more districts as needed
    };

    // Get modal form elements
    const modalDistrictSelect = document.getElementById('modal_district');
    const modalCountySelect = document.getElementById('modal_county');
    const modalSubcountySelect = document.getElementById('modal_subcounty');
    const modalParishSelect = document.getElementById('modal_parish');

    // Populate districts in modal
    function populateModalDistricts() {
        Object.keys(ugandaData).forEach(district => {
            const option = document.createElement('option');
            option.value = district;
            option.textContent = district;
            if (district === 'Oyam') {
                option.selected = true;
            }
            modalDistrictSelect.appendChild(option);
        });
        
        if (modalDistrictSelect.value === 'Oyam') {
            populateModalCounties('Oyam');
        }
    }

    function populateModalCounties(district) {
        modalCountySelect.innerHTML = '<option value="">Select County</option>';
        modalSubcountySelect.innerHTML = '<option value="">Select Subcounty</option>';
        modalParishSelect.innerHTML = '<option value="">Select Parish</option>';
        
        if (ugandaData[district]) {
            Object.keys(ugandaData[district]).forEach(county => {
                const option = document.createElement('option');
                option.value = county;
                option.textContent = county;
                modalCountySelect.appendChild(option);
            });
        }
    }

    function populateModalSubcounties(district, county) {
        modalSubcountySelect.innerHTML = '<option value="">Select Subcounty</option>';
        modalParishSelect.innerHTML = '<option value="">Select Parish</option>';
        
        if (ugandaData[district] && ugandaData[district][county]) {
            Object.keys(ugandaData[district][county]).forEach(subcounty => {
                const option = document.createElement('option');
                option.value = subcounty;
                option.textContent = subcounty;
                modalSubcountySelect.appendChild(option);
            });
        }
    }

    function populateModalParishes(district, county, subcounty) {
        modalParishSelect.innerHTML = '<option value="">Select Parish</option>';
        
        if (ugandaData[district] && ugandaData[district][county] && ugandaData[district][county][subcounty]) {
            ugandaData[district][county][subcounty].forEach(parish => {
                const option = document.createElement('option');
                option.value = parish;
                option.textContent = parish;
                modalParishSelect.appendChild(option);
            });
        }
    }

    // Event listeners for modal dropdowns
    modalDistrictSelect.addEventListener('change', function() {
        populateModalCounties(this.value);
    });

    modalCountySelect.addEventListener('change', function() {
        populateModalSubcounties(modalDistrictSelect.value, this.value);
    });

    modalSubcountySelect.addEventListener('change', function() {
        populateModalParishes(modalDistrictSelect.value, modalCountySelect.value, this.value);
    });

    // Initialize modal dropdowns when modal is shown
    document.getElementById('add_pollingstation').addEventListener('shown.bs.modal', function() {
        if (modalDistrictSelect.children.length <= 1) {
            populateModalDistricts();
        }
    });

    // Form submission handling
    document.getElementById('pollingStationModalForm').addEventListener('submit', function(e) {
        e.preventDefault();
        
        const formData = new FormData(this);
        const submitBtn = document.querySelector('button[form="pollingStationModalForm"]');
        const originalText = submitBtn.innerHTML;
        
        // Show loading state
        submitBtn.innerHTML = '<i class="spinner-border spinner-border-sm me-1"></i>Creating...';
        submitBtn.disabled = true;
        
        // Clear previous errors
        document.querySelectorAll('.is-invalid').forEach(el => el.classList.remove('is-invalid'));
        document.getElementById('modal_error_alert').classList.add('d-none');
        
        fetch(this.action, {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Close modal and reload page
                bootstrap.Modal.getInstance(document.getElementById('add_pollingstation')).hide();
                location.reload();
            } else {
                // Handle validation errors
                if (data.errors) {
                    Object.keys(data.errors).forEach(field => {
                        const input = document.getElementById('modal_' + field);
                        if (input) {
                            input.classList.add('is-invalid');
                            const feedback = input.nextElementSibling;
                            if (feedback && feedback.classList.contains('invalid-feedback')) {
                                feedback.textContent = data.errors[field][0];
                            }
                        }
                    });
                } else if (data.message) {
                    document.getElementById('modal_error_message').textContent = data.message;
                    document.getElementById('modal_error_alert').classList.remove('d-none');
                }
            }
        })
        .catch(error => {
            console.error('Error:', error);
            document.getElementById('modal_error_message').textContent = 'An error occurred. Please try again.';
            document.getElementById('modal_error_alert').classList.remove('d-none');
        })
        .finally(() => {
            // Restore button state
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        });
    });

    // Reset form when modal is hidden
    document.getElementById('add_pollingstation').addEventListener('hidden.bs.modal', function() {
        document.getElementById('pollingStationModalForm').reset();
        document.querySelectorAll('.is-invalid').forEach(el => el.classList.remove('is-invalid'));
        document.getElementById('modal_error_alert').classList.add('d-none');
    });
});
</script>
