@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">


            <!-- Compact Header -->
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h1 class="h4 mb-0">
                    <i class="bi bi-geo-alt-fill text-warning me-2"></i>{{ $title }}
                </h1>
                <div class="btn-group btn-group-sm">
                    <a href="{{ route('polling_villages.import') }}" class="btn btn-success">
                        <i class="bi bi-upload"></i> Import
                    </a>
                    <a href="{{ route('polling_stations.index') }}" class="btn btn-outline-secondary">
                        <i class="bi bi-list-ul"></i> Stations
                    </a>
                    <a href="{{ route('polling_stations.view') }}" class="btn btn-outline-secondary">
                        <i class="bi bi-grid-3x3-gap"></i> Grid
                    </a>
                </div>
            </div>

            <!-- Compact Statistics -->
            <div class="row mb-3 g-3">
                <!-- Total Stations Card -->
                <div class="col-lg-3 col-md-6">
                    <div class="card border-0 shadow-sm">
                        <div class="card-body p-3">
                            <div class="d-flex align-items-center">
                                <div class="me-3">
                                    <i class="bi bi-building text-primary" style="font-size: 1.5rem;"></i>
                                </div>
                                <div class="flex-grow-1">
                                    <div class="text-muted small">Total Stations</div>
                                    <div class="h5 mb-0 fw-bold">{{ number_format($totalStations) }}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6">
                    <div class="card border-0 shadow-sm">
                        <div class="card-body p-3">
                            <div class="d-flex align-items-center">
                                <div class="me-3">
                                    <i class="bi bi-geo-alt text-success" style="font-size: 1.5rem;"></i>
                                </div>
                                <div class="flex-grow-1">
                                    @php
                                        $coordPercentage = $totalStations > 0 ? round(($stationsWithCoordinates / $totalStations) * 100) : 0;
                                    @endphp
                                    <div class="text-muted small">With Coordinates</div>
                                    <div class="h5 mb-0 fw-bold">{{ number_format($stationsWithCoordinates) }} <small class="text-muted">({{ $coordPercentage }}%)</small></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6">
                    <div class="card border-0 shadow-sm">
                        <div class="card-body p-3">
                            <div class="d-flex align-items-center">
                                <div class="me-3">
                                    <i class="bi bi-people text-info" style="font-size: 1.5rem;"></i>
                                </div>
                                <div class="flex-grow-1">
                                    @php
                                        $agentPercentage = $totalStations > 0 ? round(($stationsWithAgents / $totalStations) * 100) : 0;
                                    @endphp
                                    <div class="text-muted small">With Agents</div>
                                    <div class="h5 mb-0 fw-bold">{{ number_format($stationsWithAgents) }} <small class="text-muted">({{ $agentPercentage }}%)</small></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6">
                    <div class="card border-0 shadow-sm">
                        <div class="card-body p-3">
                            <div class="d-flex align-items-center">
                                <div class="me-3">
                                    <i class="bi bi-file-earmark-text text-warning" style="font-size: 1.5rem;"></i>
                                </div>
                                <div class="flex-grow-1">
                                    @php
                                        $evidencePercentage = $stationsWithAgents > 0 ? round(($stationsWithEvidence / $stationsWithAgents) * 100) : 0;
                                    @endphp
                                    <div class="text-muted small">With Evidence</div>
                                    <div class="h5 mb-0 fw-bold">{{ number_format($stationsWithEvidence) }} <small class="text-muted">({{ $evidencePercentage }}%)</small></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Administrative Area Count Cards -->
            <div class="row mb-3 g-3">
                <!-- Subcounties Card -->
                <div class="col-lg-4 col-md-6">
                    <div class="card border-0 shadow-sm">
                        <div class="card-body p-3">
                            <div class="d-flex align-items-center">
                                <div class="me-3">
                                    <i class="bi bi-building-fill text-info" style="font-size: 1.5rem;"></i>
                                </div>
                                <div class="flex-grow-1">
                                    <div class="text-muted small">Subcounties</div>
                                    <div class="h5 mb-0 fw-bold">{{ number_format($totalSubcounties) }}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Parishes Card -->
                <div class="col-lg-4 col-md-6">
                    <div class="card border-0 shadow-sm">
                        <div class="card-body p-3">
                            <div class="d-flex align-items-center">
                                <div class="me-3">
                                    <i class="bi bi-geo-alt-fill text-secondary" style="font-size: 1.5rem;"></i>
                                </div>
                                <div class="flex-grow-1">
                                    <div class="text-muted small">Parishes</div>
                                    <div class="h5 mb-0 fw-bold">{{ number_format($totalParishes) }}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Villages Card -->
                <div class="col-lg-4 col-md-6">
                    <div class="card border-0 shadow-sm">
                        <div class="card-body p-3">
                            <div class="d-flex align-items-center">
                                <div class="me-3">
                                    <i class="bi bi-house-fill text-success" style="font-size: 1.5rem;"></i>
                                </div>
                                <div class="flex-grow-1">
                                    <div class="text-muted small">Villages</div>
                                    <div class="h5 mb-0 fw-bold">{{ number_format($totalVillages) }}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Compact Filters -->
            <div class="card border-0 shadow-sm mb-3">
                <div class="card-body p-3">
                    <form method="GET" action="{{ route('polling_villages.index') }}" id="filtersForm">
                        <div class="row g-2">
                            <div class="col-md-4">
                                <div class="input-group input-group-sm">
                                    <span class="input-group-text">
                                        <i class="bi bi-search"></i>
                                    </span>
                                    <input type="text" name="search" class="form-control"
                                           placeholder="Search stations, villages..."
                                           value="{{ $currentFilters['search'] ?? '' }}"
                                           id="realtimeSearch">
                                    <span class="input-group-text" id="searchStatus"></span>
                                </div>
                            </div>

                            <div class="col-md-2">
                                <select name="subcounty" class="form-select form-select-sm">
                                    <option value="">All Subcounties</option>
                                    @foreach($subcounties as $subcounty)
                                        <option value="{{ $subcounty }}" {{ ($currentFilters['subcounty'] ?? '') == $subcounty ? 'selected' : '' }}>
                                            {{ $subcounty }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>

                            <div class="col-md-2">
                                <select name="parish" class="form-select form-select-sm">
                                    <option value="">All Parishes</option>
                                    @foreach($parishes as $parish)
                                        <option value="{{ $parish }}" {{ ($currentFilters['parish'] ?? '') == $parish ? 'selected' : '' }}>
                                            {{ $parish }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>

                            <div class="col-md-2">
                                <select name="village" class="form-select form-select-sm">
                                    <option value="">All Villages</option>
                                    @foreach($villages as $village)
                                        <option value="{{ $village }}" {{ ($currentFilters['village'] ?? '') == $village ? 'selected' : '' }}>
                                            {{ $village }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>

                            <div class="col-md-2">
                                <div class="btn-group btn-group-sm w-100">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="bi bi-funnel"></i> Filter
                                    </button>
                                    <a href="{{ route('polling_villages.index') }}" class="btn btn-outline-secondary">
                                        <i class="bi bi-arrow-clockwise"></i>
                                    </a>
                                </div>
                            </div>
                        </div>



                        <div class="row mt-3">
                            <div class="col-12">
                                <button type="submit" class="btn btn-primary btn-sm">
                                    <i class="bi bi-search me-1"></i>Apply Filters
                                </button>
                                <a href="{{ route('polling_villages.index') }}" class="btn btn-outline-secondary btn-sm ms-2">
                                    <i class="bi bi-arrow-clockwise me-1"></i>Clear All
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Results Table -->
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-light py-2 d-flex justify-content-between align-items-center">
                    <h6 class="mb-0 small">
                        <i class="bi bi-table me-1"></i>{{ $polling_stations->total() }} Stations
                    </h6>
                    <small class="text-muted">
                        {{ $polling_stations->firstItem() ?? 0 }}-{{ $polling_stations->lastItem() ?? 0 }} of {{ $polling_stations->total() }}
                    </small>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover table-sm mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th class="border-0">
                                        <a href="{{ request()->fullUrlWithQuery(['sort_by' => 'subcounty', 'sort_order' => request('sort_order') === 'asc' ? 'desc' : 'asc']) }}"
                                           class="text-decoration-none text-dark">
                                            Administrative Area
                                            @if(request('sort_by') === 'subcounty')
                                                <i class="bi bi-arrow-{{ request('sort_order') === 'asc' ? 'up' : 'down' }}"></i>
                                            @endif
                                        </a>
                                        <small class="text-muted d-block" style="font-weight: normal;">Subcounty, County, District</small>
                                    </th>
                                    <th class="border-0">
                                        <a href="{{ request()->fullUrlWithQuery(['sort_by' => 'parish', 'sort_order' => request('sort_order') === 'asc' ? 'desc' : 'asc']) }}"
                                           class="text-decoration-none text-dark">
                                            Parish
                                            @if(request('sort_by') === 'parish')
                                                <i class="bi bi-arrow-{{ request('sort_order') === 'asc' ? 'up' : 'down' }}"></i>
                                            @endif
                                        </a>
                                    </th>
                                    <th class="border-0">
                                        <a href="{{ request()->fullUrlWithQuery(['sort_by' => 'village', 'sort_order' => request('sort_order') === 'asc' ? 'desc' : 'asc']) }}"
                                           class="text-decoration-none text-dark">
                                            Village
                                            @if(request('sort_by') === 'village')
                                                <i class="bi bi-arrow-{{ request('sort_order') === 'asc' ? 'up' : 'down' }}"></i>
                                            @endif
                                        </a>
                                    </th>
                                    <th class="border-0">
                                        <a href="{{ request()->fullUrlWithQuery(['sort_by' => 'name', 'sort_order' => request('sort_order') === 'asc' ? 'desc' : 'asc']) }}"
                                           class="text-decoration-none text-dark">
                                            Polling Station
                                            @if(request('sort_by') === 'name')
                                                <i class="bi bi-arrow-{{ request('sort_order') === 'asc' ? 'up' : 'down' }}"></i>
                                            @endif
                                        </a>
                                    </th>
                                    <th class="border-0">Agent Name</th>
                                    <th class="border-0">Agent Contact</th>
                                    <th class="border-0 text-center">Evidence</th>
                                    <th class="border-0 text-center">Vote Form</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($polling_stations as $polling_station)
                                    @php
                                        // Get agent information
                                        $agent = $polling_station->agents->first();

                                        // Get latest evidence count (latest 2 as per user preference)
                                        $evidenceCount = 0;
                                        if ($agent && $agent->eveidences) {
                                            $evidenceCount = $agent->eveidences()
                                                ->latest('created_at')
                                                ->limit(2)
                                                ->count();
                                        }
                                    @endphp
                                    <tr>
                                        <td>
                                            <div class="administrative-hierarchy">
                                                <div class="fw-bold text-primary mb-1" style="font-size: 1rem;">
                                                    <i class="bi bi-building me-1"></i>{{ $polling_station->subcounty ?: 'N/A' }}
                                                </div>
                                                <div class="text-muted small">
                                                    <div><i class="bi bi-geo-alt me-1"></i>{{ $polling_station->county ?: 'N/A' }}</div>
                                                    <div class="text-secondary" style="font-size: 0.8rem;">
                                                        <i class="bi bi-map me-1"></i>{{ $polling_station->district ?: 'N/A' }}
                                                    </div>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="text-muted">{{ $polling_station->parish ?: 'N/A' }}</span>
                                        </td>
                                        <td>
                                            <span class="fw-medium text-primary">{{ $polling_station->village ?: 'N/A' }}</span>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div>
                                                    <div class="fw-medium">{{ $polling_station->name }}</div>
                                                    @if($polling_station->latitude && $polling_station->longitude)
                                                        <small class="text-success">
                                                            <i class="bi bi-geo-alt-fill me-1"></i>
                                                            {{ number_format($polling_station->latitude, 4) }}, {{ number_format($polling_station->longitude, 4) }}
                                                        </small>
                                                    @else
                                                        <small class="text-muted">
                                                            <i class="bi bi-geo-alt me-1"></i>No coordinates
                                                        </small>
                                                    @endif
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            @if($agent)
                                                <div class="d-flex align-items-center">
                                                    <div class="avatar-sm bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-2">
                                                        {{ strtoupper(substr($agent->user->name, 0, 1)) }}
                                                    </div>
                                                    <div>
                                                        <div class="fw-medium">{{ $agent->user->name }}</div>
                                                        <small class="text-muted">Agent</small>
                                                    </div>
                                                </div>
                                            @else
                                                <span class="text-muted small">No agent assigned</span>
                                            @endif
                                        </td>
                                        <td>
                                            @if($agent)
                                                <div class="small">
                                                    <i class="bi bi-telephone me-1"></i>
                                                    {{ $agent->user->phone_number }}
                                                </div>
                                            @else
                                                <span class="text-muted small">--</span>
                                            @endif
                                        </td>
                                        <td class="text-center">
                                            @if($agent && $evidenceCount > 0)
                                                <div class="d-flex flex-column align-items-center">
                                                    <span class="badge bg-info mb-1">{{ $evidenceCount }}</span>
                                                    <button type="button" class="btn btn-xs btn-outline-primary"
                                                            onclick="showEvidenceModal({{ $polling_station->id }}, {{ $agent->id }})"
                                                            title="View Evidence">
                                                        <i class="bi bi-eye"></i>
                                                    </button>
                                                </div>
                                            @else
                                                <span class="text-muted small">--</span>
                                            @endif
                                        </td>
                                        <td class="text-center">
                                            @if($agent)
                                                <a href="/station/{{ $polling_station->id }}/vote-form"
                                                   target="_blank"
                                                   class="btn btn-sm btn-success"
                                                   title="Open Vote Form for {{ $polling_station->name }}">
                                                    <i class="bi bi-ballot-check me-1"></i>
                                                    Vote Form
                                                </a>
                                            @else
                                                <span class="text-muted small">
                                                    <i class="bi bi-person-x"></i>
                                                    No Agent
                                                </span>
                                            @endif
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="8" class="text-center py-4">
                                            <div class="text-muted">
                                                <i class="bi bi-inbox" style="font-size: 2rem;"></i>
                                                <p class="mt-2">No polling stations found matching your criteria.</p>
                                                <a href="{{ route('polling_villages.index') }}" class="btn btn-outline-primary btn-sm">
                                                    <i class="bi bi-arrow-clockwise me-1"></i>Clear Filters
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Pagination -->
                @if($polling_stations->hasPages())
                    <div class="card-footer bg-light">
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="text-muted small">
                                Showing {{ $polling_stations->firstItem() ?? 0 }} to {{ $polling_stations->lastItem() ?? 0 }}
                                of {{ $polling_stations->total() }} results
                            </div>
                            <div>
                                {{ $polling_stations->appends(request()->query())->links() }}
                            </div>
                        </div>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>

<!-- Evidence Modal -->
<div class="modal fade" id="evidenceModal" tabindex="-1" aria-labelledby="evidenceModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="evidenceModalLabel">
                    <i class="bi bi-file-earmark-text me-2"></i>Station Evidence
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="evidenceContent">
                <!-- Evidence will be loaded here dynamically -->
                <div class="text-center py-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2">Loading evidence...</p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>


@endsection

@push('scripts')
<script>
// Evidence modal functions
function showEvidenceModal(stationId, agentId) {
    console.log('showEvidenceModal called with stationId:', stationId, 'agentId:', agentId);

    // Check if modal exists
    const modalElement = document.getElementById('evidenceModal');
    if (!modalElement) {
        console.error('Evidence modal element not found!');
        alert('Evidence modal not found! Please refresh the page.');
        return;
    }

    try {
        // Show modal
        const modal = new bootstrap.Modal(modalElement);
        modal.show();
        console.log('Modal shown successfully');

        // Load evidence
        loadEvidence(stationId);
    } catch (error) {
        console.error('Error in showEvidenceModal:', error);
        alert('Error showing modal: ' + error.message);
    }
}

function loadEvidence(stationId) {
    console.log('loadEvidence called for stationId:', stationId);

    const content = document.getElementById('evidenceContent');
    if (!content) {
        console.error('evidenceContent element not found!');
        return;
    }

    content.innerHTML = '<div class="text-center py-4"><div class="spinner-border"></div><p class="mt-2">Loading...</p></div>';

    console.log('Fetching evidence from:', `/api/station-evidence/${stationId}`);

    fetch(`/api/station-evidence/${stationId}`)
        .then(response => {
            console.log('Response status:', response.status);
            return response.json();
        })
        .then(data => {
            console.log('Evidence data received:', data);
            if (data.success && data.evidence.length > 0) {
                displayEvidence(data.evidence, data.station_name);
            } else {
                content.innerHTML = '<div class="text-center py-4"><p class="text-muted">No evidence found</p></div>';
            }
        })
        .catch(error => {
            console.error('Error loading evidence:', error);
            content.innerHTML = '<div class="text-center py-4"><p class="text-danger">Error loading evidence: ' + error.message + '</p></div>';
        });
}

function displayEvidence(evidence, stationName = '') {
    const content = document.getElementById('evidenceContent');
    const modalTitle = document.getElementById('evidenceModalLabel');

    // Update modal title with station name
    if (stationName) {
        modalTitle.innerHTML = `<i class="bi bi-file-earmark-text me-2"></i>Evidence - ${stationName}`;
    }

    if (evidence.length === 0) {
        content.innerHTML = `
            <div class="text-center py-5">
                <i class="bi bi-file-earmark text-muted" style="font-size: 3rem;"></i>
                <h5 class="mt-3">No Evidence Found</h5>
                <p class="text-muted">No evidence files have been uploaded for this station yet.</p>
            </div>
        `;
        return;
    }

    let html = '<div class="row g-3">';

    evidence.forEach((item, index) => {
        const isImage = item.file_url.match(/\.(jpg|jpeg|png|gif|webp)$/i);
        const fileName = item.file_name || `Evidence ${index + 1}`;

        html += `
            <div class="col-md-6">
                <div class="card border-0 shadow-sm">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-start mb-2">
                            <h6 class="card-title mb-0">${fileName}</h6>
                            <small class="text-muted">${item.created_at}</small>
                        </div>

                        ${isImage ? `
                            <div class="text-center mb-3">
                                <img src="${item.file_url}" alt="${fileName}"
                                     class="img-fluid rounded" style="max-height: 200px; cursor: pointer;"
                                     onclick="window.open('${item.file_url}', '_blank')">
                            </div>
                        ` : `
                            <div class="text-center mb-3 py-4">
                                <i class="bi bi-file-earmark text-muted" style="font-size: 3rem;"></i>
                                <p class="text-muted mt-2">File attachment</p>
                            </div>
                        `}

                        <div class="d-grid">
                            <a href="${item.file_url}" target="_blank" class="btn btn-outline-primary btn-sm">
                                <i class="bi bi-download me-1"></i>View/Download
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        `;
    });

    html += '</div>';
    content.innerHTML = html;
}

// Functions moved to global scope above

// Enhanced card animations and realtime search
document.addEventListener('DOMContentLoaded', function() {
    // Animate statistics cards on page load
    animateStatCards();

    // Add intersection observer for card animations
    setupCardAnimations();
    const filtersForm = document.getElementById('filtersForm');
    const searchInput = document.querySelector('input[name="search"]');
    const tableRows = document.querySelectorAll('tbody tr');
    let filterTimeout;

    // Initialize stations data from DOM for client-side filtering
    let stationsData = [];

    // Extract data from table rows
    tableRows.forEach((row, index) => {
        const cells = row.querySelectorAll('td');
        if (cells.length > 1) { // Skip empty state row
            const adminCell = cells[0];
            const parishCell = cells[1];
            const villageCell = cells[2];
            const stationCell = cells[3];

            // Extract text content for searching
            const adminText = adminCell ? adminCell.textContent.trim().toLowerCase() : '';
            const parishText = parishCell ? parishCell.textContent.trim().toLowerCase() : '';
            const villageText = villageCell ? villageCell.textContent.trim().toLowerCase() : '';
            const stationText = stationCell ? stationCell.textContent.trim().toLowerCase() : '';

            stationsData.push({
                row: row,
                searchText: `${adminText} ${parishText} ${villageText} ${stationText}`,
                adminText: adminText,
                parishText: parishText,
                villageText: villageText,
                stationText: stationText
            });
        }
    });

    function debounceFilter(callback, delay = 300) {
        clearTimeout(filterTimeout);
        filterTimeout = setTimeout(callback, delay);
    }

    // Realtime search function
    function performRealtimeSearch(searchTerm) {
        const term = searchTerm.toLowerCase().trim();
        let visibleCount = 0;
        const searchStatus = document.getElementById('searchStatus');

        // Show searching indicator
        if (searchStatus) {
            searchStatus.innerHTML = '<i class="bi bi-search text-primary"></i>';
        }

        stationsData.forEach(station => {
            const isVisible = term === '' || station.searchText.includes(term);

            if (station.row) {
                station.row.style.display = isVisible ? '' : 'none';
                if (isVisible) visibleCount++;
            }
        });

        // Update visible count display
        updateVisibleCount(visibleCount);

        // Show/hide empty state
        const emptyRow = document.querySelector('tbody tr td[colspan]');
        if (emptyRow && emptyRow.parentElement) {
            emptyRow.parentElement.style.display = visibleCount === 0 && term !== '' ? '' : 'none';
        }

        // Update search status
        if (searchStatus) {
            if (term === '') {
                searchStatus.innerHTML = '';
            } else if (visibleCount === 0) {
                searchStatus.innerHTML = '<i class="bi bi-exclamation-circle text-warning" title="No results found"></i>';
            } else {
                searchStatus.innerHTML = `<i class="bi bi-check-circle text-success" title="${visibleCount} results found"></i>`;
            }
        }

        // Add highlight effect to search input based on results
        if (searchInput) {
            searchInput.classList.remove('border-success', 'border-warning');
            if (term !== '') {
                if (visibleCount > 0) {
                    searchInput.classList.add('border-success');
                } else {
                    searchInput.classList.add('border-warning');
                }
            }
        }
    }

    function updateVisibleCount(count) {
        const totalCount = stationsData.length;
        const countDisplay = document.querySelector('.card-header .badge');
        if (countDisplay) {
            countDisplay.textContent = `${count} stations`;

            // Add visual feedback for filtered results
            countDisplay.classList.remove('bg-info', 'bg-success', 'bg-warning');
            if (count === totalCount) {
                countDisplay.classList.add('bg-info');
            } else if (count > 0) {
                countDisplay.classList.add('bg-success');
            } else {
                countDisplay.classList.add('bg-warning');
            }
        }

        // Update results info
        const resultsInfo = document.querySelector('.card-header .text-muted');
        if (resultsInfo) {
            if (count === totalCount) {
                resultsInfo.textContent = `Showing all ${totalCount} results`;
            } else {
                resultsInfo.textContent = `Showing ${count} of ${totalCount} results (filtered)`;
            }
        }
    }

    // Search input event listener
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            const searchTerm = this.value;

            // Perform realtime search
            debounceFilter(() => {
                performRealtimeSearch(searchTerm);
            }, 200); // Faster debounce for realtime feel
        });

        // Clear search on escape key
        searchInput.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                this.value = '';
                performRealtimeSearch('');
                this.classList.remove('border-success', 'border-warning');
            }
        });
    }

    // Global keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        // Ctrl/Cmd + K to focus search
        if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
            e.preventDefault();
            if (searchInput) {
                searchInput.focus();
                searchInput.select();
            }
        }

        // Forward slash to focus search (like GitHub)
        if (e.key === '/' && !e.ctrlKey && !e.metaKey && !e.altKey) {
            // Only if not typing in an input
            if (document.activeElement.tagName !== 'INPUT' && document.activeElement.tagName !== 'TEXTAREA') {
                e.preventDefault();
                if (searchInput) {
                    searchInput.focus();
                }
            }
        }
    });
    }

    // Handle other filters (still submit form for server-side filtering)
    if (filtersForm) {
        const selectFilters = filtersForm.querySelectorAll('select');

        selectFilters.forEach(select => {
            select.addEventListener('change', function() {
                // For select filters, still use server-side filtering
                filtersForm.submit();
            });
        });
    }

    // Add search enhancement indicators
    if (searchInput) {
        // Add search icon and clear button
        const searchContainer = searchInput.parentElement;
        searchContainer.style.position = 'relative';

        // Add clear button
        const clearBtn = document.createElement('button');
        clearBtn.type = 'button';
        clearBtn.className = 'btn btn-sm btn-outline-secondary position-absolute';
        clearBtn.style.cssText = 'right: 5px; top: 50%; transform: translateY(-50%); z-index: 10; padding: 0.25rem 0.5rem; display: none;';
        clearBtn.innerHTML = '<i class="bi bi-x"></i>';
        clearBtn.title = 'Clear search';

        clearBtn.addEventListener('click', function() {
            searchInput.value = '';
            performRealtimeSearch('');
            this.style.display = 'none';
            searchInput.focus();
        });

        searchContainer.appendChild(clearBtn);

        // Show/hide clear button based on input
        searchInput.addEventListener('input', function() {
            clearBtn.style.display = this.value ? 'block' : 'none';
        });

        // Initial state
        if (searchInput.value) {
            clearBtn.style.display = 'block';
        }
    }

    // Vote form functionality simplified - using direct links
});
</script>
@endpush

@section('styles')
<style>
.avatar-sm {
    width: 32px;
    height: 32px;
    font-size: 0.875rem;
}

.btn-xs {
    padding: 0.125rem 0.25rem;
    font-size: 0.75rem;
    line-height: 1.2;
}

.table th {
    font-weight: 600;
    font-size: 0.875rem;
    color: #495057;
    border-bottom: 2px solid #dee2e6;
}

.table td {
    vertical-align: middle;
    border-bottom: 1px solid #f1f3f4;
}

.table tbody tr:hover {
    background-color: rgba(255, 165, 0, 0.05);
}

.card {
    border-radius: 12px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.form-control:focus, .form-select:focus {
    border-color: #FFD700;
    box-shadow: 0 0 0 0.2rem rgba(255, 215, 0, 0.25);
}

.btn-primary {
    background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
    border: none;
    font-weight: 600;
    color: #333;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #FFC107 0%, #FF8C00 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(255, 215, 0, 0.4);
    color: #333;
}

.text-primary {
    color: #FF8C00 !important;
}

.bg-primary {
    background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%) !important;
}

.administrative-hierarchy {
    line-height: 1.3;
}

.administrative-hierarchy .fw-bold {
    font-size: 1rem;
    margin-bottom: 0.25rem;
}

.administrative-hierarchy .text-muted {
    font-size: 0.85rem;
    line-height: 1.2;
}

.administrative-hierarchy .text-secondary {
    font-size: 0.8rem !important;
    opacity: 0.7;
}

/* Realtime search enhancements */
#realtimeSearch {
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

#realtimeSearch:focus {
    border-color: #FFD700;
    box-shadow: 0 0 0 0.2rem rgba(255, 215, 0, 0.25);
}

#realtimeSearch.border-success {
    border-color: #28a745 !important;
}

#realtimeSearch.border-warning {
    border-color: #ffc107 !important;
}

.badge.bg-success {
    background-color: #28a745 !important;
}

.badge.bg-warning {
    background-color: #ffc107 !important;
    color: #212529 !important;
}

/* Search status animations */
#searchStatus i {
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; transform: scale(0.8); }
    to { opacity: 1; transform: scale(1); }
}

/* Table row fade effect for search */
tbody tr {
    transition: opacity 0.2s ease;
}

tbody tr[style*="display: none"] {
    opacity: 0;
}

/* Live badge pulse animation */
.badge:has-text("LIVE") {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}

/* Compact styling */
.table-sm th, .table-sm td {
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
}

.card-body {
    padding: 1rem;
}

.btn-group-sm .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
}

/* Action buttons styling */
.btn-xs {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    line-height: 1.2;
    border-radius: 4px;
    font-weight: 500;
    min-width: 70px;
    text-align: center;
}

.btn-success.btn-xs {
    background: linear-gradient(135deg, #28a745, #20c997);
    border: none;
    color: white;
    transition: all 0.2s ease;
}

.btn-success.btn-xs:hover {
    background: linear-gradient(135deg, #218838, #1ea085);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
}

.dropdown-menu {
    border: none;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    border-radius: 8px;
    padding: 0.5rem 0;
    min-width: 160px;
}

.dropdown-item {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    transition: all 0.2s ease;
}

.dropdown-item:hover {
    background: linear-gradient(135deg, rgba(255, 215, 0, 0.1), rgba(255, 165, 0, 0.1));
    color: #FF8C00;
}

.dropdown-item i {
    width: 16px;
    text-align: center;
}

/* Action column specific styling */
td:last-child {
    min-width: 120px;
}

.dropdown-toggle::after {
    display: none;
}

/* Keyboard shortcut styling */
kbd {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 3px;
    padding: 0.1rem 0.3rem;
    font-size: 0.7rem;
    color: #495057;
    box-shadow: 0 1px 0 rgba(0,0,0,0.1);
}
</style>
@endsection
