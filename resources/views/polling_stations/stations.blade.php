@extends('layouts.app')

@section('styles')
<style>
/* Enhanced Toast Styles */
.vote-update-toast {
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.vote-update-toast:hover {
    transform: translateX(-5px) !important;
    box-shadow: 0 12px 40px rgba(40, 167, 69, 0.4) !important;
}

/* Evidence Display Styles */
.evidence-container {
    min-width: 180px;
}

.latest-evidence-preview {
    max-height: 100px;
    overflow-y: auto;
}

.evidence-item {
    background: rgba(248, 250, 252, 0.9);
    border: 1px solid rgba(226, 232, 240, 0.8);
}

.evidence-info {
    min-width: 0;
}

.btn-xs {
    padding: 2px 6px;
    font-size: 0.7rem;
    line-height: 1.2;
}

.badge-sm {
    font-size: 0.6rem;
    padding: 1px 4px;
}

.evidence-actions .btn-xs {
    min-width: 24px;
    height: 24px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.evidence-item:hover {
    background: rgba(241, 245, 249, 0.9);
    border-color: rgba(203, 213, 225, 0.8);
}

/* Compact Dashboard Improvements */
.dashboard-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 24px;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.dashboard-title {
    font-size: 1.75rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 4px;
}

.dashboard-subtitle {
    color: #6c757d;
    font-size: 0.95rem;
    margin-bottom: 0;
}

/* Compact Stats Cards */
.quick-stat {
    transition: all 0.3s ease;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.quick-stat:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

/* Live indicator pulse */
.badge.bg-success {
    animation: pulse-green 2s infinite;
}

@keyframes pulse-green {
    0% { box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.7); }
    70% { box-shadow: 0 0 0 10px rgba(40, 167, 69, 0); }
    100% { box-shadow: 0 0 0 0 rgba(40, 167, 69, 0); }
}

/* Compact chart containers */
.chart-container {
    position: relative;
    height: 300px;
    margin: 15px 0;
}

/* Enhanced notification styles */
#vote-toast-container .vote-update-toast {
    font-family: 'Poppins', sans-serif;
}

/* Professional Compact Dashboard Card Styles */
.dashboard-card {
    border-radius: 10px;
    border: none;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    transition: all 0.3s ease;
    overflow: hidden;
}

.dashboard-card:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.dashboard-card .card-header {
    border-radius: 10px 10px 0 0 !important;
    border-bottom: none;
    font-size: 0.9rem;
}

.hover-item:hover {
    background-color: rgba(0,0,0,0.04) !important;
    transform: translateX(1px);
}

/* Custom scrollbar for compact sections */
.card-body::-webkit-scrollbar {
    width: 3px;
}

.card-body::-webkit-scrollbar-track {
    background: rgba(0,0,0,0.03);
    border-radius: 2px;
}

.card-body::-webkit-scrollbar-thumb {
    background: rgba(0,0,0,0.15);
    border-radius: 2px;
}

.card-body::-webkit-scrollbar-thumb:hover {
    background: rgba(0,0,0,0.25);
}

/* Compact typography */
.dashboard-card .fw-bold {
    font-weight: 600 !important;
}

.dashboard-card .text-muted {
    color: #6c757d !important;
}

/* Evidence buttons styling */
.view-evidence-btn {
    transition: all 0.2s ease;
}

.view-evidence-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(13, 110, 253, 0.3);
}

.download-evidence-btn {
    transition: all 0.2s ease;
}

.download-evidence-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(25, 135, 84, 0.3);
}

/* Responsive improvements */
@media (max-width: 768px) {
    .dashboard-header {
        padding: 15px;
        margin-bottom: 20px;
    }

    .dashboard-title {
        font-size: 1.5rem;
    }

    #vote-toast-container {
        right: 10px !important;
        max-width: calc(100vw - 20px) !important;
    }

    .vote-update-toast {
        padding: 12px 16px !important;
    }

    .col-lg-4, .col-lg-8 {
        margin-bottom: 1rem;
    }

    .dashboard-card {
        min-height: auto !important;
    }
}
</style>
@endsection

@section('content')
<div class="container">
    <!-- Page Header -->
    <div class="dashboard-header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="dashboard-title">Polling Stations</h1>
                <p class="dashboard-subtitle">Monitor and manage all polling stations</p>
            </div>
            <div class="d-flex align-items-center">
                <span class="badge bg-primary me-2">{{ $polling_stations->total() }} Total Stations</span>
                <a href="{{ route('home') }}" class="btn btn-outline-secondary">
                    <i class="bi bi-arrow-left me-1"></i>Back to Dashboard
                </a>
            </div>
        </div>
    </div>

    <!-- Polling Stations Section -->
    <div class="row mt-4 mb-3">
        <div class="col-12">
            <div class="section-header d-flex align-items-center justify-content-between mb-3">
                <div class="d-flex align-items-center">
                    <div class="section-icon-container me-2" style="background-color: rgba(13, 110, 253, 0.1); width: 40px; height: 40px; display: flex; align-items: center; justify-content: center; border-radius: 8px;">
                        <i class="bi bi-building text-primary" style="font-size: 1.2rem;"></i>
                    </div>
                    <h4 class="section-title mb-0 fw-bold">Polling Stations</h4>
                </div>

                <!-- Candidate Selector for Win/Loss Analysis -->
                <div class="d-flex align-items-center">
                    <label for="candidateSelector" class="me-2 mb-0 fw-bold">Analyze Candidate:</label>
                    <select id="candidateSelector" class="form-select form-select-sm" style="width: 200px;">
                        <option value="">Select Candidate</option>
                        @foreach($positions as $position)
                            <optgroup label="{{ $position->name }}">
                                @foreach($position->candidates as $candidate)
                                    <option value="{{ $candidate->id }}" data-position="{{ $position->id }}">
                                        {{ $candidate->name }}
                                    </option>
                                @endforeach
                            </optgroup>
                        @endforeach
                    </select>

                    <!-- Candidate Results Summary -->
                    <div id="candidateResultsSummary" class="ms-3 d-none">
                        <div class="d-flex align-items-center">
                            <div class="px-3 py-1 rounded-pill bg-light border">
                                <span class="fw-bold" id="candidateName">Candidate</span>
                                <span class="text-muted" id="candidatePosition">(Position)</span>:
                                <span class="badge bg-success me-1"><i class="bi bi-trophy-fill"></i> <span id="wonCount">0</span> Won</span>
                                <span class="badge bg-danger"><i class="bi bi-x-circle-fill"></i> <span id="lostCount">0</span> Lost</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card shadow-sm">
                <div class="card-body">
                    <!-- Enhanced Filters Form -->
                    <form method="GET" action="{{ route('polling_stations.view') }}" id="filtersForm" class="mb-4">
                        <div class="row g-3">
                            <!-- Search -->
                            <div class="col-md-3">
                                <label class="form-label small fw-bold">
                                    <i class="bi bi-search me-1"></i>Search
                                </label>
                                <input type="text" name="search" id="stationSearchInput" class="form-control form-control-sm"
                                       placeholder="Search stations, locations..."
                                       value="{{ $currentFilters['search'] ?? '' }}"
                                       autocomplete="off">
                            </div>

                            <!-- District Filter -->
                            <div class="col-md-2">
                                <label class="form-label small fw-bold">
                                    <i class="bi bi-geo-alt me-1"></i>District
                                </label>
                                <select name="district" class="form-select form-select-sm" id="districtFilter">
                                    <option value="">All Districts</option>
                                    @foreach($districts as $district)
                                        <option value="{{ $district }}" {{ ($currentFilters['district'] ?? '') == $district ? 'selected' : '' }}>
                                            {{ $district }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>

                            <!-- County Filter -->
                            <div class="col-md-2">
                                <label class="form-label small fw-bold">
                                    <i class="bi bi-map me-1"></i>County
                                </label>
                                <select name="county" class="form-select form-select-sm" id="countyFilter">
                                    <option value="">All Counties</option>
                                    @foreach($counties as $county)
                                        <option value="{{ $county }}" {{ ($currentFilters['county'] ?? '') == $county ? 'selected' : '' }}>
                                            {{ $county }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>

                            <!-- Subcounty Filter -->
                            <div class="col-md-2">
                                <label class="form-label small fw-bold">
                                    <i class="bi bi-building me-1"></i>Subcounty
                                </label>
                                <select name="subcounty" class="form-select form-select-sm">
                                    <option value="">All Subcounties</option>
                                    @foreach($subcounties as $subcounty)
                                        <option value="{{ $subcounty }}" {{ ($currentFilters['subcounty'] ?? '') == $subcounty ? 'selected' : '' }}>
                                            {{ $subcounty }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>

                            <!-- Coordinates Filter -->
                            <div class="col-md-2">
                                <label class="form-label small fw-bold">
                                    <i class="bi bi-geo me-1"></i>Coordinates
                                </label>
                                <select name="coordinates" class="form-select form-select-sm">
                                    <option value="">All Stations</option>
                                    <option value="with" {{ ($currentFilters['coordinates'] ?? '') == 'with' ? 'selected' : '' }}>With Coordinates</option>
                                    <option value="without" {{ ($currentFilters['coordinates'] ?? '') == 'without' ? 'selected' : '' }}>Without Coordinates</option>
                                </select>
                            </div>

                            <!-- Agents Filter -->
                            <div class="col-md-1">
                                <label class="form-label small fw-bold">
                                    <i class="bi bi-people me-1"></i>Agents
                                </label>
                                <select name="agents" class="form-select form-select-sm">
                                    <option value="">All</option>
                                    <option value="with" {{ ($currentFilters['agents'] ?? '') == 'with' ? 'selected' : '' }}>With</option>
                                    <option value="without" {{ ($currentFilters['agents'] ?? '') == 'without' ? 'selected' : '' }}>Without</option>
                                </select>
                            </div>
                        </div>

                        <div class="row g-3 mt-2">
                            <!-- Results Filter -->
                            <div class="col-md-2">
                                <label class="form-label small fw-bold">
                                    <i class="bi bi-check-circle me-1"></i>Results
                                </label>
                                <select name="results" class="form-select form-select-sm">
                                    <option value="">All Stations</option>
                                    <option value="submitted" {{ ($currentFilters['results'] ?? '') == 'submitted' ? 'selected' : '' }}>Submitted</option>
                                    <option value="pending" {{ ($currentFilters['results'] ?? '') == 'pending' ? 'selected' : '' }}>Pending</option>
                                </select>
                            </div>

                            <!-- Evidence Filter -->
                            <div class="col-md-2">
                                <label class="form-label small fw-bold">
                                    <i class="bi bi-file-earmark me-1"></i>Evidence
                                </label>
                                <select name="evidence" class="form-select form-select-sm">
                                    <option value="">All Stations</option>
                                    <option value="with" {{ ($currentFilters['evidence'] ?? '') == 'with' ? 'selected' : '' }}>With Evidence</option>
                                    <option value="without" {{ ($currentFilters['evidence'] ?? '') == 'without' ? 'selected' : '' }}>Without Evidence</option>
                                </select>
                            </div>

                            <!-- Per Page -->
                            <div class="col-md-2">
                                <label class="form-label small fw-bold">
                                    <i class="bi bi-list me-1"></i>Per Page
                                </label>
                                <select name="per_page" class="form-select form-select-sm">
                                    <option value="10" {{ ($currentFilters['per_page'] ?? 15) == 10 ? 'selected' : '' }}>10</option>
                                    <option value="15" {{ ($currentFilters['per_page'] ?? 15) == 15 ? 'selected' : '' }}>15</option>
                                    <option value="25" {{ ($currentFilters['per_page'] ?? 15) == 25 ? 'selected' : '' }}>25</option>
                                    <option value="50" {{ ($currentFilters['per_page'] ?? 15) == 50 ? 'selected' : '' }}>50</option>
                                    <option value="100" {{ ($currentFilters['per_page'] ?? 15) == 100 ? 'selected' : '' }}>100</option>
                                </select>
                            </div>

                            <!-- Action Buttons -->
                            <div class="col-md-6 d-flex align-items-end gap-2">
                                <button type="submit" class="btn btn-primary btn-sm">
                                    <i class="bi bi-funnel me-1"></i>Apply Filters
                                </button>
                                <a href="{{ route('polling_stations.view') }}" class="btn btn-outline-secondary btn-sm">
                                    <i class="bi bi-x-circle me-1"></i>Clear All
                                </a>
                                <button type="button" id="resetSearchBtn" class="btn btn-outline-info btn-sm">
                                    <i class="bi bi-arrow-counterclockwise me-1"></i>Reset Search
                                </button>
                                <div class="ms-auto">
                                    <span class="badge bg-info">
                                        {{ $polling_stations->total() }} stations found
                                    </span>
                                </div>
                            </div>
                        </div>
                    </form>

                    <!-- Polling Stations Table -->
                    <div class="table-responsive polling-stations-table-container">
                        <table class="table table-hover table-striped align-middle" id="pollingStationsTable">
                            <thead class="table-light">
                                <tr>
                                    <th style="width: 50px;" class="text-center">#</th>
                                    <th>Station Name</th>
                                    <th>Agent</th>
                                    <th>Status</th>
                                    <th>Last Updated</th>
                                    <th class="text-center">Evidence</th>
                                    <th class="text-center">Votes</th>
                                    <th class="text-center d-none" id="resultHeader">Result</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach ($polling_stations as $index => $polling_station)
                                @php
                                    // Check if agent has submitted any votes and get the latest submission time
                                    $latestVote = null;
                                    $hasSubmittedResults = false;
                                    $stationSpoiledVotes = 0;
                                    $evidenceCount = 0;
                                    $totalStationVotes = 0;

                                    if($polling_station->agent) {
                                        $latestVote = \App\Models\Vote::where('agent_id', $polling_station->agent->id)
                                            ->orderBy('created_at', 'desc')
                                            ->first();
                                        $hasSubmittedResults = $latestVote !== null;

                                        // Get latest 2 evidence records for this agent
                                        $latestEvidence = \App\Models\Eveidence::where('agent_id', $polling_station->agent->id)
                                            ->latest('created_at')
                                            ->limit(2)
                                            ->get();
                                        $evidenceCount = $latestEvidence->count();

                                        // Calculate total votes for this station across all positions
                                        foreach ($positions as $position) {
                                            $totalStationVotes += $position->totalStationVotes($polling_station->id);
                                        }
                                    }

                                    // Get spoiled votes for this polling station
                                    $stationSpoiledVotes = isset($spoiledVotesByStation[$polling_station->id]) ? $spoiledVotesByStation[$polling_station->id]->total : 0;
                                @endphp
                                <tr class="station-row"
                                    data-has-evidence="{{ $evidenceCount > 0 ? 'yes' : 'no' }}"
                                    data-has-results="{{ $hasSubmittedResults ? 'yes' : 'no' }}"
                                    data-station-id="{{ $polling_station->id }}"
                                    data-station-name="{{ $polling_station->name }}">
                                    <td class="text-center">{{ ($polling_stations->currentPage() - 1) * $polling_stations->perPage() + $index + 1 }}</td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="station-icon me-2 rounded-circle d-flex align-items-center justify-content-center"
                                                 style="width: 36px; height: 36px; background-color: rgba(13, 110, 253, 0.1);">
                                                <i class="bi bi-building text-primary"></i>
                                            </div>
                                            <div>
                                                <div class="fw-bold">{{ $polling_station->name }}</div>
                                                <div class="small text-muted">Code: {{ $polling_station->code ?? 'N/A' }}</div>
                                            </div>
                                        </div>
                                    </td>

                                    <td>
                                        @if($polling_station->agent)
                                        <div class="d-flex align-items-center">
                                            <div class="agent-icon me-2 rounded-circle d-flex align-items-center justify-content-center"
                                                 style="width: 32px; height: 32px; background-color: rgba(108, 117, 125, 0.1);">
                                                <i class="bi bi-person text-secondary"></i>
                                            </div>
                                            <div>
                                                <div>{{ $polling_station->agent->user->name }}</div>
                                                <div class="small text-muted">
                                                    <i class="bi bi-telephone me-1"></i>
                                                    {{ $polling_station->agent->user->phone_number }}
                                                </div>
                                            </div>
                                        </div>
                                        @else
                                        <span class="text-muted">No agent assigned</span>
                                        @endif
                                    </td>
                                    <td>
                                        @if($polling_station->agent)
                                            @if($hasSubmittedResults)
                                                <span class="badge bg-success"><i class="bi bi-check-circle me-1"></i> Results Submitted</span>
                                            @else
                                                <span class="badge bg-warning text-dark"><i class="bi bi-exclamation-triangle me-1"></i> Results Pending</span>
                                            @endif

                                            @if($stationSpoiledVotes > 0)
                                                <div class="mt-1">
                                                    <span class="badge bg-danger"><i class="bi bi-x-circle me-1"></i> {{ $stationSpoiledVotes }} Spoiled</span>
                                                </div>
                                            @endif
                                        @else
                                            <span class="badge bg-secondary"><i class="bi bi-dash-circle me-1"></i> No Agent</span>
                                        @endif
                                    </td>
                                    <td>
                                        @if($latestVote)
                                            {{ $latestVote->created_at->format('M d, H:i') }}
                                        @else
                                            <span class="text-muted">--</span>
                                        @endif
                                    </td>
                                    <td class="text-center">
                                        @if($polling_station->agent && $evidenceCount > 0)
                                            <div class="evidence-container">
                                                <div class="d-flex align-items-center justify-content-center mb-2">
                                                    <span class="badge bg-info me-2">{{ $evidenceCount }} Evidence</span>
                                                    <button class="btn btn-sm btn-primary view-evidence-btn"
                                                            data-station-id="{{ $polling_station->id }}"
                                                            data-agent-id="{{ $polling_station->agent->id }}"
                                                            title="View All Evidence">
                                                        <i class="bi bi-eye"></i>
                                                    </button>
                                                </div>

                                                <!-- Latest Evidence Preview -->
                                                <div class="latest-evidence-preview">
                                                    @foreach($latestEvidence as $index => $evidence)
                                                        <div class="evidence-item mb-1 p-2 border rounded" style="font-size: 0.85rem;">
                                                            <div class="d-flex align-items-center justify-content-between">
                                                                <div class="evidence-info flex-grow-1">
                                                                    <div class="fw-bold text-truncate" style="max-width: 120px;" title="{{ $evidence->file_name ?? 'Evidence File' }}">
                                                                        {{ $evidence->file_name ?? 'Evidence File' }}
                                                                    </div>
                                                                    <div class="small text-muted">
                                                                        {{ $evidence->created_at->format('M d, H:i') }}
                                                                        @if($index === 0)
                                                                            <span class="badge badge-sm bg-success ms-1">Latest</span>
                                                                        @endif
                                                                    </div>
                                                                </div>
                                                                <div class="evidence-actions">
                                                                    <a href="{{ url('files/' . $evidence->file_url) }}"
                                                                       target="_blank"
                                                                       class="btn btn-xs btn-outline-primary"
                                                                       title="View {{ $evidence->file_name ?? 'Evidence' }}">
                                                                        <i class="bi bi-eye" style="font-size: 0.7rem;"></i>
                                                                    </a>
                                                                    <a href="{{ url('files/' . $evidence->file_url) }}"
                                                                       download
                                                                       class="btn btn-xs btn-outline-success ms-1"
                                                                       title="Download {{ $evidence->file_name ?? 'Evidence' }}">
                                                                        <i class="bi bi-download" style="font-size: 0.7rem;"></i>
                                                                    </a>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    @endforeach
                                                </div>
                                            </div>
                                        @else
                                            <span class="text-muted">No Evidence</span>
                                        @endif
                                    </td>
                                    <td class="text-center">
                                        @if($totalStationVotes > 0)
                                            <span class="fw-bold">{{ number_format($totalStationVotes) }}</span>
                                        @else
                                            <span class="text-muted">--</span>
                                        @endif
                                    </td>
                                    <td class="text-center d-none station-result-cell" data-station-id="{{ $polling_station->id }}">
                                        <span class="result-badge d-none"></span>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination Controls -->
                    <div class="d-flex justify-content-between align-items-center mt-3">
                        <div class="showing-entries">
                            <small class="text-muted">
                                Showing {{ $polling_stations->firstItem() ?? 0 }} to {{ $polling_stations->lastItem() ?? 0 }}
                                of {{ $polling_stations->total() }} stations
                            </small>
                        </div>
                        <div class="pagination-container">
                            {{ $polling_stations->links('pagination::bootstrap-4') }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Station Details Modal -->
    <div class="modal fade" id="stationDetailsModal" tabindex="-1" aria-labelledby="stationDetailsModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg modal-dialog-scrollable">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="stationDetailsModalLabel">Polling Station Details</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" id="stationDetailsContent">
                    <!-- Station details will be loaded here dynamically -->
                    <div class="text-center py-5">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2">Loading station details...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Evidence Modal -->
    <div class="modal fade" id="evidenceModal" tabindex="-1" aria-labelledby="evidenceModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg modal-dialog-scrollable">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="evidenceModalLabel">Station Evidence</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" id="evidenceModalContent">
                    <!-- Evidence will be loaded here dynamically -->
                    <div class="text-center py-5">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2">Loading evidence...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // --- Polling Stations Table Logic ---
        const table = document.getElementById('pollingStationsTable');
        const tableBody = table.querySelector('tbody');
        const stationRows = Array.from(document.querySelectorAll('.station-row'));
        const resultHeader = document.getElementById('resultHeader');
        const candidateSelector = document.getElementById('candidateSelector');
        const searchInput = document.getElementById('stationSearchInput');

        // Precompute votes by station and candidate for fast lookup
        const stationVotes = {};
        @foreach ($polling_stations as $polling_station)
            stationVotes[{{ $polling_station->id }}] = {};
            @foreach ($positions as $position)
                @foreach ($position->candidates as $candidate)
                    stationVotes[{{ $polling_station->id }}][{{ $candidate->id }}] = {{ \App\Models\Vote::join('agents', 'votes.agent_id', '=', 'agents.id')
                        ->where('agents.polling_station_id', $polling_station->id)
                        ->where('votes.candidate_id', $candidate->id)
                        ->sum('votes.number_of_votes') ?? 0 }};
                @endforeach
            @endforeach
        @endforeach



        // Handle candidate selection for win/loss analysis
        if (candidateSelector) {
            candidateSelector.addEventListener('change', function() {
                const candidateId = this.value;
                const positionId = this.options[this.selectedIndex].dataset.position;

                if (candidateId && positionId) {
                    showCandidateResults(candidateId, positionId);
                    resultHeader.classList.remove('d-none');
                    document.querySelectorAll('.station-result-cell').forEach(cell => {
                        cell.classList.remove('d-none');
                    });

                    // Update candidate summary
                    updateCandidateSummary(candidateId, this.options[this.selectedIndex].textContent);
                } else {
                    hideCandidateResults();
                    resultHeader.classList.add('d-none');
                    document.querySelectorAll('.station-result-cell').forEach(cell => {
                        cell.classList.add('d-none');
                    });

                    // Hide candidate summary
                    document.getElementById('candidateResultsSummary').classList.add('d-none');
                }
            });
        }

        function showCandidateResults(candidateId, positionId) {
            stationRows.forEach(row => {
                const stationId = row.dataset.stationId;
                const resultCell = row.querySelector('.station-result-cell');
                const resultBadge = resultCell.querySelector('.result-badge');

                if (stationVotes[stationId] && stationVotes[stationId][candidateId] !== undefined) {
                    const candidateVotes = stationVotes[stationId][candidateId];

                    // Calculate if candidate is winning in this station
                    let isWinning = true;
                    let maxVotes = candidateVotes;

                    // Check against other candidates in the same position
                    Object.keys(stationVotes[stationId]).forEach(otherCandidateId => {
                        if (otherCandidateId !== candidateId) {
                            const otherVotes = stationVotes[stationId][otherCandidateId];
                            if (otherVotes > candidateVotes) {
                                isWinning = false;
                            }
                            if (otherVotes > maxVotes) {
                                maxVotes = otherVotes;
                            }
                        }
                    });

                    // Update result badge
                    resultBadge.className = `result-badge badge ${isWinning ? 'bg-success' : 'bg-danger'}`;
                    resultBadge.textContent = isWinning ? 'Winning' : 'Losing';
                    resultBadge.classList.remove('d-none');
                } else {
                    resultBadge.className = 'result-badge badge bg-secondary';
                    resultBadge.textContent = 'No Data';
                    resultBadge.classList.remove('d-none');
                }
            });
        }

        function hideCandidateResults() {
            document.querySelectorAll('.result-badge').forEach(badge => {
                badge.classList.add('d-none');
            });
        }

        // Real-time search functionality
        if (searchInput) {
            searchInput.addEventListener('input', function() {
                const searchTerm = this.value.toLowerCase().trim();

                stationRows.forEach(row => {
                    const stationName = row.dataset.stationName.toLowerCase();
                    const rowText = row.textContent.toLowerCase();

                    if (searchTerm === '' || stationName.includes(searchTerm) || rowText.includes(searchTerm)) {
                        row.style.display = '';
                    } else {
                        row.style.display = 'none';
                    }
                });

                // Update visible count
                const visibleRows = stationRows.filter(row => row.style.display !== 'none');
                updateResultsCount(visibleRows.length);
            });
        }

        // Reset search button
        const resetSearchBtn = document.getElementById('resetSearchBtn');
        if (resetSearchBtn) {
            resetSearchBtn.addEventListener('click', function() {
                if (searchInput) {
                    searchInput.value = '';
                    // Trigger the input event to update the table
                    searchInput.dispatchEvent(new Event('input'));
                }
            });
        }

        function updateResultsCount(count) {
            const badge = document.querySelector('.badge.bg-info');
            if (badge) {
                badge.textContent = `${count} stations found`;
            }
        }

        function updateCandidateSummary(candidateId, candidateText) {
            // Show the summary
            const summary = document.getElementById('candidateResultsSummary');
            summary.classList.remove('d-none');

            // Update candidate name and position
            const parts = candidateText.split(' (');
            const candidateName = document.getElementById('candidateName');
            const candidatePosition = document.getElementById('candidatePosition');

            if (parts.length > 1) {
                candidateName.textContent = parts[0];
                candidatePosition.textContent = '(' + parts[1];
            } else {
                candidateName.textContent = candidateText;
                candidatePosition.textContent = '';
            }

            // Count wins and losses
            let wonCount = 0;
            let lostCount = 0;

            stationRows.forEach(row => {
                const stationId = row.dataset.stationId;

                if (stationVotes[stationId] && stationVotes[stationId][candidateId] !== undefined) {
                    const candidateVotes = stationVotes[stationId][candidateId];

                    // Calculate if candidate is winning in this station
                    let isWinning = true;

                    // Check against other candidates
                    Object.keys(stationVotes[stationId]).forEach(otherCandidateId => {
                        if (otherCandidateId !== candidateId) {
                            const otherVotes = stationVotes[stationId][otherCandidateId];
                            if (otherVotes > candidateVotes) {
                                isWinning = false;
                            }
                        }
                    });

                    if (candidateVotes > 0) {
                        if (isWinning) {
                            wonCount++;
                        } else {
                            lostCount++;
                        }
                    }
                }
            });

            // Update counts
            document.getElementById('wonCount').textContent = wonCount;
            document.getElementById('lostCount').textContent = lostCount;
        }

        // Handle evidence viewing
        document.addEventListener('click', function(e) {
            if (e.target.closest('.view-evidence-btn')) {
                const btn = e.target.closest('.view-evidence-btn');
                const stationId = btn.dataset.stationId;
                const agentId = btn.dataset.agentId;

                // Show evidence modal
                const modal = new bootstrap.Modal(document.getElementById('evidenceModal'));
                modal.show();

                // Load evidence data
                loadStationEvidence(stationId, agentId);
            }
        });

        function loadStationEvidence(stationId, agentId) {
            const modalContent = document.getElementById('evidenceModalContent');
            modalContent.innerHTML = `
                <div class="text-center py-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2">Loading evidence...</p>
                </div>
            `;

            // Make AJAX request to load evidence
            fetch(`/api/station-evidence/${stationId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displayEvidence(data.evidence);
                    } else {
                        modalContent.innerHTML = `
                            <div class="text-center py-5">
                                <i class="bi bi-exclamation-triangle text-warning" style="font-size: 2rem;"></i>
                                <p class="mt-2">Failed to load evidence</p>
                            </div>
                        `;
                    }
                })
                .catch(error => {
                    console.error('Error loading evidence:', error);
                    modalContent.innerHTML = `
                        <div class="text-center py-5">
                            <i class="bi bi-exclamation-triangle text-danger" style="font-size: 2rem;"></i>
                            <p class="mt-2">Error loading evidence</p>
                        </div>
                    `;
                });
        }

        function displayEvidence(evidence) {
            const modalContent = document.getElementById('evidenceModalContent');

            if (evidence.length === 0) {
                modalContent.innerHTML = `
                    <div class="text-center py-5">
                        <i class="bi bi-file-earmark text-muted" style="font-size: 2rem;"></i>
                        <p class="mt-2">No evidence found for this station</p>
                    </div>
                `;
                return;
            }

            let html = '<div class="row">';
            evidence.forEach(item => {
                html += `
                    <div class="col-md-6 mb-3">
                        <div class="card">
                            <div class="card-body">
                                <h6 class="card-title">${item.file_name || 'Evidence File'}</h6>
                                <p class="card-text small text-muted">
                                    Uploaded: ${new Date(item.created_at).toLocaleString()}
                                </p>
                                <div class="d-flex gap-2">
                                    <a href="/files/${item.file_url}" target="_blank" class="btn btn-sm btn-primary">
                                        <i class="bi bi-eye me-1"></i>View
                                    </a>
                                    <a href="/files/${item.file_url}" download class="btn btn-sm btn-success">
                                        <i class="bi bi-download me-1"></i>Download
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            });
            html += '</div>';

            modalContent.innerHTML = html;
        }

        // --- End Polling Stations Table Logic ---
    });
</script>
@endpush

@endsection
