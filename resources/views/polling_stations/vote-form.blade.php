@extends('layouts.app')

@section('content')
<div class="container-fluid px-4">
    <!-- Compact Head<PERSON> -->
    <div class="d-flex justify-content-between align-items-center mb-3 p-3 bg-success text-white rounded">
        <div>
            <h4 class="mb-1">
                <i class="bi bi-ballot-check me-2"></i>{{ $station->name }}
            </h4>
            <small class="opacity-75">
                <i class="bi bi-geo-alt me-1"></i>{{ $station->subcounty }}, {{ $station->district }}
                @if($agent) | Agent: {{ $agent->user->name }} @endif
            </small>
        </div>
        <a href="{{ route('polling_villages.index') }}" class="btn btn-outline-light btn-sm">
            <i class="bi bi-arrow-left me-1"></i>Back
        </a>
    </div>

    @if(session('success'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="bi bi-check-circle me-2"></i>{{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif

    @if(session('error'))
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="bi bi-exclamation-triangle me-2"></i>{{ session('error') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif

    @if(!$agent)
        <div class="error-state">
            <div class="error-icon">
                <i class="bi bi-x-circle"></i>
            </div>
            <h3>Cannot Submit Votes</h3>
            <p>This polling station does not have an agent assigned. Please assign an agent before submitting votes.</p>
        </div>
    @else
        <!-- Vote Submission Form -->
        <form action="{{ route('station.submit-votes', $station) }}" method="POST" enctype="multipart/form-data" id="voteForm" class="vote-form">
            @csrf

            @foreach($positions as $position)
                <div class="card mb-3">
                    <div class="card-header py-2 d-flex justify-content-between align-items-center">
                        <h6 class="mb-0">{{ $position->name }}</h6>
                        <small class="text-muted">
                            Total: <span id="total-{{ $position->id }}" class="fw-bold">0</span> votes
                        </small>
                    </div>
                    <div class="card-body p-3">
                        <div class="row g-3">
                            @foreach($position->candidates as $candidate)
                            <div class="col-md-6">
                                <div class="candidate-card d-flex align-items-center p-2 border rounded">
                                    <div class="candidate-avatar me-2">
                                        @if($candidate->picture)
                                            <img src="{{ asset('files/' . $candidate->picture) }}"
                                                 alt="{{ $candidate->name }}"
                                                 class="rounded-circle"
                                                 style="width: 35px; height: 35px; object-fit: cover;">
                                        @else
                                            <div class="rounded-circle bg-light d-flex align-items-center justify-content-center"
                                                 style="width: 35px; height: 35px;">
                                                <i class="bi bi-person text-muted"></i>
                                            </div>
                                        @endif
                                    </div>
                                    <div class="candidate-info flex-grow-1 me-2">
                                        <div class="candidate-name fw-bold small">{{ $candidate->name }}</div>
                                        @if($candidate->party)
                                            <div class="candidate-party text-muted" style="font-size: 0.7rem;">{{ $candidate->party }}</div>
                                        @endif
                                    </div>
                                    <div class="vote-input-section">
                                        <input type="number"
                                               class="form-control form-control-lg text-center fw-bold"
                                               name="votes[{{ $candidate->id }}]"
                                               value="{{ $existingVotes[$candidate->id] ?? 0 }}"
                                               min="0"
                                               placeholder="0"
                                               data-position="{{ $position->id }}"
                                               onchange="updatePositionTotal({{ $position->id }})"
                                               style="width: 100px; font-size: 1.3rem; height: 50px;">
                                    </div>
                                </div>
                            </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            @endforeach

            <!-- Evidence Upload -->
            <div class="card mb-3">
                <div class="card-header py-2">
                    <h6 class="mb-0">
                        <i class="bi bi-file-earmark-plus me-2"></i>Evidence Upload
                        <small class="text-muted ms-2">(Optional - JPG, PNG, PDF, Max 5MB each)</small>
                    </h6>
                </div>
                <div class="card-body p-3">
                    <div class="border-2 border-dashed border-secondary rounded p-3 text-center"
                         id="uploadArea"
                         style="cursor: pointer; background: #f8f9fa;">
                        <i class="bi bi-cloud-upload text-muted mb-2" style="font-size: 2rem;"></i>
                        <div class="small text-muted">Click to select files or drag and drop</div>
                        <input type="file"
                               id="evidenceFileInput"
                               name="evidence_files[]"
                               multiple
                               accept=".jpg,.jpeg,.png,.pdf"
                               style="display: none;">
                    </div>
                    <div id="selectedFiles" class="mt-2"></div>
                </div>
            </div>

            <!-- Submit Section -->
            <div class="d-flex justify-content-between align-items-center p-3 bg-light rounded">
                <div>
                    <div class="fw-bold">{{ $station->name }}</div>
                    <small class="text-muted">
                        <i class="bi bi-info-circle me-1"></i>
                        This will update all vote records and upload evidence
                    </small>
                </div>
                <button type="submit" class="btn btn-success">
                    <i class="bi bi-check-circle me-1"></i>
                    Submit Votes & Evidence
                </button>
            </div>
        </form>
    @endif
</div>

@push('scripts')
<script>
// Calculate position totals
function updatePositionTotal(positionId) {
    const inputs = document.querySelectorAll(`input[data-position="${positionId}"]`);
    let total = 0;
    
    inputs.forEach(input => {
        const value = parseInt(input.value) || 0;
        total += value;
    });
    
    document.getElementById(`total-${positionId}`).textContent = total.toLocaleString();
}

// Initialize totals on page load
document.addEventListener('DOMContentLoaded', function() {
    @foreach($positions as $position)
        updatePositionTotal({{ $position->id }});
    @endforeach
    
    // File upload handling
    const uploadArea = document.getElementById('uploadArea');
    const fileInput = document.getElementById('evidenceFileInput');
    const selectedFilesDiv = document.getElementById('selectedFiles');
    let selectedFiles = [];

    uploadArea.addEventListener('click', () => fileInput.click());
    
    fileInput.addEventListener('change', function(e) {
        handleFiles(e.target.files);
    });

    function handleFiles(files) {
        Array.from(files).forEach(file => {
            if (file.size <= 5 * 1024 * 1024) { // 5MB limit
                selectedFiles.push(file);
                displaySelectedFile(file);
            } else {
                alert(`File ${file.name} is too large. Maximum size is 5MB.`);
            }
        });
    }

    function displaySelectedFile(file) {
        const fileDiv = document.createElement('div');
        fileDiv.className = 'selected-file';
        fileDiv.innerHTML = `
            <div class="file-info">
                <i class="bi bi-file-earmark"></i>
                <span class="file-name">${file.name}</span>
                <span class="file-size">(${(file.size / 1024 / 1024).toFixed(2)} MB)</span>
            </div>
            <button type="button" class="remove-file" onclick="removeFile(this, '${file.name}')">
                <i class="bi bi-x"></i>
            </button>
        `;
        selectedFilesDiv.appendChild(fileDiv);
    }

    window.removeFile = function(button, fileName) {
        selectedFiles = selectedFiles.filter(f => f.name !== fileName);
        button.parentElement.remove();
    };
});

// Form submission confirmation
document.getElementById('voteForm').addEventListener('submit', function(e) {
    const evidenceFileInput = document.getElementById('evidenceFileInput');
    const hasEvidence = evidenceFileInput && evidenceFileInput.files && evidenceFileInput.files.length > 0;

    const evidenceText = hasEvidence ? ` and ${evidenceFileInput.files.length} evidence file(s)` : '';
    const message = `Submit votes${evidenceText} for {{ $station->name }}?`;

    if (!confirm(message)) {
        e.preventDefault();
        return false;
    }
});
</script>
@endpush

@section('styles')
<style>
/* Compact vote form styles */
.candidate-card {
    transition: all 0.2s ease;
    border: 2px solid #e9ecef !important;
    background: #ffffff;
    min-height: 60px;
    max-height: 60px;
}

.candidate-card:hover {
    border-color: #28a745 !important;
    box-shadow: 0 2px 8px rgba(40, 167, 69, 0.15);
    transform: translateY(-1px);
}

.candidate-name {
    font-size: 0.9rem;
    color: #2c3e50;
    line-height: 1.2;
}

.candidate-party {
    font-size: 0.7rem;
    color: #6c757d;
    line-height: 1;
}

.vote-input-section input {
    border: 3px solid #e9ecef !important;
    transition: all 0.2s ease;
    font-weight: 700 !important;
}

.vote-input-section input:focus {
    border-color: #28a745 !important;
    box-shadow: 0 0 0 0.3rem rgba(40, 167, 69, 0.25) !important;
}

.vote-input-section input:not(:placeholder-shown) {
    border-color: #28a745 !important;
    background-color: #f8fff9 !important;
    color: #28a745 !important;
}

.upload-area:hover {
    background-color: #e9ecef !important;
    border-color: #6c757d !important;
}

.selected-file {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.5rem;
    margin-bottom: 0.5rem;
    background: #f8f9fa;
    border-radius: 4px;
    font-size: 0.875rem;
}

.file-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.remove-file {
    background: none;
    border: none;
    color: #dc3545;
    cursor: pointer;
    padding: 0.25rem;
}

.remove-file:hover {
    color: #c82333;
}

/* Position total styling */
.card-header .fw-bold {
    color: #28a745;
    font-size: 1.1rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .candidate-card {
        min-height: 55px;
        max-height: 55px;
    }

    .candidate-avatar {
        margin-right: 0.75rem !important;
    }

    .candidate-avatar img,
    .candidate-avatar div {
        width: 30px !important;
        height: 30px !important;
    }

    .vote-input-section input {
        width: 85px !important;
        font-size: 1.2rem !important;
        height: 45px !important;
    }

    .candidate-name {
        font-size: 0.85rem !important;
    }

    .candidate-party {
        font-size: 0.65rem !important;
    }
}
</style>
@endsection
@endsection
