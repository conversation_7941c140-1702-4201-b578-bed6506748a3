@extends('layouts.app')

@section('content')
<div class="container-fluid px-4">
    <!-- Super Compressed Single-Line Header -->
    <div class="vote-form-header mb-3">
        <div class="header-content">
            <div class="header-main-info">
                <span class="vote-badge">
                    <i class="bi bi-ballot-check"></i>
                    Vote Submission
                </span>
                <h1 class="station-name">{{ $station->name }}</h1>
                <span class="location-info">
                    <i class="bi bi-geo-alt"></i>
                    {{ $station->subcounty }}, {{ $station->district }}
                </span>
            </div>

            <div class="header-secondary-info">
                @if($agent && $agent->user)
                    <span class="agent-compact">
                        <i class="bi bi-person-badge"></i>
                        {{ $agent->user->name }} • {{ $agent->user->phone_number }}
                    </span>
                @else
                    <span class="submitter-info">
                        <i class="bi bi-person-check"></i>
                        Submitting as: {{ Auth::user()->name }}
                    </span>
                @endif

                <a href="{{ route('manager.dashboard') }}" class="btn-back-compact">
                    <i class="bi bi-arrow-left"></i>
                    Dashboard
                </a>
            </div>
        </div>
    </div>

    <!-- Unified Vote Submission & Evidence Upload Form -->
        <form action="{{ route('manager.submit-votes', $station) }}" method="POST" enctype="multipart/form-data" id="voteForm" class="vote-form">
            @csrf

            <div class="positions-container">
                @foreach($positions as $position)
                <div class="position-section">
                    <div class="position-header">
                        <div class="position-title">
                            <i class="bi bi-award"></i>
                            <span>{{ $position->name }}</span>
                        </div>
                        <div class="position-total">
                            <span class="total-label">Total:</span>
                            <span class="total-value" id="total-{{ $position->id }}">0</span>
                        </div>
                    </div>

                    <div class="candidates-grid">
                        @foreach($position->candidates as $candidate)
                        <div class="candidate-item">
                            <div class="candidate-info">
                                @if($candidate->picture)
                                    <img src="{{ asset('files/' . $candidate->picture) }}"
                                         alt="{{ $candidate->name }}"
                                         class="candidate-avatar">
                                @else
                                    <div class="candidate-avatar-placeholder">
                                        <i class="bi bi-person"></i>
                                    </div>
                                @endif
                                <div class="candidate-details">
                                    <h6 class="candidate-name">{{ $candidate->name }}</h6>
                                    @if($candidate->party)
                                        <span class="candidate-party">{{ $candidate->party }}</span>
                                    @endif
                                </div>
                            </div>
                            <div class="vote-input-container">
                                <input type="number"
                                       class="vote-input"
                                       name="votes[{{ $candidate->id }}]"
                                       value="{{ $existingVotes[$candidate->id] ?? 0 }}"
                                       min="0"
                                       placeholder="0"
                                       data-position="{{ $position->id }}"
                                       onchange="updatePositionTotal({{ $position->id }})"
                                       onfocus="this.select()">
                                <span class="vote-label">votes</span>
                            </div>
                        </div>
                        @endforeach
                    </div>
                </div>
                @endforeach
            </div>

            <!-- Compact Evidence Upload -->
            <div class="evidence-compact">
                <div class="evidence-row">
                    <div class="evidence-label">
                        <i class="bi bi-camera"></i>
                        <span>Evidence</span>
                        @if(isset($existingEvidence) && $existingEvidence->count() > 0)
                            <span class="existing-badge">{{ $existingEvidence->count() }} files</span>
                        @endif
                    </div>

                    <div class="evidence-actions">
                        <div class="file-input-compact">
                            <input type="file"
                                   id="evidenceFileInput"
                                   class="evidence-file-input-compact"
                                   name="evidence_files[]"
                                   accept=".jpg,.jpeg,.png,.pdf,.mp4,.mov,.avi,.wmv,.flv,.webm"
                                   onchange="handleCompactFileSelect(this)"
                                   multiple>
                            <button type="button" class="btn-add-files" onclick="document.getElementById('evidenceFileInput').click()">
                                <i class="bi bi-plus"></i>
                                <span>Add Files</span>
                            </button>
                            <button type="button" class="btn-clear-files" onclick="clearAllCompactFiles()" style="display: none;">
                                <i class="bi bi-trash"></i>
                                <span>Clear All</span>
                            </button>
                        </div>

                        <!-- Hidden inputs for file names -->
                        <div id="fileNamesContainer"></div>

                        <div class="evidence-type-pills">
                            <button type="button" class="type-pill" onclick="selectCompactType('DR Form')" title="DR Form">
                                <i class="bi bi-file-text"></i>
                            </button>
                            <button type="button" class="type-pill" onclick="selectCompactType('Tally Sheet')" title="Tally Sheet">
                                <i class="bi bi-table"></i>
                            </button>
                            <button type="button" class="type-pill" onclick="selectCompactType('Photo')" title="Photo">
                                <i class="bi bi-camera"></i>
                            </button>
                            <button type="button" class="type-pill" onclick="selectCompactType('Video')" title="Video">
                                <i class="bi bi-camera-video"></i>
                            </button>
                        </div>

                        @if(isset($existingEvidence) && $existingEvidence->count() > 0)
                        <button type="button" class="btn-view-existing" onclick="toggleCompactExisting()" title="View existing files">
                            <i class="bi bi-eye"></i>
                        </button>
                        @endif
                    </div>
                </div>

                <!-- Selected Files (Compact) -->
                <div id="selectedFilesCompact" class="selected-files-compact" style="display: none;">
                    <div class="files-compact-list" id="filesCompactList"></div>
                    <button type="button" class="btn-clear-compact" onclick="clearAllCompactFiles()">
                        <i class="bi bi-x"></i>
                        Clear
                    </button>
                </div>

                <!-- Existing Evidence (Compact) -->
                @if(isset($existingEvidence) && $existingEvidence->count() > 0)
                <div id="existingCompact" class="existing-compact" style="display: none;">
                    @foreach($existingEvidence as $evidence)
                    <div class="existing-file-compact">
                        @php
                            $fileExt = strtolower(pathinfo($evidence->file_url, PATHINFO_EXTENSION));
                            $videoExts = ['mp4', 'mov', 'avi', 'wmv', 'flv', 'webm'];
                            $iconClass = 'bi-image'; // default for images
                            if (str_ends_with(strtolower($evidence->file_url), '.pdf')) {
                                $iconClass = 'bi-file-pdf';
                            } elseif (in_array($fileExt, $videoExts)) {
                                $iconClass = 'bi-camera-video';
                            }
                        @endphp
                        <i class="bi {{ $iconClass }}"></i>
                        <span class="file-name-compact">{{ $evidence->file_name ?: 'Evidence' }}</span>
                        <a href="{{ asset('files/' . $evidence->file_url) }}" target="_blank" class="view-link-compact">
                            <i class="bi bi-eye"></i>
                        </a>
                    </div>
                    @endforeach
                </div>
                @endif
            </div>

            <!-- Compact Submit Section -->
            <div class="submit-section">
                <div class="submit-info">
                    <div class="submit-summary">
                        <span class="summary-label">Submitting votes and evidence for:</span>
                        <span class="summary-station">{{ $station->name }}</span>
                    </div>
                    <div class="text-warning">
                        <i class="bi bi-info-circle"></i>
                        <span>This will update all vote records and upload evidence for this station</span>
                    </div>
                </div>
                <button type="submit" class="btn-submit">
                    <i class="bi bi-check-circle"></i>
                    <span>Submit Votes & Evidence</span>
                </button>
            </div>
        </form>
</div>

@section('styles')
<style>
/* Modern Vote Form Styles */
:root {
    --primary-gold: #D4AF37;
    --primary-gold-light: #E8C547;
    --primary-gold-dark: #B8941F;
    --success-green: #10B981;
    --warning-orange: #F59E0B;
    --error-red: #EF4444;
    --neutral-50: #F9FAFB;
    --neutral-100: #F3F4F6;
    --neutral-200: #E5E7EB;
    --neutral-300: #D1D5DB;
    --neutral-400: #9CA3AF;
    --neutral-500: #6B7280;
    --neutral-600: #4B5563;
    --neutral-700: #374151;
    --neutral-800: #1F2937;
    --neutral-900: #111827;
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Super Compressed Header */
.vote-form-header {
    background: linear-gradient(135deg, var(--primary-gold) 0%, var(--primary-gold-light) 100%);
    border-radius: 8px;
    padding: 0.75rem 1rem;
    color: white;
    box-shadow: var(--shadow-md);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 1rem;
    min-height: 32px;
}

.header-main-info {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex: 1;
}

.header-secondary-info {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.vote-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    background: rgba(255, 255, 255, 0.2);
    padding: 0.125rem 0.5rem;
    border-radius: 20px;
    font-size: 0.7rem;
    font-weight: 600;
    backdrop-filter: blur(10px);
    white-space: nowrap;
}

.station-name {
    font-size: 1.125rem;
    font-weight: 700;
    margin: 0;
    line-height: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 200px;
}

.location-info {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    font-size: 0.75rem;
    font-weight: 500;
    opacity: 0.9;
    white-space: nowrap;
}

.location-info i {
    font-size: 0.75rem;
    opacity: 0.8;
}

.agent-compact {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    font-size: 0.75rem;
    font-weight: 500;
    opacity: 0.95;
    white-space: nowrap;
}

.agent-compact i {
    font-size: 0.75rem;
    opacity: 0.8;
}

.agent-warning-compact {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    color: #FEF3C7;
    font-size: 0.75rem;
    font-weight: 600;
    background: rgba(245, 158, 11, 0.2);
    padding: 0.125rem 0.5rem;
    border-radius: 4px;
    backdrop-filter: blur(10px);
    white-space: nowrap;
}

.agent-warning-compact i {
    color: #F59E0B;
    font-size: 0.75rem;
}

.submitter-info {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    color: #D1FAE5;
    font-size: 0.75rem;
    background: rgba(16, 185, 129, 0.15);
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    backdrop-filter: blur(10px);
    white-space: nowrap;
}

.submitter-info i {
    color: #10B981;
    font-size: 0.75rem;
}

.btn-back-compact {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    background: rgba(255, 255, 255, 0.15);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
    padding: 0.25rem 0.5rem;
    border-radius: 6px;
    text-decoration: none;
    font-weight: 600;
    font-size: 0.75rem;
    transition: all 0.2s ease;
    backdrop-filter: blur(10px);
    white-space: nowrap;
}

.btn-back-compact:hover {
    background: rgba(255, 255, 255, 0.25);
    color: white;
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
}

.btn-back-compact i {
    font-size: 0.75rem;
}

/* Removed - Info cards now integrated into header */

/* Error State */
.error-state {
    text-align: center;
    padding: 4rem 2rem;
    background: white;
    border-radius: 16px;
    border: 1px solid var(--neutral-200);
    box-shadow: var(--shadow-sm);
}

.error-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 1.5rem;
    background: #FEE2E2;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--error-red);
    font-size: 2rem;
}

.error-state h3 {
    color: var(--neutral-800);
    margin-bottom: 1rem;
    font-weight: 700;
}

.error-state p {
    color: var(--neutral-600);
    max-width: 500px;
    margin: 0 auto;
    line-height: 1.6;
}

/* Vote Form */
.vote-form {
    max-width: none;
}

.positions-container {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.position-section {
    background: white;
    border-radius: 16px;
    border: 1px solid var(--neutral-200);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    transition: all 0.2s ease;
}

.position-section:hover {
    box-shadow: var(--shadow-md);
}

.position-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 2rem;
    background: linear-gradient(135deg, var(--neutral-50) 0%, var(--neutral-100) 100%);
    border-bottom: 1px solid var(--neutral-200);
}

.position-title {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--neutral-800);
}

.position-title i {
    color: var(--primary-gold);
    font-size: 1.375rem;
}

.position-total {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: white;
    padding: 0.5rem 1rem;
    border-radius: 50px;
    border: 1px solid var(--neutral-200);
    box-shadow: var(--shadow-sm);
}

.total-label {
    font-size: 0.875rem;
    color: var(--neutral-500);
    font-weight: 500;
}

.total-value {
    font-size: 1.125rem;
    font-weight: 700;
    color: var(--primary-gold);
    min-width: 2rem;
    text-align: center;
}

.candidates-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 1rem;
    padding: 2rem;
}

.candidate-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1.25rem;
    background: var(--neutral-50);
    border: 1px solid var(--neutral-200);
    border-radius: 12px;
    transition: all 0.2s ease;
}

.candidate-item:hover {
    background: white;
    border-color: var(--primary-gold);
    box-shadow: var(--shadow-md);
    transform: translateY(-1px);
}

.candidate-info {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex: 1;
}

.candidate-avatar {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid var(--neutral-200);
    transition: all 0.2s ease;
}

.candidate-item:hover .candidate-avatar {
    border-color: var(--primary-gold);
}

.candidate-avatar-placeholder {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background: var(--neutral-200);
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid var(--neutral-300);
    color: var(--neutral-500);
    font-size: 1.25rem;
    transition: all 0.2s ease;
}

.candidate-item:hover .candidate-avatar-placeholder {
    background: var(--primary-gold);
    border-color: var(--primary-gold);
    color: white;
}

.candidate-details {
    flex: 1;
}

.candidate-name {
    font-size: 1rem;
    font-weight: 600;
    color: var(--neutral-800);
    margin: 0 0 0.25rem 0;
    line-height: 1.3;
}

.candidate-party {
    font-size: 0.875rem;
    color: var(--neutral-500);
    font-weight: 500;
}

.vote-input-container {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    min-width: 120px;
}

.vote-input {
    width: 80px;
    height: 48px;
    border: 2px solid var(--neutral-200);
    border-radius: 8px;
    text-align: center;
    font-size: 1.125rem;
    font-weight: 700;
    color: var(--neutral-800);
    background: white;
    transition: all 0.2s ease;
    outline: none;
}

.vote-input:focus {
    border-color: var(--primary-gold);
    box-shadow: 0 0 0 3px rgba(212, 175, 55, 0.1);
}

.vote-input:hover {
    border-color: var(--primary-gold-light);
}

.vote-label {
    font-size: 0.875rem;
    color: var(--neutral-500);
    font-weight: 500;
}

/* Submit Section */
.submit-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: white;
    border: 1px solid var(--neutral-200);
    border-radius: 16px;
    padding: 2rem;
    margin-top: 2rem;
    box-shadow: var(--shadow-sm);
}

.submit-info {
    flex: 1;
}

.submit-summary {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
}

.summary-label {
    font-size: 1rem;
    color: var(--neutral-600);
    font-weight: 500;
}

.summary-station {
    font-size: 1rem;
    color: var(--neutral-800);
    font-weight: 700;
}

.submit-warning {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--neutral-500);
    font-size: 0.875rem;
}

.btn-submit {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    background: linear-gradient(135deg, var(--primary-gold) 0%, var(--primary-gold-light) 100%);
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 12px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: var(--shadow-md);
}

.btn-submit:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    background: linear-gradient(135deg, var(--primary-gold-dark) 0%, var(--primary-gold) 100%);
}

.btn-submit:active {
    transform: translateY(0);
    box-shadow: var(--shadow-md);
}

/* Responsive Design */
@media (max-width: 768px) {
    .header-content {
        flex-wrap: wrap;
        gap: 0.5rem;
    }

    .header-main-info {
        flex-wrap: wrap;
        gap: 0.5rem;
        min-width: 0;
    }

    .header-secondary-info {
        flex-wrap: wrap;
        gap: 0.5rem;
    }

    .station-name {
        max-width: 150px;
        font-size: 1rem;
    }

    .location-info {
        font-size: 0.7rem;
    }

    .candidates-grid {
        grid-template-columns: 1fr;
        padding: 1rem;
    }

    .submit-section {
        flex-direction: column;
        gap: 1.5rem;
        text-align: center;
    }

    .position-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
}

@media (max-width: 480px) {
    .vote-form-header {
        padding: 1rem;
    }

    .header-title {
        font-size: 1.25rem;
    }

    .header-content {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }

    .candidate-item {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .vote-input-container {
        justify-content: center;
    }
}

/* Compact Evidence Upload */
.evidence-compact {
    background: white;
    border: 1px solid var(--neutral-200);
    border-radius: 8px;
    margin: 1rem 0;
    padding: 1rem;
    box-shadow: var(--shadow-sm);
}

.evidence-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 1rem;
}

.evidence-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
    color: var(--neutral-800);
    font-size: 0.875rem;
}

.evidence-label i {
    color: var(--primary-gold);
    font-size: 1rem;
}

.existing-badge {
    background: var(--success-green);
    color: white;
    padding: 0.125rem 0.375rem;
    border-radius: 10px;
    font-size: 0.7rem;
    font-weight: 500;
}

.evidence-actions {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.file-input-compact {
    position: relative;
}

.evidence-file-input-compact {
    position: absolute;
    opacity: 0;
    width: 100%;
    height: 100%;
    cursor: pointer;
}

.btn-add-files {
    display: flex;
    align-items: center;
    gap: 0.375rem;
    background: var(--primary-gold);
    color: white;
    border: none;
    padding: 0.5rem 0.75rem;
    border-radius: 6px;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-add-files:hover {
    background: var(--primary-gold-dark);
    transform: translateY(-1px);
}

.btn-add-files i {
    font-size: 0.875rem;
}

.btn-clear-files {
    display: flex;
    align-items: center;
    gap: 0.375rem;
    background: var(--danger-red, #dc3545);
    color: white;
    border: none;
    padding: 0.5rem 0.75rem;
    border-radius: 6px;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    margin-left: 0.5rem;
}

.btn-clear-files:hover {
    background: #c82333;
    transform: translateY(-1px);
}

.btn-clear-files i {
    font-size: 0.875rem;
}

.evidence-type-pills {
    display: flex;
    gap: 0.25rem;
}

.type-pill {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: white;
    border: 1px solid var(--neutral-300);
    border-radius: 6px;
    color: var(--neutral-600);
    cursor: pointer;
    transition: all 0.2s ease;
}

.type-pill:hover {
    background: var(--primary-gold);
    color: white;
    border-color: var(--primary-gold);
}

.type-pill.selected {
    background: var(--primary-gold);
    color: white;
    border-color: var(--primary-gold);
}

.type-pill i {
    font-size: 0.875rem;
}

.btn-view-existing {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--neutral-100);
    border: 1px solid var(--neutral-300);
    border-radius: 6px;
    color: var(--neutral-600);
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-view-existing:hover {
    background: var(--neutral-200);
    color: var(--neutral-800);
}

.btn-view-existing.active {
    background: var(--primary-gold);
    color: white;
    border-color: var(--primary-gold);
}

/* Compact Selected Files */
.selected-files-compact {
    margin-top: 0.75rem;
    padding-top: 0.75rem;
    border-top: 1px solid var(--neutral-200);
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.files-compact-list {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    flex: 1;
}

.file-compact-item {
    display: flex;
    align-items: center;
    gap: 0.375rem;
    background: var(--neutral-50);
    border: 1px solid var(--neutral-200);
    border-radius: 6px;
    padding: 0.375rem 0.5rem;
    font-size: 0.75rem;
    color: var(--neutral-700);
}

.file-compact-icon {
    color: var(--primary-gold);
    font-size: 0.875rem;
}

.file-compact-name {
    max-width: 80px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-weight: 500;
}

.file-compact-remove {
    color: var(--error-red);
    cursor: pointer;
    font-size: 0.75rem;
    padding: 0.125rem;
    border-radius: 2px;
    transition: all 0.2s ease;
}

.file-compact-remove:hover {
    background: var(--error-red);
    color: white;
}

.btn-clear-compact {
    background: var(--error-red);
    color: white;
    border: none;
    padding: 0.375rem 0.5rem;
    border-radius: 4px;
    font-size: 0.75rem;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.btn-clear-compact:hover {
    background: #DC2626;
}

/* Compact Existing Evidence */
.existing-compact {
    margin-top: 0.75rem;
    padding-top: 0.75rem;
    border-top: 1px solid var(--neutral-200);
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.existing-file-compact {
    display: flex;
    align-items: center;
    gap: 0.375rem;
    background: var(--neutral-50);
    border: 1px solid var(--neutral-200);
    border-radius: 6px;
    padding: 0.375rem 0.5rem;
    font-size: 0.75rem;
    color: var(--neutral-700);
}

.existing-file-compact i:first-child {
    color: var(--primary-gold);
    font-size: 0.875rem;
}

.file-name-compact {
    max-width: 80px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-weight: 500;
}

.view-link-compact {
    color: var(--primary-gold);
    text-decoration: none;
    font-size: 0.75rem;
    padding: 0.125rem;
    border-radius: 2px;
    transition: all 0.2s ease;
}

.view-link-compact:hover {
    background: var(--primary-gold);
    color: white;
}

/* Responsive Compact Design */
@media (max-width: 768px) {
    .evidence-row {
        flex-direction: column;
        gap: 0.75rem;
        align-items: flex-start;
    }

    .evidence-actions {
        width: 100%;
        justify-content: space-between;
    }

    .files-compact-list {
        flex-direction: column;
    }

    .existing-compact {
        flex-direction: column;
    }
}

.selected-files {
    background: var(--neutral-50);
    border: 1px solid var(--neutral-200);
    border-radius: 8px;
    padding: 1rem;
}

.selected-files-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.75rem;
}

.selected-count {
    font-weight: 600;
    color: var(--neutral-700);
}

.btn-clear-all {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    background: none;
    border: none;
    color: var(--error-red);
    font-size: 0.875rem;
    cursor: pointer;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.btn-clear-all:hover {
    background: var(--error-red);
    color: white;
}

.files-list {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.file-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem;
    background: white;
    border: 1px solid var(--neutral-200);
    border-radius: 6px;
}

.file-item-icon {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--primary-gold);
    color: white;
    border-radius: 6px;
    flex-shrink: 0;
}

.file-item-info {
    flex: 1;
    min-width: 0;
}

.file-item-name {
    font-weight: 600;
    color: var(--neutral-800);
    font-size: 0.875rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.file-item-size {
    font-size: 0.75rem;
    color: var(--neutral-500);
}

.file-item-description {
    width: 150px;
    padding: 0.25rem 0.5rem;
    border: 1px solid var(--neutral-300);
    border-radius: 4px;
    font-size: 0.875rem;
    outline: none;
}

.file-item-description:focus {
    border-color: var(--primary-gold);
}

.file-item-remove {
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: none;
    border: 1px solid var(--error-red);
    color: var(--error-red);
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
    flex-shrink: 0;
}

.file-item-remove:hover {
    background: var(--error-red);
    color: white;
}

/* Existing Evidence Compact */
.existing-evidence-compact {
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid var(--neutral-200);
}

.btn-toggle-existing {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    width: 100%;
    padding: 0.75rem 1rem;
    background: var(--neutral-50);
    border: 1px solid var(--neutral-200);
    border-radius: 8px;
    color: var(--neutral-700);
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    text-align: left;
    justify-content: space-between;
}

.btn-toggle-existing:hover {
    background: var(--neutral-100);
    border-color: var(--primary-gold);
}

.btn-toggle-existing i:last-child {
    transition: transform 0.2s ease;
}

.btn-toggle-existing.expanded i:last-child {
    transform: rotate(180deg);
}

.existing-evidence-list {
    margin-top: 0.75rem;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.existing-file-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem;
    background: white;
    border: 1px solid var(--neutral-200);
    border-radius: 6px;
    transition: all 0.2s ease;
}

.existing-file-item:hover {
    border-color: var(--primary-gold);
    box-shadow: var(--shadow-sm);
}

.file-icon {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--neutral-50);
    border-radius: 6px;
    flex-shrink: 0;
}

.file-icon i {
    font-size: 1.125rem;
}

.file-details {
    flex: 1;
    min-width: 0;
}

.file-name {
    display: block;
    font-weight: 600;
    color: var(--neutral-800);
    font-size: 0.875rem;
    margin-bottom: 0.125rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.file-date {
    font-size: 0.75rem;
    color: var(--neutral-500);
}

.file-view-btn {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--primary-gold);
    color: white;
    border-radius: 6px;
    text-decoration: none;
    transition: all 0.2s ease;
    flex-shrink: 0;
}

.file-view-btn:hover {
    background: var(--primary-gold-dark);
    color: white;
    transform: scale(1.05);
}

.evidence-inputs {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.evidence-input-row {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
}

.file-input-wrapper {
    flex: 1;
    position: relative;
}

.evidence-file-input {
    position: absolute;
    opacity: 0;
    width: 100%;
    height: 100%;
    cursor: pointer;
}

.file-input-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 2rem 1rem;
    border: 2px dashed var(--neutral-300);
    border-radius: 8px;
    background: var(--neutral-50);
    text-align: center;
    transition: all 0.2s ease;
    cursor: pointer;
}

.file-input-placeholder:hover {
    border-color: var(--primary-gold);
    background: #FFFBF0;
}

.file-input-placeholder i {
    font-size: 2rem;
    color: var(--neutral-400);
    margin-bottom: 0.5rem;
}

.file-input-placeholder span {
    font-weight: 600;
    color: var(--neutral-700);
    margin-bottom: 0.25rem;
}

.file-input-placeholder small {
    color: var(--neutral-500);
    font-size: 0.75rem;
}

.file-input-wrapper.has-file .file-input-placeholder {
    border-color: var(--success-green);
    background: #F0FDF4;
}

.file-input-wrapper.has-file .file-input-placeholder i {
    color: var(--success-green);
}

.file-description-wrapper {
    flex: 1;
}

.evidence-description-input {
    width: 100%;
    height: 40px;
    border: 1px solid var(--neutral-300);
    border-radius: 8px;
    padding: 0 1rem;
    font-size: 0.875rem;
    color: var(--neutral-800);
    background: white;
    transition: all 0.2s ease;
    outline: none;
}

.evidence-description-input:focus {
    border-color: var(--primary-gold);
    box-shadow: 0 0 0 2px rgba(212, 175, 55, 0.1);
}

.btn-remove-evidence {
    width: 40px;
    height: 40px;
    border: 1px solid var(--error-red);
    background: white;
    color: var(--error-red);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    flex-shrink: 0;
}

.btn-remove-evidence:hover {
    background: var(--error-red);
    color: white;
}

.btn-add-evidence {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: white;
    border: 1px solid var(--primary-gold);
    color: var(--primary-gold);
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-size: 0.875rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    margin-top: 1rem;
}

.btn-add-evidence:hover {
    background: var(--primary-gold);
    color: white;
}

/* Existing Evidence Display */
.existing-evidence {
    margin-bottom: 1.5rem;
}

.existing-evidence-title {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1rem;
    font-weight: 600;
    color: var(--neutral-700);
    margin-bottom: 1rem;
}

.existing-evidence-title i {
    color: var(--primary-gold);
}

.evidence-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 0.75rem;
    margin-bottom: 1rem;
}

.evidence-item-compact {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem;
    background: var(--neutral-50);
    border: 1px solid var(--neutral-200);
    border-radius: 8px;
    transition: all 0.2s ease;
}

.evidence-item-compact:hover {
    background: white;
    border-color: var(--primary-gold);
    box-shadow: var(--shadow-sm);
}

.evidence-icon {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: white;
    border-radius: 8px;
    border: 1px solid var(--neutral-200);
    flex-shrink: 0;
}

.evidence-icon i {
    font-size: 1.25rem;
}

.evidence-info {
    flex: 1;
    min-width: 0;
}

.evidence-name {
    display: block;
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--neutral-800);
    margin-bottom: 0.25rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.evidence-date {
    font-size: 0.75rem;
    color: var(--neutral-500);
}

.evidence-view {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--primary-gold);
    color: white;
    border-radius: 6px;
    text-decoration: none;
    transition: all 0.2s ease;
    flex-shrink: 0;
}

.evidence-view:hover {
    background: var(--primary-gold-dark);
    color: white;
    transform: scale(1.05);
}

.evidence-separator {
    border: none;
    height: 1px;
    background: var(--neutral-200);
    margin: 1.5rem 0;
}

/* Responsive Evidence Grid */
@media (max-width: 768px) {
    .evidence-grid {
        grid-template-columns: 1fr;
    }

    .evidence-item-compact {
        padding: 0.5rem;
    }

    .evidence-icon {
        width: 32px;
        height: 32px;
    }

    .evidence-icon i {
        font-size: 1rem;
    }
}
</style>
@endsection

<script>
// Calculate position totals
function updatePositionTotal(positionId) {
    const inputs = document.querySelectorAll(`input[data-position="${positionId}"]`);
    let total = 0;
    
    inputs.forEach(input => {
        const value = parseInt(input.value) || 0;
        total += value;
    });
    
    document.getElementById(`total-${positionId}`).textContent = total.toLocaleString();
}

// Initialize totals on page load
document.addEventListener('DOMContentLoaded', function() {
    @foreach($positions as $position)
        updatePositionTotal({{ $position->id }});
    @endforeach
});

// Form validation
document.getElementById('voteForm').addEventListener('submit', function(e) {
    const inputs = document.querySelectorAll('.vote-input');
    let hasVotes = false;
    inputs.forEach(input => {
        if (parseInt(input.value) > 0) {
            hasVotes = true;
        }
    });
    if (!hasVotes) {
        e.preventDefault();
        alert('Please enter at least one vote before submitting.');
        return false;
    }

    // Check for evidence files
    const evidenceFileInput = document.getElementById('evidenceFileInput');
    const hasEvidence = evidenceFileInput && evidenceFileInput.files && evidenceFileInput.files.length > 0;

    const evidenceText = hasEvidence ? ` and ${evidenceFileInput.files.length} evidence file(s)` : '';
    const message = `Submit votes${evidenceText} for {{ $station->name }}?`;

    if (!confirm(message)) {
        e.preventDefault();
        return false;
    }

    // Debug: Log form data
    console.log('Form submission:', {
        action: this.action,
        method: this.method,
        hasEvidence: hasEvidence,
        evidenceFileCount: hasEvidence ? evidenceFileInput.files.length : 0,
        selectedFilesCount: selectedFiles.length,
        formData: new FormData(this)
    });

    // Ensure file names are updated before submission
    updateFileNamesInputs();
});

// Compact Evidence Upload Functions
let selectedFiles = [];
let selectedEvidenceType = '';

function handleCompactFileSelect(input) {
    const files = Array.from(input.files);
    let validFiles = [];

    // Don't clear previous selections - allow accumulating files
    // selectedFiles = []; // Removed to allow multiple file additions

    files.forEach(file => {
        // Check if file is already selected (prevent duplicates)
        const isDuplicate = selectedFiles.some(selectedFile =>
            selectedFile.name === file.name && selectedFile.size === file.size
        );

        if (isDuplicate) {
            alert(`File "${file.name}" is already selected.`);
            return;
        }
        // Check file size (50MB limit for videos, 5MB for other files)
        const isVideo = file.type.startsWith('video/');
        const maxSize = isVideo ? 50 * 1024 * 1024 : 5 * 1024 * 1024; // 50MB for videos, 5MB for others

        if (file.size > maxSize) {
            const maxSizeText = isVideo ? '50MB' : '5MB';
            alert(`File "${file.name}" is too large. Maximum size is ${maxSizeText}.`);
            return;
        }

        // Check file type
        const allowedTypes = [
            'image/jpeg', 'image/jpg', 'image/png', 'application/pdf',
            'video/mp4', 'video/quicktime', 'video/x-msvideo', 'video/x-ms-wmv',
            'video/x-flv', 'video/webm'
        ];
        if (!allowedTypes.includes(file.type)) {
            alert(`File "${file.name}" is not supported. Please use JPG, PNG, PDF, MP4, MOV, AVI, WMV, FLV, or WEBM files.`);
            return;
        }

        validFiles.push(file);

        // Add to selected files for display
        const fileObj = {
            file: file,
            name: file.name,
            size: file.size,
            type: file.type,
            description: selectedEvidenceType || file.name.replace(/\.[^/.]+$/, "")
        };

        selectedFiles.push(fileObj);
    });

    updateCompactFilesDisplay();
    updateFileDescriptions();
    updateFileNamesInputs();
    updateFileInput();

    console.log('Files selected:', selectedFiles.length, 'Valid files:', validFiles.length);
}

// Function to update the file input with all selected files
function updateFileInput() {
    const fileInput = document.getElementById('evidenceFileInput');
    const dataTransfer = new DataTransfer();

    // Add all selected files to the DataTransfer object
    selectedFiles.forEach(fileObj => {
        dataTransfer.items.add(fileObj.file);
    });

    // Update the file input's files property
    fileInput.files = dataTransfer.files;
}

function selectCompactType(type) {
    selectedEvidenceType = type;

    // Update button states
    document.querySelectorAll('.type-pill').forEach(btn => {
        btn.classList.remove('selected');
    });

    event.target.closest('.type-pill').classList.add('selected');

    // Update descriptions for existing files
    selectedFiles.forEach(fileObj => {
        if (!fileObj.description || fileObj.description === fileObj.name.replace(/\.[^/.]+$/, "")) {
            fileObj.description = type;
        }
    });

    updateFileDescriptions();
    updateFileNamesInputs();
}

function updateCompactFilesDisplay() {
    const selectedFilesDiv = document.getElementById('selectedFilesCompact');
    const filesList = document.getElementById('filesCompactList');
    const clearButton = document.querySelector('.btn-clear-files');
    const addButton = document.querySelector('.btn-add-files span');

    if (selectedFiles.length === 0) {
        selectedFilesDiv.style.display = 'none';
        if (clearButton) clearButton.style.display = 'none';
        if (addButton) addButton.textContent = 'Add Files';
        return;
    }

    selectedFilesDiv.style.display = 'flex';
    if (clearButton) clearButton.style.display = 'flex';
    if (addButton) addButton.textContent = `Add Files (${selectedFiles.length})`;
    filesList.innerHTML = '';

    selectedFiles.forEach((fileObj, index) => {
        const fileItem = document.createElement('div');
        fileItem.className = 'file-compact-item';

        const isPDF = fileObj.type === 'application/pdf';
        const isVideo = fileObj.type.startsWith('video/');
        let iconClass = 'bi-image'; // default for images

        if (isPDF) {
            iconClass = 'bi-file-pdf';
        } else if (isVideo) {
            iconClass = 'bi-camera-video';
        }

        fileItem.innerHTML = `
            <i class="file-compact-icon bi ${iconClass}"></i>
            <span class="file-compact-name" title="${fileObj.name}">${fileObj.name}</span>
            <i class="file-compact-remove bi bi-x" onclick="removeCompactFile(${index})"></i>
        `;

        filesList.appendChild(fileItem);
    });
}

function removeCompactFile(index) {
    selectedFiles.splice(index, 1);
    updateCompactFilesDisplay();
    updateFileDescriptions();
    updateFileNamesInputs();
    updateFileInput();
}

function clearAllCompactFiles() {
    selectedFiles = [];
    updateCompactFilesDisplay();
    updateFileDescriptions();
    updateFileNamesInputs();
    updateFileInput();

    // Reset evidence type selection
    selectedEvidenceType = '';
    document.querySelectorAll('.type-pill').forEach(btn => {
        btn.classList.remove('selected');
    });
}

// Function to update hidden file names inputs
function updateFileNamesInputs() {
    const container = document.getElementById('fileNamesContainer');
    container.innerHTML = '';

    selectedFiles.forEach((fileObj, index) => {
        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = `file_names[${index}]`;
        input.value = fileObj.description;
        container.appendChild(input);
    });

    console.log('Updated file names inputs:', selectedFiles.length, 'files');
}

function toggleCompactExisting() {
    const existingDiv = document.getElementById('existingCompact');
    const button = document.querySelector('.btn-view-existing');

    if (existingDiv.style.display === 'none') {
        existingDiv.style.display = 'flex';
        button.classList.add('active');
    } else {
        existingDiv.style.display = 'none';
        button.classList.remove('active');
    }
}

function updateFileDescriptions() {
    // Remove existing description inputs
    document.querySelectorAll('.dynamic-description-input').forEach(input => input.remove());

    if (selectedFiles.length === 0) {
        return;
    }

    // Create hidden inputs for file descriptions
    const form = document.getElementById('voteForm');

    selectedFiles.forEach((fileObj, index) => {
        const descInput = document.createElement('input');
        descInput.type = 'hidden';
        descInput.name = 'file_names[]';
        descInput.value = fileObj.description || fileObj.name.replace(/\.[^/.]+$/, "");
        descInput.className = 'dynamic-description-input';

        form.appendChild(descInput);
    });
}
</script>
@endsection
