@extends('layouts.app')

@section('styles')
<style>
/* Statistics Cards */
.stat-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    border: 1px solid #e5e7eb;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.stat-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.stat-card-body {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.stat-icon {
    width: 3rem;
    height: 3rem;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    flex-shrink: 0;
}

.stat-card-primary .stat-icon {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.stat-card-success .stat-icon {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.stat-card-warning .stat-icon {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.stat-card-info .stat-icon {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
}

.stat-content {
    flex: 1;
}

.stat-number {
    font-size: 2.25rem;
    font-weight: 800;
    color: #111827;
    line-height: 1;
    margin-bottom: 0.25rem;
}

.stat-label {
    font-size: 0.875rem;
    font-weight: 600;
    color: #6b7280;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-bottom: 0.5rem;
}

.stat-trend {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    font-size: 0.75rem;
    color: #9ca3af;
}

.stat-card-footer {
    display: flex;
    justify-content: flex-end;
}

.stat-percentage {
    text-align: center;
    padding: 0.75rem;
}

.percentage-text {
    display: block;
    font-size: 1.5rem;
    font-weight: 700;
    color: #f59e0b;
    margin-bottom: 0.25rem;
}

.stat-card-success .percentage-text {
    color: #10b981;
}

.stat-card-warning .percentage-text {
    color: #f59e0b;
}

.stat-card-info .percentage-text {
    color: #3b82f6;
}

.percentage-label {
    font-size: 0.75rem;
    color: #9ca3af;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    font-weight: 600;
}

@media (max-width: 640px) {
    .stat-card {
        padding: 1rem;
    }

    .stat-card-body {
        flex-direction: column;
        text-align: center;
        gap: 0.75rem;
    }
}
/* Enhanced Toast Styles */
.vote-update-toast {
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.vote-update-toast:hover {
    transform: translateX(-5px) !important;
    box-shadow: 0 12px 40px rgba(40, 167, 69, 0.4) !important;
}

/* Evidence Display Styles */
.evidence-container {
    min-width: 180px;
}

.latest-evidence-preview {
    max-height: 100px;
    overflow-y: auto;
}

.evidence-item {
    background: rgba(248, 250, 252, 0.9);
    border: 1px solid rgba(226, 232, 240, 0.8);
}

.evidence-info {
    min-width: 0;
}

.btn-xs {
    padding: 2px 6px;
    font-size: 0.7rem;
    line-height: 1.2;
}

.badge-sm {
    font-size: 0.6rem;
    padding: 1px 4px;
}

.evidence-actions .btn-xs {
    min-width: 24px;
    height: 24px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.evidence-item:hover {
    background: rgba(241, 245, 249, 0.9);
    border-color: rgba(203, 213, 225, 0.8);
}

/* Compact Dashboard Improvements */
.dashboard-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 24px;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.dashboard-title {
    font-size: 1.75rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 4px;
}

.dashboard-subtitle {
    color: #6c757d;
    font-size: 0.95rem;
    margin-bottom: 0;
}

/* Compact Stats Cards */
.quick-stat {
    transition: all 0.3s ease;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.quick-stat:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

/* Live indicator pulse */
.badge.bg-success {
    animation: pulse-green 2s infinite;
}

@keyframes pulse-green {
    0% { box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.7); }
    70% { box-shadow: 0 0 0 10px rgba(40, 167, 69, 0); }
    100% { box-shadow: 0 0 0 0 rgba(40, 167, 69, 0); }
}

/* Compact chart containers */
.chart-container {
    position: relative;
    height: 300px;
    margin: 15px 0;
}

/* Enhanced notification styles */
#vote-toast-container .vote-update-toast {
    font-family: 'Poppins', sans-serif;
}

/* Professional Compact Dashboard Card Styles */
.dashboard-card {
    border-radius: 10px;
    border: none;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    transition: all 0.3s ease;
    overflow: hidden;
}

.dashboard-card:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.dashboard-card .card-header {
    border-radius: 10px 10px 0 0 !important;
    border-bottom: none;
    font-size: 0.9rem;
}

.hover-item:hover {
    background-color: rgba(0,0,0,0.04) !important;
    transform: translateX(1px);
}

/* Main Dashboard Three-Column Layout */
.main-dashboard-section {
    margin-bottom: 2rem;
}

.main-dashboard-section .col-lg-4 {
    flex: 0 0 33.333333%;
    max-width: 33.333333%;
}

/* Custom scrollbar for compact sections */
.card-body::-webkit-scrollbar {
    width: 3px;
}

.card-body::-webkit-scrollbar-track {
    background: rgba(0,0,0,0.03);
    border-radius: 2px;
}

.card-body::-webkit-scrollbar-thumb {
    background: rgba(0,0,0,0.15);
    border-radius: 2px;
}

.card-body::-webkit-scrollbar-thumb:hover {
    background: rgba(0,0,0,0.25);
}

/* Compact typography */
.dashboard-card .fw-bold {
    font-weight: 600 !important;
}

.dashboard-card .text-muted {
    color: #6c757d !important;
}

/* Evidence buttons styling */
.view-evidence-btn {
    transition: all 0.2s ease;
}

.view-evidence-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(13, 110, 253, 0.3);
}

.download-evidence-btn {
    transition: all 0.2s ease;
}

.download-evidence-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(25, 135, 84, 0.3);
}

/* Responsive improvements */
@media (max-width: 768px) {
    .dashboard-header {
        padding: 15px;
        margin-bottom: 20px;
    }

    .dashboard-title {
        font-size: 1.5rem;
    }

    #vote-toast-container {
        right: 10px !important;
        max-width: calc(100vw - 20px) !important;
    }

    .vote-update-toast {
        padding: 12px 16px !important;
    }

    .col-lg-4, .col-lg-8 {
        margin-bottom: 1rem;
    }

    /* First section mobile responsive fixes */
    .main-dashboard-section .col-lg-4 {
        flex: 0 0 100%;
        max-width: 100%;
    }

    .dashboard-card {
        min-height: auto !important;
    }

    .dashboard-card .card-body {
        padding: 1rem !important;
    }

    /* Chart container responsive */
    .dashboard-card canvas {
        max-width: 250px !important;
        max-height: 250px !important;
    }

    /* Compact text on mobile */
    .dashboard-card .fw-bold {
        font-size: 0.9rem !important;
    }

    .dashboard-card .text-muted {
        font-size: 0.75rem !important;
    }

    /* Button adjustments */
    .dashboard-card .btn-sm {
        font-size: 0.7rem !important;
        padding: 0.25rem 0.5rem !important;
    }
}

@media (max-width: 576px) {
    /* Extra small screens */
    .main-dashboard-section {
        margin-bottom: 1rem;
    }

    .dashboard-card {
        margin-bottom: 1rem;
    }

    .dashboard-card .card-header {
        padding: 0.75rem !important;
    }

    .dashboard-card .card-header h6 {
        font-size: 0.85rem !important;
    }

    /* Chart adjustments for very small screens */
    .dashboard-card canvas {
        max-width: 200px !important;
        max-height: 200px !important;
    }

    /* Progress bars */
    .progress {
        height: 3px !important;
    }

    /* Badges */
    .badge {
        font-size: 0.65rem !important;
    }
}

    .dashboard-card {
        min-height: auto !important;
    }
}
</style>
@endsection

@section('content')
<div class="container">

   
    <!-- Main Dashboard Content -->
    <div class="row g-3 main-dashboard-section">
        <!-- Section 1: Preferred Candidate -->
        <div class="col-lg-4 col-md-6 col-sm-12 mb-3">
            <div class="card dashboard-card h-100" style="min-height: 380px;">
                <div class="card-header bg-gradient text-white text-center py-2" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                    <h6 class="mb-0 fw-bold">
                        <i class="bi bi-star-fill me-2"></i>Preferred Candidate
                    </h6>
                </div>
                <div class="card-body p-2 d-flex flex-column">
                    <div class="flex-grow-1 d-flex align-items-center justify-content-center">
                        <div style="position: relative; width: 100%; max-width: 300px; height: 300px; margin: 0 auto;">
                            <canvas id="preferredCandidateChart"></canvas>
                            <div class="position-absolute top-50 start-50 translate-middle text-center">
                                @php
                                    // Find preferred candidate
                                    $preferredCandidate = null;
                                    $preferredVotes = 0;
                                    $preferredPosition = null;

                                    // Find leading candidate
                                    $leadingCandidate = null;
                                    $leadingVotes = 0;
                                    $leadingPosition = null;

                                    // Find both preferred and leading candidates
                                    foreach($positions as $position) {
                                        foreach($position->candidates as $candidate) {
                                            $candidateVotes = $candidate->totalVotes();

                                            // Check if this is a preferred candidate
                                            if ($candidate->isPreferred()) {
                                                $preferredCandidate = $candidate;
                                                $preferredVotes = $candidateVotes;
                                                $preferredPosition = $position;
                                            }

                                            // Check if this is the leading candidate overall
                                            if ($candidateVotes > $leadingVotes) {
                                                $leadingCandidate = $candidate;
                                                $leadingVotes = $candidateVotes;
                                                $leadingPosition = $position;
                                            }
                                        }
                                    }

                                    // Determine if preferred is also leading
                                    $isPreferredLeading = ($preferredCandidate && $leadingCandidate && $preferredCandidate->id === $leadingCandidate->id);

                                    // Calculate percentage for preferred candidate
                                    $totalPositionVotes = 0;
                                    if ($isPreferredLeading) {
                                        // If preferred is leading, calculate against total votes
                                        if ($preferredPosition) {
                                            foreach($preferredPosition->candidates as $c) {
                                                $totalPositionVotes += $c->totalVotes();
                                            }
                                        }
                                        $preferredPercentage = ($totalPositionVotes > 0) ? round(($preferredVotes/$totalPositionVotes * 100), 1) : 0;
                                    } else {
                                        // If not leading, calculate against leading candidate
                                        $totalVotes = $preferredVotes + $leadingVotes;
                                        $preferredPercentage = ($totalVotes > 0) ? round(($preferredVotes/$totalVotes * 100), 1) : 0;
                                    }

                                    // Get position (1st, 2nd, 3rd, etc.)
                                    $candidateRank = 0;
                                    if ($preferredCandidate && $preferredPosition) {
                                        $sortedCandidates = $preferredPosition->candidates->sortByDesc(function($c) {
                                            return $c->totalVotes();
                                        });

                                        foreach($sortedCandidates as $index => $c) {
                                            if ($c->id === $preferredCandidate->id) {
                                                $candidateRank = $index + 1;
                                                break;
                                            }
                                        }
                                    }

                                    // Format rank as 1st, 2nd, 3rd, etc.
                                    $rankSuffix = 'th';
                                    if ($candidateRank % 10 == 1 && $candidateRank % 100 != 11) {
                                        $rankSuffix = 'st';
                                    } elseif ($candidateRank % 10 == 2 && $candidateRank % 100 != 12) {
                                        $rankSuffix = 'nd';
                                    } elseif ($candidateRank % 10 == 3 && $candidateRank % 100 != 13) {
                                        $rankSuffix = 'rd';
                                    }
                                    $rankFormatted = $candidateRank . $rankSuffix;
                                @endphp

                                @if($preferredCandidate)
                                <div class="mb-1">
                                    @if($isPreferredLeading)
                                    <span class="badge bg-success px-1 py-1" style="font-size: 0.65rem;">
                                        <i class="bi bi-trophy-fill me-1"></i>{{ $rankFormatted }}
                                    </span>
                                    @else
                                    <span class="badge bg-primary bg-opacity-20 text-primary px-1 py-1" style="font-size: 0.65rem;">
                                        <i class="bi bi-star-fill me-1"></i>{{ $rankFormatted }}
                                    </span>
                                    @endif
                                </div>
                                <div class="fw-bold" style="font-size: 1.3rem; line-height: 1.2;">{{ $preferredPercentage }}%</div>
                                <div class="text-muted" style="font-size: 0.7rem;">vs {{ $isPreferredLeading ? 'others' : 'leader' }}</div>
                                <div class="mt-1 fw-bold" style="font-size: 0.8rem;">{{ $preferredCandidate->name }}</div>
                                <div class="text-muted" style="font-size: 0.65rem;">{{ $preferredPosition->name }} - {{ number_format($preferredVotes) }} votes</div>
                                @else
                                <div class="text-muted">No preferred candidate set</div>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Section 2: Overall Results -->
        <div class="col-lg-4 col-md-6 col-sm-12 mb-3">
            <div class="card dashboard-card h-100" style="min-height: 380px;">
                <div class="card-header bg-gradient text-white py-4" style="background: linear-gradient(135deg, #4a89dc, #5d9cec);">
                    <div class="d-flex justify-content-between align-items-center">
                        <h6 class="mb-0 fw-bold">
                            <i class="bi bi-trophy-fill me-2"></i>Overall Results
                        </h6>
                        <span class="badge bg-white text-primary px-2 py-1" style="font-size: 0.65rem;">
                            <i class="bi bi-award me-1"></i>Top 4
                        </span>
                    </div>
                </div>

                @php
                    // Get all candidates across all positions
                    $allCandidates = collect();
                    foreach ($positions as $position) {
                        foreach ($position->candidates as $candidate) {
                            $votes = $candidate->totalVotes();
                            $allCandidates->push([
                                'candidate' => $candidate,
                                'position' => $position,
                                'votes' => $votes,
                                'percentage' => ($position->totalVotes() > 0) ? round(($votes/$position->totalVotes() * 100), 1) : 0
                            ]);
                        }
                    }

                    // Sort by votes (descending) and take top 3
                    $topCandidates = $allCandidates->sortByDesc('votes')->take(4);
                    $colors = [
                        ['gradient' => 'linear-gradient(135deg, #FFD700, #FFA500)', 'progress' => 'bg-warning'],
                        ['gradient' => 'linear-gradient(135deg, #C0C0C0, #A9A9A9)', 'progress' => 'bg-secondary'],
                        ['gradient' => 'linear-gradient(135deg, #CD7F32, #8B4513)', 'progress' => 'bg-dark']
                    ];
                @endphp

                <div class="card-body p-2" style="max-height: 320px; overflow-y: auto;">
                    @foreach ($topCandidates as $index => $item)
                    <div class="d-flex align-items-center py-1 px-2 mb-2 rounded hover-item" style="transition: all 0.2s ease; background: rgba(0,0,0,0.02);">
                        <div class="me-2">
                             <span class="badge rounded-circle d-flex align-items-center justify-content-center"
                                   style="background: {{ $colors[min($index, 2)]['gradient'] }}; width: 20px; height: 20px; color: white; font-size: 0.7rem; font-weight: bold;">
                                 {{ $loop->iteration }}
                             </span>
                        </div>

                        <div class="flex-grow-1">
                            @php
                                // Get candidate initials (up to 2 characters)
                                $nameParts = explode(' ', $item['candidate']->name);
                                $initials = '';
                                if (count($nameParts) >= 2) {
                                    $initials = strtoupper(substr($nameParts[0], 0, 1) . substr($nameParts[count($nameParts)-1], 0, 1));
                                } else {
                                    $initials = strtoupper(substr($item['candidate']->name, 0, 2));
                                }
                            @endphp

                            <div class="d-flex align-items-center mb-1">
                                <div class="rounded-circle d-flex align-items-center justify-content-center me-2"
                                     style="width: 24px; height: 24px; background: {{ $colors[min($index, 2)]['gradient'] }}; color: white; font-weight: bold; font-size: 0.7rem;">
                                    {{ $initials }}
                                </div>
                                <div class="flex-grow-1">
                                    <div class="d-flex align-items-center justify-content-between">
                                        <div>
                                            <div class="fw-bold" style="font-size: 0.8rem;">{{ $item['candidate']->name }}</div>
                                            <div class="text-muted" style="font-size: 0.65rem;">{{ $item['position']->name }}</div>
                                        </div>
                                        <div class="text-end">
                                            <div class="fw-bold" style="font-size: 0.8rem;">{{ number_format($item['votes']) }}</div>
                                            <div class="badge rounded-pill" style="background: {{ $colors[min($index, 2)]['gradient'] }}; font-size: 0.6rem;">
                                                {{ $item['percentage'] }}%
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="progress" style="height: 4px; border-radius: 2px; background-color: rgba(0,0,0,0.05);">
                                <div class="progress-bar {{ $colors[min($index, 2)]['progress'] }}" role="progressbar"
                                     style="width: {{ $item['percentage'] }}%; border-radius: 2px;"
                                     aria-valuenow="{{ $item['percentage'] }}" aria-valuemin="0" aria-valuemax="100">
                                </div>
                            </div>
                        </div>
                    </div>
                    @endforeach
                </div>
            </div>
        </div>

        <!-- Section 3: Preferred Candidates Monitoring -->
        <div class="col-lg-4 col-md-6 col-sm-12 mb-3">
            <div class="card dashboard-card h-100" style="min-height: 380px;">
                <div class="card-header bg-gradient text-white py-4" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
                    <div class="d-flex justify-content-between align-items-center">
                        <h6 class="mb-0 fw-bold">
                            <i class="bi bi-star-fill me-2"></i>Monitoring
                        </h6>
                        <a href="{{ route('monitoring.index') }}" class="btn btn-sm btn-outline-primary" style="font-size: 0.65rem;">
                            <i class="bi bi-gear"></i> Manage
                        </a>
                    </div>
                </div>

                @if(count($preferredCandidates) > 0)

                <div class="card-body p-2" style="max-height: 320px; overflow-y: auto;">
                    @foreach($preferredCandidates as $candidate)
                        @php
                            $gapInfo = $monitoringData[$candidate->id] ?? null;
                            if (!$gapInfo) continue;

                            $isLeading = $gapInfo['is_leading'];
                            $gap = $gapInfo['gap'];
                            $competitor = $gapInfo['competitor'];
                            $alertThreshold = $candidate->monitoring->vote_gap_alert_threshold;
                            $isAlertTriggered = $alertThreshold && $gap < $alertThreshold;

                            // Calculate percentage for visual representation
                            $totalVotes = $gapInfo['preferred_votes'] + $gapInfo['competitor_votes'];
                            $preferredPercentage = $totalVotes > 0 ? ($gapInfo['preferred_votes'] / $totalVotes) * 100 : 50;

                            // Determine status colors and icons
                            $statusColor = $isLeading ? 'success' : 'danger';
                            $statusIcon = $isLeading ? 'bi-arrow-up-circle-fill' : 'bi-arrow-down-circle-fill';
                            $alertIcon = $isAlertTriggered ? 'bi-exclamation-triangle-fill' : 'bi-shield-check';
                            $alertColor = $isAlertTriggered ? 'danger' : 'success';
                        @endphp
                        <div class="mb-2 p-2 rounded" style="background: rgba(0,0,0,0.02); border-left: 3px solid var(--bs-{{ $statusColor }});">
                            <!-- Ultra Compact Header -->
                            <div class="d-flex justify-content-between align-items-center mb-1">
                                <div class="d-flex align-items-center">
                                    <i class="bi {{ $statusIcon }} text-{{ $statusColor }} me-1" style="font-size: 1rem;"></i>
                                    <div>
                                        <div class="fw-bold" style="font-size: 0.8rem;">{{ $candidate->name }}</div>
                                        <div class="d-flex align-items-center">
                                            <small class="text-muted" style="font-size: 0.65rem;">{{ $candidate->position->name }}</small>
                                            <span class="badge bg-{{ $statusColor }} ms-1" style="font-size: 0.6rem;">
                                                @if($isLeading)
                                                    1st
                                                @else
                                                    {{ $gapInfo['preferred_position'] ?? 'N/A' }}{{ $gapInfo['preferred_position'] == 2 ? 'nd' : ($gapInfo['preferred_position'] == 3 ? 'rd' : 'th') }}
                                                @endif
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <a href="{{ route('monitoring.compare', $candidate) }}" class="btn btn-sm btn-outline-{{ $statusColor }}" style="font-size: 0.65rem; padding: 2px 6px;">
                                    <i class="bi bi-bar-chart-line"></i>
                                </a>
                            </div>

                            <!-- Ultra Compact Vote Information -->
                            <div class="row mb-1">
                                <div class="col-6">
                                    <div class="text-center">
                                        <div class="text-muted" style="font-size: 0.65rem;">Yours</div>
                                        <div class="fw-bold" style="font-size: 0.85rem;">{{ number_format($gapInfo['preferred_votes']) }}</div>
                                    </div>
                                </div>
                                @if($competitor)
                                <div class="col-6">
                                    <div class="text-center">
                                        <div class="text-muted" style="font-size: 0.65rem;">{{ $isLeading ? '2nd' : 'Leader' }}</div>
                                        <div class="fw-bold" style="font-size: 0.85rem;">{{ number_format($gapInfo['competitor_votes']) }}</div>
                                    </div>
                                </div>
                                @endif
                            </div>

                            <!-- Ultra Compact Gap Information -->
                            @if($competitor)
                            <div class="d-flex justify-content-between align-items-center mb-1">
                                <div class="d-flex align-items-center">
                                    <i class="bi {{ $alertIcon }} text-{{ $alertColor }} me-1" style="font-size: 0.7rem;"></i>
                                    <span class="text-{{ $statusColor }}" style="font-size: 0.7rem;">
                                        {{ $isLeading ? 'Leading by' : 'Behind by' }}
                                    </span>
                                </div>
                                <div class="fw-bold text-{{ $isAlertTriggered ? 'danger' : $statusColor }}" style="font-size: 0.8rem;">
                                    {{ number_format($gap) }}
                                    @if($isAlertTriggered)
                                        <span class="badge bg-danger ms-1" style="font-size: 0.55rem;">!</span>
                                    @endif
                                </div>
                            </div>

                            <!-- Ultra Compact Progress Bar -->
                            <div class="progress" style="height: 4px; border-radius: 2px; background-color: rgba(0,0,0,0.05);">
                                <div class="progress-bar bg-{{ $statusColor }}" role="progressbar"
                                     style="width: {{ $preferredPercentage }}%; border-radius: 2px;"
                                     aria-valuenow="{{ $preferredPercentage }}" aria-valuemin="0" aria-valuemax="100">
                                </div>
                            </div>
                            @endif
                        </div>
                    @endforeach
                </div>
                @else
                <div class="card-body p-2 d-flex align-items-center justify-content-center" style="min-height: 320px;">
                    <div class="text-center">
                        <i class="bi bi-star text-muted mb-2" style="font-size: 2rem;"></i>
                        <div class="text-muted mb-2" style="font-size: 0.8rem;">No candidates selected</div>
                        <a href="{{ route('monitoring.index') }}" class="btn btn-sm btn-outline-primary" style="font-size: 0.7rem;">
                            <i class="bi bi-plus-circle me-1"></i>Add
                        </a>
                    </div>
                </div>
                @endif
            </div>
        </div>
    </div>
    
    <!-- Manager Dashboard Statistics Cards -->
    <div class="row g-4 mb-5">
        <div class="col-lg-3 col-md-6">
            <div class="stat-card stat-card-primary">
                <div class="stat-card-body">
                    <div class="stat-icon">
                        <i class="bi bi-building"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number">{{ number_format($totalStations) }}</div>
                        <div class="stat-label">Total Stations</div>
                        <div class="stat-trend">
                            <i class="bi bi-graph-up"></i>
                            <span>All registered</span>
                        </div>
                    </div>
                </div>
                <div class="stat-card-footer">
                    <div class="stat-percentage">
                        <span class="percentage-text">100%</span>
                        <small class="percentage-label">Complete</small>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6">
            <div class="stat-card stat-card-success">
                <div class="stat-card-body">
                    <div class="stat-icon">
                        <i class="bi bi-check-circle-fill"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number">{{ number_format($submittedStations) }}</div>
                        <div class="stat-label">Submitted</div>
                        <div class="stat-trend">
                            <i class="bi bi-arrow-up"></i>
                            <span>{{ $totalStations > 0 ? number_format(($submittedStations / $totalStations) * 100, 1) : 0 }}% complete</span>
                        </div>
                    </div>
                </div>
                <div class="stat-card-footer">
                    <div class="stat-percentage">
                        <span class="percentage-text">{{ $totalStations > 0 ? number_format(($submittedStations / $totalStations) * 100, 1) : 0 }}%</span>
                        <small class="percentage-label">Submitted</small>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6">
            <div class="stat-card stat-card-warning">
                <div class="stat-card-body">
                    <div class="stat-icon">
                        <i class="bi bi-clock-fill"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number">{{ number_format($pendingStations) }}</div>
                        <div class="stat-label">Pending</div>
                        <div class="stat-trend">
                            <i class="bi bi-hourglass-split"></i>
                            <span>Awaiting submission</span>
                        </div>
                    </div>
                </div>
                <div class="stat-card-footer">
                    <div class="stat-percentage">
                        <span class="percentage-text">{{ $totalStations > 0 ? number_format(($pendingStations / $totalStations) * 100, 1) : 0 }}%</span>
                        <small class="percentage-label">Pending</small>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6">
            <div class="stat-card stat-card-info">
                <div class="stat-card-body">
                    <div class="stat-icon">
                        <i class="bi bi-bar-chart-fill"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number">{{ number_format($totalVotes) }}</div>
                        <div class="stat-label">Total Votes</div>
                        <div class="stat-trend">
                            <i class="bi bi-people"></i>
                            <span>Across all stations</span>
                        </div>
                    </div>
                </div>
                <div class="stat-card-footer">
                    <div class="stat-percentage">
                        <span class="percentage-text"><i class="bi bi-infinity"></i></span>
                        <small class="percentage-label">Total Votes</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    

    
    <!-- Simple Vote Trends Graph Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="section-header d-flex align-items-center justify-content-between mb-3">
                <div class="d-flex align-items-center">
                    <div class="section-icon-container me-2" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); width: 40px; height: 40px; display: flex; align-items: center; justify-content: center; border-radius: 12px; box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);">
                        <i class="bi bi-graph-up text-white" style="font-size: 1.2rem;"></i>
                    </div>
                    <div>
                        <h4 class="section-title mb-0 fw-bold">Vote Trends Over Time</h4>
                        <small class="text-muted">Track candidate vote counts throughout the day</small>
                    </div>
                </div>

                <!-- Simple Controls -->
                <div class="d-flex align-items-center gap-2">
                    <select class="form-select form-select-sm" id="positionFilter" style="width: auto;">
                        <option value="overall">All Positions</option>
                        @foreach($positions as $position)
                            <option value="{{ $position->id }}">{{ $position->name }}</option>
                        @endforeach
                    </select>

                    <button class="btn btn-outline-secondary btn-sm" id="refreshChart" title="Refresh Data">
                        <i class="bi bi-arrow-clockwise"></i>
                    </button>
                </div>
            </div>

            <div class="card bg-white rounded shadow-sm border-0" style="box-shadow: 0 10px 30px rgba(0,0,0,0.1) !important;">
                <div class="card-body p-4">
                    <!-- Simple Vote Trends Chart -->
                    <div class="vote-trends-chart">
                        <div class="chart-container" style="position: relative; height: 400px; background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%); border-radius: 8px; padding: 10px;">
                            <canvas id="voteTrendsChart"></canvas>
                        </div>
                    </div>
                        

                    <!-- Simple Chart Summary -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card border-0 bg-light">
                                <div class="card-body p-3">
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <h6 class="mb-0 fw-bold">
                                            <i class="bi bi-info-circle text-primary me-1"></i>
                                            Chart Information
                                        </h6>
                                        <div class="d-flex gap-2">
                                            <span class="badge bg-success">Live</span>
                                            <small class="text-muted" id="lastUpdated">Updated: Just now</small>
                                        </div>
                                    </div>

                                    <!-- Simple Stats Row -->
                                    <div class="row">
                                        <div class="col-md-3 text-center">
                                            <div class="quick-stat">
                                                <i class="bi bi-clock text-primary mb-1" style="font-size: 1.5rem;"></i>
                                                <div class="fw-bold">{{ date('H:i') }}</div>
                                                <small class="text-muted">Current Time</small>
                                            </div>
                                        </div>
                                        <div class="col-md-3 text-center">
                                            <div class="quick-stat">
                                                <i class="bi bi-people text-success mb-1" style="font-size: 1.5rem;"></i>
                                                <div class="fw-bold" id="totalVotes">{{ number_format(\App\Models\Vote::sum('number_of_votes')) }}</div>
                                                <small class="text-muted">Total Votes</small>
                                            </div>
                                        </div>
                                        <div class="col-md-3 text-center">
                                            <div class="quick-stat">
                                                <i class="bi bi-geo-alt text-info mb-1" style="font-size: 1.5rem;"></i>
                                                <div class="fw-bold">{{ $totalStations ?? 0 }}</div>
                                                <small class="text-muted">Stations</small>
                                            </div>
                                        </div>
                                        <div class="col-md-3 text-center">
                                            <div class="quick-stat">
                                                <i class="bi bi-graph-up text-warning mb-1" style="font-size: 1.5rem;"></i>
                                                <div class="fw-bold">Real-time</div>
                                                <small class="text-muted">Updates</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    @include('partials.home.map')
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    
</div>

@endsection

@section('styles')

<link rel="stylesheet" href="{{ asset('css/custom.css') }}">

@endsection

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Polling stations table moved to dedicated page

        // --- End Polling Stations Table Logic ---

        // --- Station Cards Filtering Logic ---
        const positionFilter = document.getElementById('positionFilter');

        // Add event listener to position filter (with null check)
        if (positionFilter) {
            positionFilter.addEventListener('change', function() {
                filterByPosition();
            });
        }
        
        // Function to filter dashboard by position
        function filterByPosition() {
            const selectedPosition = positionFilter ? positionFilter.value : 'all';
            
            // Show/hide results based on position
            if (selectedPosition === 'all') {
                document.querySelectorAll('.results-card-compact').forEach(function(card) {
                    card.closest('.col-12').style.display = '';
                });
            } else {
                const positionId = selectedPosition.replace('position-', '');
                document.querySelectorAll('.results-card-compact').forEach(function(card) {
                    const cardPositionId = card.getAttribute('data-position-id');
                    if (cardPositionId === positionId) {
                        card.closest('.col-12').style.display = '';
                    } else {
                        card.closest('.col-12').style.display = 'none';
                    }
                });
            }
            
            // Filter station cards to show only the selected position data
            stationCards.forEach(function(card) {
                if (selectedPosition === 'all') {
                    // Show all position tables
                    card.querySelectorAll('.position-votes-table').forEach(function(table) {
                        table.closest('.position-section').style.display = '';
                    });
                } else {
                    // Show only selected position tables
                    const positionId = selectedPosition.replace('position-', '');
                    card.querySelectorAll('.position-votes-table').forEach(function(table) {
                        const tablePositionId = table.getAttribute('data-position-id');
                        if (tablePositionId === positionId) {
                            table.closest('.position-section').style.display = '';
                        } else {
                            table.closest('.position-section').style.display = 'none';
                        }
                    });
                }
            });
        }
        
        // Function to filter stations
        function filterStations() {
            const searchTerm = searchInput ? searchInput.value.toLowerCase() : '';
            const filterValue = filterSelect ? filterSelect.value : 'all';
            
            stationCards.forEach(function(card) {
                let showCard = true;
                
                // Filter by search term
                if (searchTerm) {
                    const stationName = card.querySelector('.station-name').textContent.toLowerCase();
                    if (!stationName.includes(searchTerm)) {
                        showCard = false;
                    }
                }
                
                // Apply additional filters
                if (showCard && filterValue !== 'all') {
                    switch(filterValue) {
                        case 'with-evidence':
                            if (!card.querySelector('.evidence-list') || card.querySelectorAll('.evidence-item').length === 0) {
                                showCard = false;
                            }
                            break;
                        case 'without-evidence':
                            if (card.querySelector('.evidence-list') && card.querySelectorAll('.evidence-item').length > 0) {
                                showCard = false;
                            }
                            break;
                        case 'complete':
                            // Check if all positions have votes reported
                            const positionTables = card.querySelectorAll('.position-votes-table');
                            let allComplete = true;
                            positionTables.forEach(function(table) {
                                const hasVotes = table.querySelector('tbody tr:not(.no-results)');
                                if (!hasVotes) {
                                    allComplete = false;
                                }
                            });
                            if (!allComplete) {
                                showCard = false;
                            }
                            break;
                        case 'incomplete':
                            // Check if any positions are missing votes
                            const tables = card.querySelectorAll('.position-votes-table');
                            let hasIncomplete = false;
                            tables.forEach(function(table) {
                                const hasVotes = table.querySelector('tbody tr:not(.no-results)');
                                if (!hasVotes) {
                                    hasIncomplete = true;
                                }
                            });
                            if (!hasIncomplete) {
                                showCard = false;
                            }
                            break;
                    }
                }
                
                // Show or hide the card
                if (showCard) {
                    card.style.display = '';
                } else {
                    card.style.display = 'none';
                }
            });
            
            // Show message if no results
            const visibleCards = document.querySelectorAll('.station-card[style=""]');
            const noResultsMessage = document.getElementById('noStationsMessage');
            
            if (visibleCards.length === 0) {
                // Create message if it doesn't exist
                if (!noResultsMessage) {
                    const message = document.createElement('div');
                    message.id = 'noStationsMessage';
                    message.className = 'col-12 text-center py-5';
                    message.innerHTML = `
                        <div class="empty-state">
                            <i class="bi bi-search text-muted mb-3" style="font-size: 2rem;"></i>
                            <h5>No polling stations found</h5>
                            <p class="text-muted">Try adjusting your search or filter criteria</p>
                            <button class="btn btn-sm filter-btn" onclick="document.getElementById('stationSearchInput').value='';document.getElementById('stationFilterSelect').value='all';filterStations()">
                                <i class="bi bi-arrow-counterclockwise"></i> Reset Filters
                            </button>
                        </div>
                    `;
                    document.querySelector('.row:last-child').appendChild(message);
                }
            } else if (noResultsMessage) {
                // Remove message if results exist
                noResultsMessage.remove();
            }
        }
    });
</script>
    
<!-- Load Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Wait for the document to be fully loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM fully loaded, initializing chart...');
    
    // Initialize the chart
    const initChart = () => {
        const ctx = document.getElementById('preferredCandidateChart');
        if (!ctx) {
            console.error('Chart element not found');
            return;
        }

        console.log('Initializing chart...');

        // Get data from PHP variables
        @if(isset($preferredCandidate) && isset($leadingCandidate) && $preferredCandidate && $leadingCandidate)
        const preferredVotes = {{ $preferredVotes ?? 0 }};
        const leadingVotes = {{ $leadingVotes ?? 0 }};
        const isPreferredLeading = {{ isset($isPreferredLeading) && $isPreferredLeading ? 'true' : 'false' }};

        // Set labels based on whether preferred is leading
        let firstLabel, secondLabel, firstValue, secondValue;

        if (isPreferredLeading) {
            firstLabel = '{{ $preferredCandidate ? addslashes($preferredCandidate->name) : "Preferred" }}';
            secondLabel = 'Other Candidates';
            firstValue = preferredVotes;
            secondValue = {{ $totalPositionVotes ?? 0 }} - preferredVotes;
        } else {
            firstLabel = '{{ $preferredCandidate ? addslashes($preferredCandidate->name) : "Preferred" }}';
            secondLabel = '{{ $leadingCandidate ? addslashes($leadingCandidate->name) : "Leading" }}';
            firstValue = preferredVotes;
            secondValue = leadingVotes;
        }
        @else
        // Default values if no candidates are found
        let firstLabel = 'No Data';
        let secondLabel = '';
        let firstValue = 1;
        let secondValue = 0;
        const isPreferredLeading = false;
        @endif

        const data = {
            labels: [firstLabel, secondLabel],
            datasets: [{
                data: [firstValue, secondValue],
                backgroundColor: [
                    isPreferredLeading ? 'rgba(40, 167, 69, 0.9)' : 'rgba(13, 110, 253, 0.9)',
                    isPreferredLeading ? 'rgba(108, 117, 125, 0.2)' : 'rgba(40, 167, 69, 0.9)'
                ],
                borderColor: [
                    isPreferredLeading ? 'rgba(40, 167, 69, 1)' : 'rgba(13, 110, 253, 1)',
                    isPreferredLeading ? 'rgba(108, 117, 125, 0.3)' : 'rgba(40, 167, 69, 1)'
                ],
                borderWidth: 2,
                cutout: '75%',
                borderRadius: 4,
                spacing: 2
            }]
        };

        const config = {
            type: 'doughnut',
            data: data,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: { display: false },
                    tooltip: {
                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                        titleFont: { size: 14, weight: 'bold' },
                        bodyFont: { size: 13 },
                        padding: 12,
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = context.raw || 0;
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                
                                // Use the same percentage calculation as in PHP
                                let percentage;
                                if (context.dataIndex === 0) {
                                    // For preferred candidate, use the same percentage as displayed in center
                                    percentage = {{ isset($preferredPercentage) ? $preferredPercentage : 0 }};
                                } else {
                                    // For other candidate(s), calculate the remaining percentage
                                    percentage = Math.round((value / total) * 100);
                                }
                                
                                return `${label}: ${value.toLocaleString()} votes (${percentage}%)`;
                            }
                        }
                    }
                },
                rotation: -90,
                circumference: 360,
                animation: {
                    animateRotate: true,
                    animateScale: true
                }
            }
        };

        try {
            new Chart(ctx, config);
            console.log('Chart initialized successfully');
        } catch (error) {
            console.error('Error initializing chart:', error);
        }
    };

    // Initialize the chart
    initChart();
});
</script>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Simple Chart System
        let currentChart = null;
        let currentData = null;
        let currentPosition = 'overall';

        // Initialize chart
        initializeChart();

        function initializeChart() {
            const ctx = document.getElementById('voteTrendsChart');
            if (!ctx) return;

            // Create loading indicator
            const loadingIndicator = createLoadingIndicator(ctx);

            // Load initial data
            loadChartData('overall');

            // Setup event listeners
            setupEventListeners();
        }

        function createLoadingIndicator(ctx) {
            const loadingIndicator = document.createElement('div');
            loadingIndicator.className = 'text-center py-5';
            loadingIndicator.innerHTML = `
                <div class="d-flex justify-content-center align-items-center">
                    <div class="spinner-border text-primary me-3" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <div>
                        <p class="mb-1 fw-bold">Loading ranking data...</p>
                        <small class="text-muted">Analyzing candidate trends</small>
                    </div>
                </div>
            `;
            ctx.parentNode.appendChild(loadingIndicator);
            return loadingIndicator;
        }

        function setupEventListeners() {
            // Position filter
            const positionFilter = document.getElementById('positionFilter');
            if (positionFilter) {
                positionFilter.addEventListener('change', function() {
                    currentPosition = this.value;
                    loadChartData(currentPosition);
                });
            }

            // Refresh button
            const refreshBtn = document.getElementById('refreshChart');
            if (refreshBtn) {
                refreshBtn.addEventListener('click', function() {
                    this.innerHTML = '<i class="bi bi-arrow-clockwise spin"></i>';
                    loadChartData(currentPosition);
                    setTimeout(() => {
                        this.innerHTML = '<i class="bi bi-arrow-clockwise"></i>';
                    }, 1000);
                });
            }
        }

        function loadChartData(positionId) {
            const loadingIndicator = document.querySelector('.text-center.py-5');

            fetch(`/vote-trends-data/${positionId}`)
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Network response was not ok');
                    }
                    return response.json();
                })
                .then(data => {
                    currentData = data;

                    // Remove loading indicator
                    if (loadingIndicator) {
                        loadingIndicator.remove();
                    }

                    // Create/update chart
                    createVoteTrendsChart(data);
                    updateLastUpdated();

                })
                .catch(error => {
                    console.error('Error fetching vote trends data:', error);
                    if (loadingIndicator) {
                        loadingIndicator.innerHTML = `
                            <div class="alert alert-danger">
                                <i class="bi bi-exclamation-triangle me-2"></i>
                                Failed to load vote trends data. Please try refreshing the page.
                            </div>
                        `;
                    }
                });
        }

        function createVoteTrendsChart(data) {
            const ctx = document.getElementById('voteTrendsChart');
            if (!ctx) return;

            // Destroy existing chart
            if (currentChart) {
                currentChart.destroy();
            }

            // Simple chart configuration
            const config = {
                type: 'line',
                data: {
                    labels: data.labels,
                    datasets: data.datasets.map((dataset, index) => ({
                        label: dataset.label,
                        data: dataset.data,
                        borderColor: dataset.borderColor,
                        backgroundColor: `rgba(${hexToRgb(dataset.borderColor)}, 0.1)`,
                        borderWidth: 3,
                        pointRadius: 5,
                        pointHoverRadius: 7,
                        pointBorderWidth: 2,
                        pointBorderColor: '#fff',
                        tension: 0.2,
                        fill: false
                    }))
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    interaction: {
                        intersect: false,
                        mode: 'index'
                    },
                    animation: {
                        duration: 1000,
                        easing: 'easeInOutQuart'
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return value.toLocaleString();
                                },
                                font: {
                                    size: 12,
                                    weight: 'bold'
                                }
                            },
                            title: {
                                display: true,
                                text: 'Vote Count',
                                font: {
                                    size: 14,
                                    weight: 'bold'
                                }
                            },
                            grid: {
                                color: 'rgba(0,0,0,0.1)',
                                lineWidth: 1
                            }
                        },
                        x: {
                            title: {
                                display: true,
                                text: 'Time',
                                font: {
                                    size: 14,
                                    weight: 'bold'
                                }
                            },
                            grid: {
                                color: 'rgba(0,0,0,0.05)',
                                lineWidth: 1
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            display: true,
                            position: 'top',
                            labels: {
                                usePointStyle: true,
                                padding: 20,
                                font: {
                                    size: 12,
                                    weight: 'bold'
                                }
                            }
                        },
                        tooltip: {
                            backgroundColor: 'rgba(0,0,0,0.8)',
                            titleColor: '#fff',
                            bodyColor: '#fff',
                            borderColor: 'rgba(255,255,255,0.2)',
                            borderWidth: 1,
                            cornerRadius: 8,
                            displayColors: true,
                            callbacks: {
                                title: function(context) {
                                    return 'Time: ' + context[0].label;
                                },
                                label: function(context) {
                                    return context.dataset.label + ': ' + context.raw.toLocaleString() + ' votes';
                                }
                            }
                        }
                    }
                }
            };

            currentChart = new Chart(ctx, config);
        }

        function updateLastUpdated() {
            const element = document.getElementById('lastUpdated');
            if (element) {
                element.textContent = `Updated: ${new Date().toLocaleTimeString()}`;
            }
        }

        // Utility functions

        function hexToRgb(hex) {
            const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
            return result ?
                `${parseInt(result[1], 16)}, ${parseInt(result[2], 16)}, ${parseInt(result[3], 16)}` :
                '0, 0, 0';
        }

        // Auto-refresh every 30 seconds
        setInterval(() => {
            loadChartData(currentPosition);
        }, 30000);

    });
</script>

<!-- Add custom CSS for simple styling -->
<style>
    .quick-stat {
        transition: all 0.3s ease;
    }

    .quick-stat:hover {
        transform: scale(1.05);
    }

    .spin {
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
    }



    .form-control-sm, .form-select-sm {
        font-size: 0.875rem;
    }

    /* Loading state */
    .loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(255, 255, 255, 0.8);
        display: none;
        z-index: 9999;
        align-items: center;
        justify-content: center;
    }

    .loading-overlay.show {
        display: flex;
    }
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Home dashboard functionality

    // Performance optimization: Lazy load images
    const images = document.querySelectorAll('img[data-src]');
    const imageObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                img.src = img.dataset.src;
                img.removeAttribute('data-src');
                observer.unobserve(img);
            }
        });
    });

    images.forEach(img => imageObserver.observe(img));

    // Optimize table rendering for large datasets
    const table = document.getElementById('pollingStationsTable');
    if (table) {
        // Add virtual scrolling for very large tables (if needed)
        const tbody = table.querySelector('tbody');
        const rows = tbody.querySelectorAll('tr');

        // Only show visible rows to improve performance
        if (rows.length > 100) {
            console.log('Large dataset detected, optimizing rendering...');
            // You can implement virtual scrolling here if needed
        }
    }

    // Show live activity bar after page loads
    setTimeout(() => {
        const liveBar = document.getElementById('live-activity-bar');
        if (liveBar) {
            liveBar.classList.remove('d-none');

            // Auto-hide after 10 seconds
            setTimeout(() => {
                liveBar.classList.add('d-none');
            }, 10000);
        }
    }, 2000);

    // Enhanced vote update handling
    if (window.smoothRefresh) {
        // Override the original showVoteUpdateToast to add sound notification
        const originalShowToast = window.smoothRefresh.showVoteUpdateToast;
        window.smoothRefresh.showVoteUpdateToast = function(update) {
            // Call original method
            originalShowToast.call(this, update);

            // Add subtle sound notification (optional)
            try {
                // Create a subtle notification sound
                const audioContext = new (window.AudioContext || window.webkitAudioContext)();
                const oscillator = audioContext.createOscillator();
                const gainNode = audioContext.createGain();

                oscillator.connect(gainNode);
                gainNode.connect(audioContext.destination);

                oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
                oscillator.frequency.setValueAtTime(600, audioContext.currentTime + 0.1);

                gainNode.gain.setValueAtTime(0, audioContext.currentTime);
                gainNode.gain.linearRampToValueAtTime(0.1, audioContext.currentTime + 0.01);
                gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.2);

                oscillator.start(audioContext.currentTime);
                oscillator.stop(audioContext.currentTime + 0.2);
            } catch (e) {
                // Ignore audio errors
            }
        };
    }
});
</script>

<!-- Additional CSS for enhanced animations -->
<style>
@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.vote-update-toast {
    animation: slideInRight 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

#live-activity-bar {
    animation: slideInRight 0.5s ease-out;
}

/* Pulse animation for live indicator */
.spinner-border {
    animation: spinner-border 0.75s linear infinite, pulse-glow 2s ease-in-out infinite alternate;
}

@keyframes pulse-glow {
    from {
        box-shadow: 0 0 5px rgba(255, 255, 255, 0.5);
    }
    to {
        box-shadow: 0 0 20px rgba(255, 255, 255, 0.8);
    }
}
</style>

@endpush
