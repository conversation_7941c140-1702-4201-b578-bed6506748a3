@extends('layouts.app')

@section('content')
<div class="container-fluid px-4">
    <!-- Header -->
    <div class="audit-detail-header mb-4">
        <div class="header-content">
            <div class="header-info">
                <div class="header-icon">
                    <i class="bi bi-file-text"></i>
                </div>
                <div class="header-text">
                    <h1 class="header-title">Audit Log Details</h1>
                    <p class="header-subtitle">Detailed information about this audit entry</p>
                </div>
            </div>
            <div class="header-actions">
                <a href="{{ route('admin.audit.index') }}" class="btn btn-secondary btn-modern">
                    <i class="bi bi-arrow-left"></i>
                    <span>Back to Audit Logs</span>
                </a>
            </div>
        </div>
    </div>

    <!-- Audit Details -->
    <div class="audit-detail-content">
        <div class="detail-grid">
            <!-- Basic Information -->
            <div class="detail-section">
                <div class="section-header">
                    <h3 class="section-title">
                        <i class="bi bi-info-circle"></i>
                        Basic Information
                    </h3>
                </div>
                <div class="section-content">
                    <div class="detail-item">
                        <label class="detail-label">Action</label>
                        <div class="detail-value">
                            <span class="action-badge action-{{ $auditLog->action }}">
                                <i class="bi bi-{{ $auditLog->action == 'create' ? 'plus-circle' : ($auditLog->action == 'update' ? 'pencil-square' : ($auditLog->action == 'delete' ? 'trash' : 'lightning')) }}"></i>
                                {{ ucfirst(str_replace('_', ' ', $auditLog->action)) }}
                            </span>
                        </div>
                    </div>
                    <div class="detail-item">
                        <label class="detail-label">Timestamp</label>
                        <div class="detail-value">
                            {{ $auditLog->created_at->format('F j, Y \a\t g:i A') }}
                            <small class="text-muted">({{ $auditLog->created_at->diffForHumans() }})</small>
                        </div>
                    </div>
                    <div class="detail-item">
                        <label class="detail-label">Description</label>
                        <div class="detail-value">
                            {{ $auditLog->description ?? 'No description provided' }}
                        </div>
                    </div>
                </div>
            </div>

            <!-- User Information -->
            <div class="detail-section">
                <div class="section-header">
                    <h3 class="section-title">
                        <i class="bi bi-person"></i>
                        User Information
                    </h3>
                </div>
                <div class="section-content">
                    <div class="detail-item">
                        <label class="detail-label">User</label>
                        <div class="detail-value">
                            @if($auditLog->user)
                                <div class="user-info">
                                    <div class="user-avatar">
                                        <span>{{ strtoupper(substr($auditLog->user->name, 0, 1)) }}</span>
                                    </div>
                                    <div class="user-details">
                                        <div class="user-name">{{ $auditLog->user->name }}</div>
                                        <div class="user-type">{{ ucfirst($auditLog->user->user_type) }}</div>
                                    </div>
                                </div>
                            @else
                                <span class="text-muted">{{ $auditLog->user_name ?? 'System' }}</span>
                            @endif
                        </div>
                    </div>
                    <div class="detail-item">
                        <label class="detail-label">User Type</label>
                        <div class="detail-value">
                            {{ ucfirst($auditLog->user_type ?? 'system') }}
                        </div>
                    </div>
                    <div class="detail-item">
                        <label class="detail-label">IP Address</label>
                        <div class="detail-value">
                            {{ $auditLog->ip_address ?? 'N/A' }}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Model Information -->
            @if($auditLog->model_type)
            <div class="detail-section">
                <div class="section-header">
                    <h3 class="section-title">
                        <i class="bi bi-box"></i>
                        Affected Model
                    </h3>
                </div>
                <div class="section-content">
                    <div class="detail-item">
                        <label class="detail-label">Model Type</label>
                        <div class="detail-value">
                            <span class="model-badge">
                                <i class="bi bi-box"></i>
                                {{ class_basename($auditLog->model_type) }}
                            </span>
                        </div>
                    </div>
                    <div class="detail-item">
                        <label class="detail-label">Model ID</label>
                        <div class="detail-value">
                            #{{ $auditLog->model_id }}
                        </div>
                    </div>
                </div>
            </div>
            @endif

            <!-- Request Information -->
            <div class="detail-section">
                <div class="section-header">
                    <h3 class="section-title">
                        <i class="bi bi-globe"></i>
                        Request Information
                    </h3>
                </div>
                <div class="section-content">
                    <div class="detail-item">
                        <label class="detail-label">URL</label>
                        <div class="detail-value">
                            <code>{{ $auditLog->url ?? 'N/A' }}</code>
                        </div>
                    </div>
                    <div class="detail-item">
                        <label class="detail-label">Method</label>
                        <div class="detail-value">
                            @if($auditLog->method)
                                <span class="method-badge method-{{ strtolower($auditLog->method) }}">
                                    {{ $auditLog->method }}
                                </span>
                            @else
                                N/A
                            @endif
                        </div>
                    </div>
                    <div class="detail-item">
                        <label class="detail-label">User Agent</label>
                        <div class="detail-value">
                            <small>{{ $auditLog->user_agent ?? 'N/A' }}</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Data Changes -->
        @if($auditLog->old_values || $auditLog->new_values || $auditLog->request_data)
        <div class="data-changes-section">
            <div class="section-header">
                <h3 class="section-title">
                    <i class="bi bi-code-square"></i>
                    Data Changes
                </h3>
            </div>
            
            <div class="changes-grid">
                @if($auditLog->old_values)
                <div class="changes-column">
                    <h4 class="changes-title">
                        <i class="bi bi-arrow-left-circle text-danger"></i>
                        Old Values
                    </h4>
                    <div class="json-display">
                        <pre><code>{{ json_encode($auditLog->old_values, JSON_PRETTY_PRINT) }}</code></pre>
                    </div>
                </div>
                @endif

                @if($auditLog->new_values)
                <div class="changes-column">
                    <h4 class="changes-title">
                        <i class="bi bi-arrow-right-circle text-success"></i>
                        New Values
                    </h4>
                    <div class="json-display">
                        <pre><code>{{ json_encode($auditLog->new_values, JSON_PRETTY_PRINT) }}</code></pre>
                    </div>
                </div>
                @endif

                @if($auditLog->request_data)
                <div class="changes-column">
                    <h4 class="changes-title">
                        <i class="bi bi-file-earmark-code text-info"></i>
                        Request Data
                    </h4>
                    <div class="json-display">
                        <pre><code>{{ json_encode($auditLog->request_data, JSON_PRETTY_PRINT) }}</code></pre>
                    </div>
                </div>
                @endif
            </div>
        </div>
        @endif
    </div>
</div>
@endsection

@section('styles')
<style>
/* Audit Detail Page Styles */
:root {
    --primary-color: #3b82f6;
    --primary-dark: #2563eb;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --info-color: #06b6d4;
    --neutral-50: #f9fafb;
    --neutral-100: #f3f4f6;
    --neutral-200: #e5e7eb;
    --neutral-300: #d1d5db;
    --neutral-400: #9ca3af;
    --neutral-500: #6b7280;
    --neutral-600: #4b5563;
    --neutral-700: #374151;
    --neutral-800: #1f2937;
    --neutral-900: #111827;
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
}

/* Header */
.audit-detail-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    border-radius: var(--radius-xl);
    padding: 2rem;
    color: white;
    box-shadow: var(--shadow-lg);
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 1rem;
}

.header-info {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.header-icon {
    width: 3rem;
    height: 3rem;
    background: rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    backdrop-filter: blur(10px);
}

.header-title {
    font-size: 1.875rem;
    font-weight: 700;
    margin: 0;
    line-height: 1.2;
}

.header-subtitle {
    font-size: 1rem;
    opacity: 0.9;
    margin: 0.25rem 0 0 0;
}

.btn-modern {
    background: rgba(255, 255, 255, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    backdrop-filter: blur(10px);
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.25rem;
    border-radius: var(--radius-lg);
    font-weight: 500;
    transition: all 0.3s ease;
    text-decoration: none;
}

.btn-modern:hover {
    background: rgba(255, 255, 255, 0.25);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    color: white;
}

/* Detail Content */
.audit-detail-content {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.detail-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 1.5rem;
}

.detail-section {
    background: white;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--neutral-200);
    overflow: hidden;
}

.section-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--neutral-200);
    background: var(--neutral-50);
}

.section-title {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--neutral-800);
    margin: 0;
}

.section-title i {
    color: var(--primary-color);
}

.section-content {
    padding: 1.5rem;
}

.detail-item {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
}

.detail-item:last-child {
    margin-bottom: 0;
}

.detail-label {
    font-weight: 600;
    color: var(--neutral-700);
    font-size: 0.875rem;
}

.detail-value {
    color: var(--neutral-800);
}

/* Badges */
.action-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.375rem;
    padding: 0.5rem 1rem;
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    font-weight: 500;
}

.action-create {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
}

.action-update {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning-color);
}

.action-delete {
    background: rgba(239, 68, 68, 0.1);
    color: var(--danger-color);
}

.action-badge:not(.action-create):not(.action-update):not(.action-delete) {
    background: rgba(59, 130, 246, 0.1);
    color: var(--primary-color);
}

.model-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.375rem;
    padding: 0.5rem 1rem;
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    font-weight: 500;
    background: rgba(107, 114, 128, 0.1);
    color: var(--neutral-600);
}

.method-badge {
    padding: 0.25rem 0.5rem;
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.method-get { background: rgba(16, 185, 129, 0.1); color: var(--success-color); }
.method-post { background: rgba(59, 130, 246, 0.1); color: var(--primary-color); }
.method-put { background: rgba(245, 158, 11, 0.1); color: var(--warning-color); }
.method-delete { background: rgba(239, 68, 68, 0.1); color: var(--danger-color); }

/* User Info */
.user-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.user-avatar {
    width: 2.5rem;
    height: 2.5rem;
    background: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 1rem;
}

.user-name {
    font-weight: 600;
    color: var(--neutral-800);
}

.user-type {
    font-size: 0.875rem;
    color: var(--neutral-600);
}

/* Data Changes */
.data-changes-section {
    background: white;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--neutral-200);
    overflow: hidden;
}

.changes-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    padding: 1.5rem;
}

.changes-column {
    background: var(--neutral-50);
    border-radius: var(--radius-md);
    overflow: hidden;
}

.changes-title {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1rem;
    font-weight: 600;
    padding: 1rem;
    margin: 0;
    background: white;
    border-bottom: 1px solid var(--neutral-200);
}

.json-display {
    padding: 1rem;
    max-height: 400px;
    overflow-y: auto;
}

.json-display pre {
    margin: 0;
    font-size: 0.875rem;
    line-height: 1.5;
    color: var(--neutral-700);
}

.json-display code {
    background: none;
    padding: 0;
    color: inherit;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        align-items: stretch;
        text-align: center;
    }
    
    .header-info {
        justify-content: center;
        margin-bottom: 1rem;
    }
    
    .detail-grid {
        grid-template-columns: 1fr;
    }
    
    .changes-grid {
        grid-template-columns: 1fr;
    }
}
</style>
@endsection
