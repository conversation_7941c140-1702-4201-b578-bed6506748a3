@extends('layouts.app')

@section('content')
<div class="container-fluid px-4">
    <!-- Modern Header -->
    <div class="audit-header mb-4">
        <div class="header-content">
            <div class="header-info">
                <div class="header-icon">
                    <i class="bi bi-shield-check"></i>
                </div>
                <div class="header-text">
                    <h1 class="header-title">Unified Audit Dashboard</h1>
                    <p class="header-subtitle">Monitor system activities, user actions, and vote submissions</p>
                </div>
            </div>
            <div class="header-actions">
                <div class="action-buttons">
                    <a href="{{ route('admin.audit.activity') }}" class="btn btn-secondary btn-modern">
                        <i class="bi bi-activity"></i>
                        <span>Vote Activity</span>
                    </a>
                    <a href="{{ route('admin.audit.flagged') }}" class="btn btn-warning btn-modern">
                        <i class="bi bi-flag"></i>
                        <span>Flagged Items</span>
                        @if(isset($suspiciousActivity) && $suspiciousActivity['total_flagged'] > 0)
                        <span class="badge bg-danger ms-1">{{ $suspiciousActivity['total_flagged'] }}</span>
                        @endif
                    </a>
                    <a href="{{ route('admin.audit.statistics') }}" class="btn btn-info btn-modern">
                        <i class="bi bi-graph-up"></i>
                        <span>Statistics</span>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Vote Audit Statistics Cards -->
    <div class="stats-section mb-4">
        <div class="section-header mb-3">
            <h2 class="section-title">
                <i class="bi bi-bar-chart"></i>
                Vote Audit Overview
            </h2>
        </div>
        <div class="stats-grid">
            <div class="stat-card stat-danger">
                <div class="stat-content">
                    <div class="stat-info">
                        <div class="stat-number">{{ $suspiciousActivity['total_flagged'] ?? 0 }}</div>
                        <div class="stat-label">Flagged Submissions</div>
                    </div>
                    <div class="stat-icon">
                        <i class="bi bi-flag"></i>
                    </div>
                </div>
            </div>
            <div class="stat-card stat-warning">
                <div class="stat-content">
                    <div class="stat-info">
                        <div class="stat-number">{{ $suspiciousActivity['rapid_submissions'] ?? 0 }}</div>
                        <div class="stat-label">Rapid Submissions</div>
                    </div>
                    <div class="stat-icon">
                        <i class="bi bi-lightning"></i>
                    </div>
                </div>
            </div>
            <div class="stat-card stat-info">
                <div class="stat-content">
                    <div class="stat-info">
                        <div class="stat-number">{{ $suspiciousActivity['large_changes'] ?? 0 }}</div>
                        <div class="stat-label">Large Vote Changes</div>
                    </div>
                    <div class="stat-icon">
                        <i class="bi bi-arrow-up-circle"></i>
                    </div>
                </div>
            </div>
            <div class="stat-card stat-secondary">
                <div class="stat-content">
                    <div class="stat-info">
                        <div class="stat-number">{{ $suspiciousActivity['agents_with_multiple_submissions'] ?? 0 }}</div>
                        <div class="stat-label">Multiple Submissions</div>
                    </div>
                    <div class="stat-icon">
                        <i class="bi bi-people"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Compact Filters Section -->
    <div class="filters-card mb-4">
        <form method="GET" class="filters-form">
            <div class="filters-row">
                <!-- Search Input -->
                <div class="filter-group search-group">
                    <div class="search-input-wrapper">
                        <i class="bi bi-search search-icon"></i>
                        <input type="text"
                               class="search-input"
                               name="search"
                               value="{{ request('search') }}"
                               placeholder="Search audit logs..."
                               autocomplete="off">
                        @if(request('search'))
                        <button type="button" class="search-clear" onclick="clearSearch()">
                            <i class="bi bi-x"></i>
                        </button>
                        @endif
                    </div>
                </div>

                <!-- Action Filter -->
                <div class="filter-group">
                    <select class="filter-select" name="action">
                        <option value="">All Actions</option>
                        @if(isset($actions))
                            @foreach($actions as $action)
                                <option value="{{ $action }}" {{ request('action') == $action ? 'selected' : '' }}>
                                    {{ ucfirst(str_replace('_', ' ', $action)) }}
                                </option>
                            @endforeach
                        @endif
                    </select>
                </div>

                <!-- User Type Filter -->
                <div class="filter-group">
                    <select class="filter-select" name="user_type">
                        <option value="">All User Types</option>
                        @if(isset($userTypes))
                            @foreach($userTypes as $userType)
                                <option value="{{ $userType }}" {{ request('user_type') == $userType ? 'selected' : '' }}>
                                    {{ ucfirst($userType) }}
                                </option>
                            @endforeach
                        @endif
                    </select>
                </div>

                <!-- Model Type Filter -->
                <div class="filter-group">
                    <select class="filter-select" name="model_type">
                        <option value="">All Models</option>
                        @if(isset($modelTypes))
                            @foreach($modelTypes as $modelType)
                                <option value="{{ $modelType }}" {{ request('model_type') == $modelType ? 'selected' : '' }}>
                                    {{ $modelType }}
                                </option>
                            @endforeach
                        @endif
                    </select>
                </div>

                <!-- Date From -->
                <div class="filter-group">
                    <input type="date"
                           class="filter-select"
                           name="date_from"
                           value="{{ request('date_from') }}"
                           placeholder="From Date">
                </div>

                <!-- Date To -->
                <div class="filter-group">
                    <input type="date"
                           class="filter-select"
                           name="date_to"
                           value="{{ request('date_to') }}"
                           placeholder="To Date">
                </div>

                <!-- Per Page -->
                <div class="filter-group">
                    <select class="filter-select" name="per_page" onchange="this.form.submit()">
                        <option value="25" {{ request('per_page', 25) == 25 ? 'selected' : '' }}>25 per page</option>
                        <option value="50" {{ request('per_page') == 50 ? 'selected' : '' }}>50 per page</option>
                        <option value="100" {{ request('per_page') == 100 ? 'selected' : '' }}>100 per page</option>
                        <option value="200" {{ request('per_page') == 200 ? 'selected' : '' }}>200 per page</option>
                    </select>
                </div>

                <!-- Filter Actions -->
                <div class="filter-actions">
                    <button type="submit" class="btn-filter-apply" title="Apply Filters">
                        <i class="bi bi-funnel"></i>
                    </button>
                    @if(request()->hasAny(['search', 'action', 'user_type', 'model_type', 'date_from', 'date_to']))
                    <a href="{{ route('admin.audit.index') }}" class="btn-filter-clear" title="Clear Filters">
                        <i class="bi bi-x-circle"></i>
                    </a>
                    @endif
                </div>
            </div>

            <!-- Active Filters Display -->
            @if(request()->hasAny(['search', 'action', 'user_type', 'model_type', 'date_from', 'date_to']))
            <div class="active-filters">
                <span class="active-filters-label">Active filters:</span>
                <div class="active-filters-list">
                    @if(request('search'))
                        <span class="filter-tag">
                            <i class="bi bi-search"></i>
                            Search: {{ request('search') }}
                            <button type="button" onclick="removeFilter('search')" class="filter-tag-remove">
                                <i class="bi bi-x"></i>
                            </button>
                        </span>
                    @endif
                    @if(request('action'))
                        <span class="filter-tag">
                            <i class="bi bi-lightning"></i>
                            Action: {{ ucfirst(str_replace('_', ' ', request('action'))) }}
                            <button type="button" onclick="removeFilter('action')" class="filter-tag-remove">
                                <i class="bi bi-x"></i>
                            </button>
                        </span>
                    @endif
                    @if(request('user_type'))
                        <span class="filter-tag">
                            <i class="bi bi-person-badge"></i>
                            User Type: {{ ucfirst(request('user_type')) }}
                            <button type="button" onclick="removeFilter('user_type')" class="filter-tag-remove">
                                <i class="bi bi-x"></i>
                            </button>
                        </span>
                    @endif
                    @if(request('model_type'))
                        <span class="filter-tag">
                            <i class="bi bi-box"></i>
                            Model: {{ request('model_type') }}
                            <button type="button" onclick="removeFilter('model_type')" class="filter-tag-remove">
                                <i class="bi bi-x"></i>
                            </button>
                        </span>
                    @endif
                    @if(request('date_from'))
                        <span class="filter-tag">
                            <i class="bi bi-calendar"></i>
                            From: {{ request('date_from') }}
                            <button type="button" onclick="removeFilter('date_from')" class="filter-tag-remove">
                                <i class="bi bi-x"></i>
                            </button>
                        </span>
                    @endif
                    @if(request('date_to'))
                        <span class="filter-tag">
                            <i class="bi bi-calendar"></i>
                            To: {{ request('date_to') }}
                            <button type="button" onclick="removeFilter('date_to')" class="filter-tag-remove">
                                <i class="bi bi-x"></i>
                            </button>
                        </span>
                    @endif
                </div>
            </div>
            @endif
        </form>
    </div>

    <!-- Best Performing Users Section -->
    <div class="best-performers-section mb-4">
        <div class="section-header mb-3">
            <h2 class="section-title">
                <i class="bi bi-trophy"></i>
                Best Performing Users
            </h2>
        </div>
        <div class="performers-grid">
            @forelse($bestPerformingUsers as $user)
                <div class="performer-card">
                    <div class="performer-avatar">
                        <span>{{ strtoupper(substr($user->name ?? 'U', 0, 1)) }}</span>
                    </div>
                    <div class="performer-info">
                        <div class="performer-name">{{ $user->name }}</div>
                        <div class="performer-type">{{ ucfirst($user->user_type) }}</div>
                        <div class="performer-stats">
                            <div class="stat">
                                <span class="stat-value">{{ number_format($user->submission_count) }}</span>
                                <span class="stat-label">Submissions</span>
                            </div>
                            <div class="stat">
                                <span class="stat-value">{{ $user->accuracy_score }}%</span>
                                <span class="stat-label">Accuracy</span>
                            </div>
                        </div>
                    </div>
                    <div class="performer-score" style="background: linear-gradient(to right, var(--success-color) 0%, var(--success-color) {{ $user->accuracy_score }}%, var(--neutral-200) {{ $user->accuracy_score }}%, var(--neutral-200) 100%)">
                        <span class="score-label">Performance Score</span>
                    </div>
                </div>
            @empty
                <div class="empty-state">
                    <div class="empty-icon">
                        <i class="bi bi-trophy"></i>
                    </div>
                    <div class="empty-title">No Performance Data Available</div>
                    <div class="empty-subtitle">User performance metrics will appear here once more vote submissions are recorded.</div>
                </div>
            @endforelse
        </div>
    </div>

    <!-- Two Column Layout for Audit Logs -->
    <div class="audit-content-grid">
        <!-- System Audit Logs -->
        <div class="audit-section p-4">
            <div class="section-header">
                <h2 class="section-title">
                    <i class="bi bi-shield-check"></i>
                    System Audit Logs
                </h2>
                <p class="section-subtitle">{{ $auditLogs->total() }} total records</p>
            </div>

            <div class="audit-table-container">
                <div class="table-wrapper">
                    <table class="audit-table">
                        <thead class="table-head">
                            <tr>
                                <th class="th-timestamp">
                                    <div class="th-content">
                                        <i class="bi bi-clock"></i>
                                        <span>Time</span>
                                    </div>
                                </th>
                                <th class="th-user">
                                    <div class="th-content">
                                        <i class="bi bi-person"></i>
                                        <span>User</span>
                                    </div>
                                </th>
                                <th class="th-action">
                                    <div class="th-content">
                                        <i class="bi bi-lightning"></i>
                                        <span>Action</span>
                                    </div>
                                </th>
                                <th class="th-model">
                                    <div class="th-content">
                                        <i class="bi bi-box"></i>
                                        <span>Model</span>
                                    </div>
                                </th>
                                <th class="th-description">
                                    <div class="th-content">
                                        <i class="bi bi-file-text"></i>
                                        <span>Description</span>
                                    </div>
                                </th>
                                <th class="th-ip">
                                    <div class="th-content">
                                        <i class="bi bi-globe"></i>
                                        <span>IP Address</span>
                                    </div>
                                </th>
                                <th class="th-actions">
                                    <div class="th-content">
                                        <i class="bi bi-gear"></i>
                                        <span>Actions</span>
                                    </div>
                                </th>
                            </tr>
                        </thead>
                        <tbody class="table-body">
                            @forelse($auditLogs as $log)
                            <tr class="audit-row" data-log-id="{{ $log->id }}">
                                <td class="td-timestamp">
                                    <div class="timestamp-cell">
                                        <div class="timestamp-date">{{ $log->created_at->format('M d, Y') }}</div>
                                        <div class="timestamp-time">{{ $log->created_at->format('H:i:s') }}</div>
                                        <div class="timestamp-ago text-muted">{{ $log->created_at->diffForHumans() }}</div>
                                    </div>
                                </td>
                                <td class="td-user">
                                    <div class="user-cell">
                                        <div class="user-avatar">
                                            <span>{{ $log->user_name ? strtoupper(substr($log->user_name, 0, 1)) : 'S' }}</span>
                                        </div>
                                        <div class="user-info">
                                            <div class="user-name">{{ $log->user_name ?? 'System' }}</div>
                                            <div class="user-type">{{ ucfirst($log->user_type ?? 'system') }}</div>
                                            @if($log->user_id)
                                                <div class="user-id text-muted">ID: {{ $log->user_id }}</div>
                                            @endif
                                        </div>
                                    </div>
                                </td>
                                <td class="td-action">
                                    <div class="action-badge action-{{ $log->action }}">
                                        <i class="bi bi-{{ $log->action == 'create' ? 'plus-circle' : ($log->action == 'update' ? 'pencil-square' : ($log->action == 'delete' ? 'trash' : 'lightning')) }}"></i>
                                        <span>{{ ucfirst(str_replace('_', ' ', $log->action)) }}</span>
                                    </div>
                                </td>
                                <td class="td-model">
                                    @if($log->model_type)
                                        <div class="model-badge">
                                            <i class="bi bi-box"></i>
                                            <span>{{ class_basename($log->model_type) }}</span>
                                            @if($log->model_id)
                                                <span class="model-id">#{{ $log->model_id }}</span>
                                            @endif
                                        </div>
                                    @else
                                        <span class="text-muted">System</span>
                                    @endif
                                </td>
                                <td class="td-description">
                                    <div class="description-cell">
                                        {{ Str::limit($log->description, 50) }}
                                        @if(strlen($log->description) > 50)
                                            <span class="description-tooltip" title="{{ $log->description }}">
                                                <i class="bi bi-info-circle"></i>
                                            </span>
                                        @endif
                                    </div>
                                </td>
                                <td class="td-ip">
                                    @if($log->ip_address)
                                        <div class="ip-badge">
                                            <i class="bi bi-globe"></i>
                                            <span>{{ $log->ip_address }}</span>
                                        </div>
                                    @else
                                        <span class="text-muted">—</span>
                                    @endif
                                </td>
                                <td class="td-actions">
                                    <div class="action-buttons">
                                        <a href="{{ route('admin.audit.show', $log) }}"
                                           class="action-btn action-btn-view"
                                           title="View Details">
                                            <i class="bi bi-eye"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            @empty
                            <tr class="empty-row">
                                <td colspan="5" class="empty-cell">
                                    <div class="empty-state">
                                        <div class="empty-icon">
                                            <i class="bi bi-shield-check"></i>
                                        </div>
                                        <div class="empty-title">No System Audit Logs Found</div>
                                        <div class="empty-subtitle">
                                            @if(request()->hasAny(['search', 'action', 'user_type', 'model_type', 'date_from', 'date_to']))
                                                No audit logs match your current filters.
                                            @else
                                                No system audit logs have been recorded yet.
                                            @endif
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Pagination for System Audit Logs -->
            @if($auditLogs->hasPages())
            <div class="pagination-section">
                <div class="pagination-info">
                    <span class="pagination-text">
                        Showing {{ $auditLogs->firstItem() }}-{{ $auditLogs->lastItem() }} of {{ $auditLogs->total() }} logs
                    </span>
                </div>
                <div class="pagination-controls">
                    {{ $auditLogs->links() }}
                </div>
            </div>
            @endif
        </div>

        <!-- Recent Vote Audits -->
        <div class="audit-section">
            <div class="section-header p-4">
                <h2 class="section-title">
                    <i class="bi bi-activity"></i>
                    Recent Vote Audits
                </h2>
                <p class="section-subtitle">Latest vote submission activities</p>
            </div>

            <div class="vote-audit-list">
                @forelse($recentVoteAudits ?? [] as $voteAudit)
                <div class="vote-audit-item {{ $voteAudit->is_flagged ? 'flagged' : '' }}">
                    <div class="audit-item-header">
                        <div class="audit-time">
                            <i class="bi bi-clock"></i>
                            {{ $voteAudit->created_at->format('M d, H:i') }}
                        </div>
                        @if($voteAudit->is_flagged)
                        <div class="audit-flag">
                            <i class="bi bi-flag-fill"></i>
                            Flagged
                        </div>
                        @endif
                    </div>
                    <div class="audit-item-content">
                        <div class="audit-user">
                            <i class="bi bi-person"></i>
                            {{ $voteAudit->user->name ?? 'Unknown User' }}
                        </div>
                        <div class="audit-station">
                            <i class="bi bi-geo-alt"></i>
                            {{ $voteAudit->pollingStation->name ?? 'Unknown Station' }}
                        </div>
                        <div class="audit-action">
                            <i class="bi bi-lightning"></i>
                            {{ ucfirst($voteAudit->action_type) }} Vote
                        </div>
                    </div>
                    <div class="audit-item-actions">
                        <a href="{{ route('admin.audit.activity') }}" class="audit-link">
                            View Details <i class="bi bi-arrow-right"></i>
                        </a>
                    </div>
                </div>
                @empty
                <div class="empty-vote-audits">
                    <div class="empty-icon">
                        <i class="bi bi-activity"></i>
                    </div>
                    <div class="empty-text">No recent vote audits</div>
                </div>
                @endforelse
            </div>

            <div class="section-footer">
                <a href="{{ route('admin.audit.activity') }}" class="btn btn-primary btn-modern">
                    <i class="bi bi-activity"></i>
                    <span>View All Vote Audits</span>
                </a>
            </div>
        </div>
    </div>
</div>

<script>
// Filter management
function clearSearch() {
    document.querySelector('input[name="search"]').value = '';
    document.querySelector('.filters-form').submit();
}

function removeFilter(filterName) {
    const form = document.querySelector('.filters-form');
    const input = form.querySelector(`[name="${filterName}"]`);
    if (input) {
        input.value = '';
        form.submit();
    }
}
</script>
@endsection

@section('styles')
<style>
/* Unified Audit Dashboard - Modern Design */
:root {
    --primary-color: #3b82f6;
    --primary-dark: #2563eb;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --info-color: #06b6d4;
    --neutral-50: #f9fafb;
    --neutral-100: #f3f4f6;
    --neutral-200: #e5e7eb;
    --neutral-300: #d1d5db;
    --neutral-400: #9ca3af;
    --neutral-500: #6b7280;
    --neutral-600: #4b5563;
    --neutral-700: #374151;
    --neutral-800: #1f2937;
    --neutral-900: #111827;
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
}

/* Header */
.audit-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    border-radius: var(--radius-xl);
    padding: 2rem;
    color: white;
    box-shadow: var(--shadow-lg);
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 1rem;
}

.header-info {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.header-icon {
    width: 3rem;
    height: 3rem;
    background: rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    backdrop-filter: blur(10px);
}

.header-title {
    font-size: 1.875rem;
    font-weight: 700;
    margin: 0;
    line-height: 1.2;
}

.header-subtitle {
    font-size: 1rem;
    opacity: 0.9;
    margin: 0.25rem 0 0 0;
}

.action-buttons {
    display: flex;
    gap: 0.75rem;
    flex-wrap: wrap;
}

.btn-modern {
    background: rgba(255, 255, 255, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    backdrop-filter: blur(10px);
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.25rem;
    border-radius: var(--radius-lg);
    font-weight: 500;
    transition: all 0.3s ease;
    text-decoration: none;
}

.btn-modern:hover {
    background: rgba(255, 255, 255, 0.25);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    color: white;
}

.btn-warning {
    background: rgba(245, 158, 11, 0.15);
    border-color: rgba(245, 158, 11, 0.2);
}

.btn-info {
    background: rgba(6, 182, 212, 0.15);
    border-color: rgba(6, 182, 212, 0.2);
}

/* Statistics Section */
.stats-section {
    margin-bottom: 2rem;
}

.section-header {
    margin-bottom: 1rem;
}

.section-title {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--neutral-800);
    margin: 0;
}

.section-title i {
    color: var(--primary-color);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
}

.stat-card {
    background: white;
    border-radius: var(--radius-lg);
    padding: 1.5rem;
    box-shadow: var(--shadow-md);
    border: 1px solid var(--neutral-200);
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.stat-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.25rem;
}

.stat-label {
    font-size: 0.875rem;
    color: var(--neutral-600);
    margin: 0;
}

.stat-icon {
    width: 3rem;
    height: 3rem;
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
}

.stat-danger .stat-number { color: var(--danger-color); }
.stat-danger .stat-icon { background: var(--danger-color); }

.stat-warning .stat-number { color: var(--warning-color); }
.stat-warning .stat-icon { background: var(--warning-color); }

.stat-info .stat-number { color: var(--info-color); }
.stat-info .stat-icon { background: var(--info-color); }

.stat-secondary .stat-number { color: var(--neutral-600); }
.stat-secondary .stat-icon { background: var(--neutral-600); }

/* Filters */
.filters-card {
    background: white;
    border-radius: var(--radius-lg);
    padding: 1.25rem;
    box-shadow: var(--shadow-md);
    border: 1px solid var(--neutral-200);
}

.filters-row {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    flex-wrap: wrap;
}

.filter-group {
    position: relative;
}

.search-group {
    flex: 1;
    min-width: 250px;
}

.search-input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
}

.search-icon {
    position: absolute;
    left: 0.75rem;
    color: var(--neutral-400);
    font-size: 1rem;
    z-index: 2;
}

.search-input {
    width: 100%;
    padding: 0.75rem 0.75rem 0.75rem 2.5rem;
    border: 1px solid var(--neutral-300);
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    background: white;
    transition: all 0.2s ease;
}

.search-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.search-clear {
    position: absolute;
    right: 0.5rem;
    width: 1.5rem;
    height: 1.5rem;
    border: none;
    background: var(--neutral-200);
    color: var(--neutral-600);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.search-clear:hover {
    background: var(--neutral-300);
    transform: scale(1.1);
}

.filter-select {
    padding: 0.75rem 1rem;
    border: 1px solid var(--neutral-300);
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    background: white;
    color: var(--neutral-700);
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 140px;
}

.filter-select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.filter-actions {
    display: flex;
    gap: 0.5rem;
}

.btn-filter-apply,
.btn-filter-clear {
    width: 2.5rem;
    height: 2.5rem;
    border: 1px solid var(--neutral-300);
    border-radius: var(--radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.875rem;
    text-decoration: none;
}

.btn-filter-apply {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.btn-filter-apply:hover {
    background: var(--primary-dark);
    border-color: var(--primary-dark);
    transform: translateY(-1px);
}

.btn-filter-clear {
    background: white;
    color: var(--danger-color);
    border-color: var(--danger-color);
}

.btn-filter-clear:hover {
    background: var(--danger-color);
    color: white;
    transform: translateY(-1px);
}

/* Active Filters */
.active-filters {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid var(--neutral-200);
}

.active-filters-label {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--neutral-600);
    flex-shrink: 0;
}

.active-filters-list {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    flex: 1;
}

.filter-tag {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: var(--primary-color);
    color: white;
    padding: 0.375rem 0.75rem;
    border-radius: var(--radius-md);
    font-size: 0.75rem;
    font-weight: 500;
}

.filter-tag-remove {
    background: none;
    border: none;
    color: white;
    font-size: 0.875rem;
    cursor: pointer;
    padding: 0;
    margin-left: 0.25rem;
    border-radius: 50%;
    width: 1rem;
    height: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.filter-tag-remove:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.1);
}

/* Audit Content Grid */
.audit-content-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1.5rem;
}

/* Best Performing Users Section */
.best-performers-section {
    background-color: var(--neutral-50);
    border-radius: 0.75rem;
    padding: 1.5rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.performers-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 1rem;
}

.performer-card {
    background-color: white;
    border-radius: 0.5rem;
    padding: 1.25rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    display: flex;
    flex-direction: column;
    transition: transform 0.2s, box-shadow 0.2s;
}

.performer-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
}

.performer-avatar {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background-color: var(--primary-color);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.performer-info {
    flex-grow: 1;
}

.performer-name {
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.performer-type {
    font-size: 0.875rem;
    color: var(--neutral-600);
    margin-bottom: 1rem;
}

.performer-stats {
    display: flex;
    gap: 1.5rem;
    margin-bottom: 1rem;
}

.stat {
    display: flex;
    flex-direction: column;
}

.stat-value {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--primary-color);
}

.stat-label {
    font-size: 0.75rem;
    color: var(--neutral-600);
}

.performer-score {
    height: 8px;
    border-radius: 4px;
    margin-top: 0.75rem;
    position: relative;
}

.score-label {
    position: absolute;
    top: -18px;
    right: 0;
    font-size: 0.75rem;
    color: var(--neutral-600);
}

/* Enhanced Table Styles */
.td-description {
    max-width: 250px;
}

.description-cell {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.description-tooltip {
    color: var(--neutral-500);
    cursor: help;
}

.description-tooltip:hover {
    color: var(--primary-color);
}

.td-ip {
    font-family: monospace;
    font-size: 0.875rem;
}

.ip-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    background-color: var(--neutral-100);
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
}

.timestamp-ago {
    font-size: 0.75rem;
    margin-top: 0.25rem;
}

.user-id {
    font-size: 0.75rem;
    margin-top: 0.25rem;
}

.audit-section {
    background: white;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--neutral-200);
    overflow: hidden;
}

/* System Audit Table */
.audit-table-container {
    overflow: hidden;
}

.table-wrapper {
    overflow-x: auto;
}

.audit-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.875rem;
}

.table-head {
    background: var(--neutral-50);
    border-bottom: 1px solid var(--neutral-200);
}

.table-head th {
    padding: 1rem 0.75rem;
    text-align: left;
    font-weight: 600;
    color: var(--neutral-700);
    border: none;
}

.th-content {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.th-content i {
    color: var(--neutral-500);
    font-size: 0.875rem;
}

.table-body tr {
    border-bottom: 1px solid var(--neutral-100);
    transition: all 0.2s ease;
}

.table-body tr:hover {
    background: var(--neutral-50);
}

.table-body td {
    padding: 1rem 0.75rem;
    vertical-align: middle;
    border: none;
}

/* Timestamp Cell */
.timestamp-cell {
    display: flex;
    flex-direction: column;
    gap: 0.125rem;
}

.timestamp-date {
    font-weight: 600;
    color: var(--neutral-800);
}

.timestamp-time {
    font-size: 0.75rem;
    color: var(--neutral-600);
}

/* User Cell */
.user-cell {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.user-avatar {
    width: 2rem;
    height: 2rem;
    background: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 0.75rem;
    flex-shrink: 0;
}

.user-name {
    font-weight: 600;
    color: var(--neutral-800);
    margin-bottom: 0.125rem;
}

.user-type {
    font-size: 0.75rem;
    color: var(--neutral-600);
    text-transform: capitalize;
}

/* Action Badge */
.action-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.375rem;
    padding: 0.375rem 0.75rem;
    border-radius: var(--radius-md);
    font-size: 0.75rem;
    font-weight: 500;
}

.action-create {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
}

.action-update {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning-color);
}

.action-delete {
    background: rgba(239, 68, 68, 0.1);
    color: var(--danger-color);
}

.action-badge:not(.action-create):not(.action-update):not(.action-delete) {
    background: rgba(59, 130, 246, 0.1);
    color: var(--primary-color);
}

/* Model Badge */
.model-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.375rem;
    padding: 0.375rem 0.75rem;
    border-radius: var(--radius-md);
    font-size: 0.75rem;
    font-weight: 500;
    background: rgba(107, 114, 128, 0.1);
    color: var(--neutral-600);
}

.model-id {
    font-weight: 700;
    color: var(--primary-color);
}

/* Action Buttons */
.action-buttons {
    display: flex;
    gap: 0.375rem;
    justify-content: center;
}

.action-btn {
    width: 2rem;
    height: 2rem;
    border: 1px solid var(--neutral-300);
    border-radius: var(--radius-sm);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.875rem;
    text-decoration: none;
    background: white;
}

.action-btn-view {
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.action-btn-view:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-1px);
}

/* Empty State */
.empty-row td {
    padding: 3rem 1rem;
}

.empty-state {
    text-align: center;
    color: var(--neutral-600);
}

.empty-icon {
    font-size: 3rem;
    color: var(--neutral-400);
    margin-bottom: 1rem;
}

.empty-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--neutral-700);
    margin-bottom: 0.5rem;
}

.empty-subtitle {
    font-size: 0.875rem;
    color: var(--neutral-600);
    margin-bottom: 1.5rem;
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
}

/* Pagination */
.pagination-section {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1.5rem;
    border-top: 1px solid var(--neutral-200);
    background: var(--neutral-50);
}

.pagination-text {
    font-size: 0.875rem;
    color: var(--neutral-600);
}

/* Vote Audit List */
.vote-audit-list {
    padding: 1rem;
    max-height: 600px;
    overflow-y: auto;
}

.vote-audit-item {
    padding: 1rem;
    border: 1px solid var(--neutral-200);
    border-radius: var(--radius-md);
    margin-bottom: 1rem;
    transition: all 0.2s ease;
}

.vote-audit-item:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-1px);
}

.vote-audit-item.flagged {
    border-color: var(--danger-color);
    background: rgba(239, 68, 68, 0.05);
}

.audit-item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.75rem;
}

.audit-time {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    color: var(--neutral-600);
}

.audit-flag {
    display: flex;
    align-items: center;
    gap: 0.375rem;
    background: var(--danger-color);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    font-weight: 500;
}

.audit-item-content {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin-bottom: 0.75rem;
}

.audit-user,
.audit-station,
.audit-action {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    color: var(--neutral-700);
}

.audit-item-actions {
    text-align: right;
}

.audit-link {
    color: var(--primary-color);
    text-decoration: none;
    font-size: 0.875rem;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    gap: 0.375rem;
    transition: all 0.2s ease;
}

.audit-link:hover {
    color: var(--primary-dark);
    transform: translateX(2px);
}

.empty-vote-audits {
    text-align: center;
    padding: 2rem;
    color: var(--neutral-600);
}

.empty-vote-audits .empty-icon {
    font-size: 2rem;
    color: var(--neutral-400);
    margin-bottom: 0.5rem;
}

.empty-text {
    font-size: 0.875rem;
}

.section-footer {
    padding: 1.5rem;
    border-top: 1px solid var(--neutral-200);
    background: var(--neutral-50);
    text-align: center;
}

/* Mobile Responsive */
@media (max-width: 1024px) {
    .audit-content-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        align-items: stretch;
        text-align: center;
    }

    .header-info {
        justify-content: center;
        margin-bottom: 1rem;
    }

    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }

    .filters-row {
        flex-direction: column;
        align-items: stretch;
    }

    .search-group {
        min-width: 100%;
        order: 1;
    }

    .filter-group {
        order: 2;
    }

    .filter-actions {
        order: 3;
        justify-content: center;
        margin-top: 0.5rem;
    }

    .table-wrapper {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }

    .audit-table {
        min-width: 600px;
    }

    .pagination-section {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
}
</style>
@endsection
