@extends('layouts.app')

@section('content')
<div class="container-fluid px-4">
    <!-- Modern Header -->
    <div class="create-header mb-4">
        <div class="header-content">
            <div class="header-info">
                <div class="header-icon">
                    <i class="bi bi-person-plus-fill"></i>
                </div>
                <div class="header-text">
                    <h1 class="header-title">Create New User</h1>
                    <p class="header-subtitle">Add a new user to the system with roles and permissions</p>
                </div>
            </div>
            <div class="header-actions">
                <a href="{{ route('admin.users.index') }}" class="btn btn-secondary btn-modern">
                    <i class="bi bi-arrow-left"></i>
                    <span>Back to Users</span>
                </a>
            </div>
        </div>
    </div>

    <!-- Create User Form -->
    <div class="create-form-container">
        <form method="POST" action="{{ route('admin.users.store') }}" class="create-user-form">
            @csrf

            <!-- Personal Information Section -->
            <div class="form-section">
                <div class="section-header">
                    <div class="section-icon">
                        <i class="bi bi-person-circle"></i>
                    </div>
                    <div class="section-info">
                        <h3 class="section-title">Personal Information</h3>
                        <p class="section-subtitle">Basic user details and contact information</p>
                    </div>
                </div>

                <div class="section-content">
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="name" class="form-label">
                                <i class="bi bi-person"></i>
                                <span>Full Name</span>
                                <span class="required">*</span>
                            </label>
                            <div class="input-wrapper">
                                <input type="text"
                                       class="form-input @error('name') is-invalid @enderror"
                                       id="name"
                                       name="name"
                                       value="{{ old('name') }}"
                                       placeholder="Enter full name"
                                       required>
                                @error('name')
                                    <div class="error-message">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="phone_number" class="form-label">
                                <i class="bi bi-telephone"></i>
                                <span>Phone Number</span>
                                <span class="required">*</span>
                            </label>
                            <div class="input-wrapper">
                                <input type="tel"
                                       class="form-input @error('phone_number') is-invalid @enderror"
                                       id="phone_number"
                                       name="phone_number"
                                       value="{{ old('phone_number') }}"
                                       placeholder="+256700000000"
                                       required>
                                @error('phone_number')
                                    <div class="error-message">{{ $message }}</div>
                                @enderror
                                <div class="field-hint">Include country code (e.g., +256)</div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="password" class="form-label">
                                <i class="bi bi-lock"></i>
                                <span>Password</span>
                                <span class="required">*</span>
                            </label>
                            <div class="input-wrapper">
                                <input type="password"
                                       class="form-input @error('password') is-invalid @enderror"
                                       id="password"
                                       name="password"
                                       placeholder="Enter secure password"
                                       required>
                                @error('password')
                                    <div class="error-message">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="password_confirmation" class="form-label">
                                <i class="bi bi-lock-fill"></i>
                                <span>Confirm Password</span>
                                <span class="required">*</span>
                            </label>
                            <div class="input-wrapper">
                                <input type="password"
                                       class="form-input"
                                       id="password_confirmation"
                                       name="password_confirmation"
                                       placeholder="Confirm password"
                                       required>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Role & Permissions Section -->
            <div class="form-section">
                <div class="section-header">
                    <div class="section-icon">
                        <i class="bi bi-shield-check"></i>
                    </div>
                    <div class="section-info">
                        <h3 class="section-title">Role & Permissions</h3>
                        <p class="section-subtitle">Assign user role and access permissions</p>
                    </div>
                </div>

                <div class="section-content">
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="role_id" class="form-label">
                                <i class="bi bi-shield-check"></i>
                                <span>Primary Role</span>
                                <span class="required">*</span>
                            </label>
                            <div class="input-wrapper">
                                <select class="form-select @error('role_id') is-invalid @enderror"
                                        id="role_id" name="role_id" required>
                                    <option value="">Select a role</option>
                                    @foreach($roles as $role)
                                        <option value="{{ $role->id }}" {{ old('role_id') == $role->id ? 'selected' : '' }}>
                                            {{ $role->display_name }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('role_id')
                                    <div class="error-message">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="user_type" class="form-label">
                                <i class="bi bi-person-badge"></i>
                                <span>User Type</span>
                                <span class="required">*</span>
                            </label>
                            <div class="input-wrapper">
                                <select class="form-select @error('user_type') is-invalid @enderror"
                                        id="user_type" name="user_type" required>
                                    <option value="">Select user type</option>
                                    <option value="admin" {{ old('user_type') == 'admin' ? 'selected' : '' }}>Admin</option>
                                    <option value="manager" {{ old('user_type') == 'manager' ? 'selected' : '' }}>Manager</option>
                                    <option value="viewer" {{ old('user_type') == 'viewer' ? 'selected' : '' }}>Viewer</option>
                                    <option value="agent" {{ old('user_type') == 'agent' ? 'selected' : '' }}>Agent</option>
                                </select>
                                @error('user_type')
                                    <div class="error-message">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="form-group">
                            <div class="checkbox-group">
                                <label class="checkbox-label">
                                    <input type="checkbox"
                                           class="checkbox-input"
                                           id="is_active"
                                           name="is_active"
                                           value="1"
                                           {{ old('is_active', true) ? 'checked' : '' }}>
                                    <span class="checkbox-custom"></span>
                                    <span class="checkbox-text">
                                        <i class="bi bi-toggle-on"></i>
                                        Activate user account
                                    </span>
                                </label>
                                <div class="checkbox-hint">User will be able to login immediately</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="form-actions">
                <div class="actions-content">
                    <button type="button" class="btn btn-secondary btn-modern" onclick="window.history.back()">
                        <i class="bi bi-x-circle"></i>
                        <span>Cancel</span>
                    </button>
                    <button type="submit" class="btn btn-primary btn-modern">
                        <i class="bi bi-check-circle"></i>
                        <span>Create User</span>
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

@endsection

@section('styles')
<style>
/* Create User - Modern Design */
:root {
    --primary-color: #3b82f6;
    --primary-dark: #2563eb;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --neutral-50: #f9fafb;
    --neutral-100: #f3f4f6;
    --neutral-200: #e5e7eb;
    --neutral-300: #d1d5db;
    --neutral-400: #9ca3af;
    --neutral-500: #6b7280;
    --neutral-600: #4b5563;
    --neutral-700: #374151;
    --neutral-800: #1f2937;
    --neutral-900: #111827;
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
}

/* Header */
.create-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    border-radius: var(--radius-xl);
    padding: 2rem;
    color: white;
    box-shadow: var(--shadow-lg);
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 1rem;
}

.header-info {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.header-icon {
    width: 3rem;
    height: 3rem;
    background: rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    backdrop-filter: blur(10px);
}

.header-title {
    font-size: 1.875rem;
    font-weight: 700;
    margin: 0;
    line-height: 1.2;
}

.header-subtitle {
    font-size: 1rem;
    opacity: 0.9;
    margin: 0.25rem 0 0 0;
}

.header-actions .btn-modern {
    background: rgba(255, 255, 255, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    backdrop-filter: blur(10px);
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.25rem;
    border-radius: var(--radius-lg);
    font-weight: 500;
    transition: all 0.3s ease;
    text-decoration: none;
}

.header-actions .btn-modern:hover {
    background: rgba(255, 255, 255, 0.25);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    color: white;
}

/* Form Container */
.create-form-container {
    max-width: 800px;
    margin: 0 auto;
}

.create-user-form {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

/* Form Sections */
.form-section {
    background: white;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--neutral-200);
    overflow: hidden;
}

.section-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1.5rem;
    background: var(--neutral-50);
    border-bottom: 1px solid var(--neutral-200);
}

.section-icon {
    width: 2.5rem;
    height: 2.5rem;
    background: var(--primary-color);
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.25rem;
}

.section-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--neutral-800);
    margin: 0;
}

.section-subtitle {
    font-size: 0.875rem;
    color: var(--neutral-600);
    margin: 0.25rem 0 0 0;
}

.section-content {
    padding: 2rem;
}

/* Form Grid */
.form-grid {
    display: grid;
    gap: 1.5rem;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

/* Form Groups */
.form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.form-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
    color: var(--neutral-700);
    font-size: 0.875rem;
}

.form-label i {
    color: var(--primary-color);
    font-size: 1rem;
}

.required {
    color: var(--danger-color);
    font-weight: 700;
}

.input-wrapper {
    position: relative;
}

.form-input,
.form-select {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 1px solid var(--neutral-300);
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    background: white;
    color: var(--neutral-700);
    transition: all 0.2s ease;
}

.form-input:focus,
.form-select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-input.is-invalid,
.form-select.is-invalid {
    border-color: var(--danger-color);
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.error-message {
    color: var(--danger-color);
    font-size: 0.75rem;
    margin-top: 0.25rem;
}

.field-hint {
    color: var(--neutral-500);
    font-size: 0.75rem;
    margin-top: 0.25rem;
}

/* Checkbox Group */
.checkbox-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    cursor: pointer;
    font-weight: 500;
    color: var(--neutral-700);
}

.checkbox-input {
    display: none;
}

.checkbox-custom {
    width: 1.25rem;
    height: 1.25rem;
    border: 2px solid var(--neutral-300);
    border-radius: var(--radius-sm);
    background: white;
    position: relative;
    transition: all 0.2s ease;
}

.checkbox-input:checked + .checkbox-custom {
    background: var(--primary-color);
    border-color: var(--primary-color);
}

.checkbox-input:checked + .checkbox-custom::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 0.75rem;
    font-weight: 700;
}

.checkbox-text {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.checkbox-hint {
    color: var(--neutral-500);
    font-size: 0.75rem;
    margin-left: 2rem;
}

/* Form Actions */
.form-actions {
    background: white;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--neutral-200);
    padding: 1.5rem;
}

.actions-content {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
}

.btn-modern {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border-radius: var(--radius-lg);
    font-weight: 500;
    font-size: 0.875rem;
    border: 1px solid transparent;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background: var(--primary-dark);
    border-color: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.25);
}

.btn-secondary {
    background: white;
    color: var(--neutral-600);
    border-color: var(--neutral-300);
}

.btn-secondary:hover {
    background: var(--neutral-50);
    color: var(--neutral-700);
    border-color: var(--neutral-400);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        align-items: stretch;
        text-align: center;
    }

    .header-info {
        justify-content: center;
        margin-bottom: 1rem;
    }

    .create-header {
        padding: 1.5rem;
    }

    .header-title {
        font-size: 1.5rem;
    }

    .section-content {
        padding: 1.5rem;
    }

    .form-grid {
        grid-template-columns: 1fr;
    }

    .actions-content {
        flex-direction: column-reverse;
    }

    .btn-modern {
        justify-content: center;
    }
}
</style>
@endsection

@section('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Form validation
    const form = document.querySelector('.create-user-form');
    const inputs = form.querySelectorAll('.form-input, .form-select');

    // Real-time validation
    inputs.forEach(input => {
        input.addEventListener('blur', function() {
            validateField(this);
        });

        input.addEventListener('input', function() {
            if (this.classList.contains('is-invalid')) {
                validateField(this);
            }
        });
    });

    function validateField(field) {
        const value = field.value.trim();
        const isRequired = field.hasAttribute('required');

        // Remove existing error state
        field.classList.remove('is-invalid');
        const existingError = field.parentNode.querySelector('.error-message');
        if (existingError) {
            existingError.remove();
        }

        // Validate required fields
        if (isRequired && !value) {
            showFieldError(field, 'This field is required');
            return false;
        }

        // Specific validations
        if (field.name === 'phone_number' && value) {
            if (!/^\+\d{10,15}$/.test(value)) {
                showFieldError(field, 'Please enter a valid phone number with country code');
                return false;
            }
        }

        if (field.name === 'password' && value) {
            if (value.length < 8) {
                showFieldError(field, 'Password must be at least 8 characters long');
                return false;
            }
        }

        if (field.name === 'password_confirmation' && value) {
            const password = form.querySelector('[name="password"]').value;
            if (value !== password) {
                showFieldError(field, 'Passwords do not match');
                return false;
            }
        }

        return true;
    }

    function showFieldError(field, message) {
        field.classList.add('is-invalid');
        const errorDiv = document.createElement('div');
        errorDiv.className = 'error-message';
        errorDiv.textContent = message;
        field.parentNode.appendChild(errorDiv);
    }

    // Form submission
    form.addEventListener('submit', function(e) {
        let isValid = true;

        inputs.forEach(input => {
            if (!validateField(input)) {
                isValid = false;
            }
        });

        if (!isValid) {
            e.preventDefault();
            // Scroll to first error
            const firstError = form.querySelector('.is-invalid');
            if (firstError) {
                firstError.scrollIntoView({ behavior: 'smooth', block: 'center' });
                firstError.focus();
            }
        }
    });

    // Password confirmation matching
    const passwordField = form.querySelector('[name="password"]');
    const confirmField = form.querySelector('[name="password_confirmation"]');

    if (passwordField && confirmField) {
        [passwordField, confirmField].forEach(field => {
            field.addEventListener('input', function() {
                if (confirmField.value && passwordField.value !== confirmField.value) {
                    confirmField.classList.add('is-invalid');
                    let errorDiv = confirmField.parentNode.querySelector('.error-message');
                    if (!errorDiv) {
                        errorDiv = document.createElement('div');
                        errorDiv.className = 'error-message';
                        confirmField.parentNode.appendChild(errorDiv);
                    }
                    errorDiv.textContent = 'Passwords do not match';
                } else {
                    confirmField.classList.remove('is-invalid');
                    const errorDiv = confirmField.parentNode.querySelector('.error-message');
                    if (errorDiv) {
                        errorDiv.remove();
                    }
                }
            });
        });
    }
});
</script>
@endsection
