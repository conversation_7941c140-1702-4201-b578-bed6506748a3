@extends('layouts.app')

@section('content')
<div class="container-fluid px-4">
    <!-- Modern Header Section -->
    <div class="admin-header mb-4">
        <div class="header-content">
            <div class="header-info">
                <div class="header-icon">
                    <i class="bi bi-people-fill"></i>
                </div>
                <div class="header-text">
                    <h1 class="header-title">User Management</h1>
                    <p class="header-subtitle">Manage system users, roles, and permissions</p>
                </div>
            </div>
            <div class="header-actions">
                <div class="action-buttons">
                    @if(auth()->user()->hasPermission('view_users'))
                    <a href="{{ route('admin.users.export') }}" class="btn btn-success btn-modern">
                        <i class="bi bi-download"></i>
                        <span>Export</span>
                    </a>
                    @endif
                    @if(auth()->user()->hasPermission('create_users'))
                    <a href="{{ route('admin.users.create') }}" class="btn btn-primary btn-modern">
                        <i class="bi bi-plus-circle"></i>
                        <span>Add User</span>
                    </a>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Compact Filters Section -->
    <div class="filters-card mb-4">
        <form method="GET" class="filters-form">
            <div class="filters-row">
                <!-- Search Input -->
                <div class="filter-group search-group">
                    <div class="search-input-wrapper">
                        <i class="bi bi-search search-icon"></i>
                        <input type="text"
                               class="search-input"
                               name="search"
                               value="{{ request('search') }}"
                               placeholder="Search users..."
                               autocomplete="off">
                        @if(request('search'))
                        <button type="button" class="search-clear" onclick="clearSearch()">
                            <i class="bi bi-x"></i>
                        </button>
                        @endif
                    </div>
                </div>

                <!-- Role Filter -->
                <div class="filter-group">
                    <select class="filter-select" name="role">
                        <option value="">All Roles</option>
                        @foreach($roles as $role)
                            <option value="{{ $role->id }}" {{ request('role') == $role->id ? 'selected' : '' }}>
                                {{ $role->display_name }}
                            </option>
                        @endforeach
                    </select>
                </div>

                <!-- Status Filter -->
                <div class="filter-group">
                    <select class="filter-select" name="status">
                        <option value="">All Status</option>
                        <option value="active" {{ request('status') == 'active' ? 'selected' : '' }}>Active</option>
                        <option value="inactive" {{ request('status') == 'inactive' ? 'selected' : '' }}>Inactive</option>
                    </select>
                </div>

                <!-- Per Page -->
                <div class="filter-group">
                    <select class="filter-select" name="per_page" onchange="this.form.submit()">
                        <option value="15" {{ request('per_page', 15) == 15 ? 'selected' : '' }}>15 per page</option>
                        <option value="25" {{ request('per_page') == 25 ? 'selected' : '' }}>25 per page</option>
                        <option value="50" {{ request('per_page') == 50 ? 'selected' : '' }}>50 per page</option>
                        <option value="100" {{ request('per_page') == 100 ? 'selected' : '' }}>100 per page</option>
                    </select>
                </div>

                <!-- Filter Actions -->
                <div class="filter-actions">
                    <button type="submit" class="btn-filter-apply" title="Apply Filters">
                        <i class="bi bi-funnel"></i>
                    </button>
                    @if(request()->hasAny(['search', 'role', 'status']))
                    <a href="{{ route('admin.users.index') }}" class="btn-filter-clear" title="Clear Filters">
                        <i class="bi bi-x-circle"></i>
                    </a>
                    @endif
                </div>
            </div>

            <!-- Active Filters Display -->
            @if(request()->hasAny(['search', 'role', 'status']))
            <div class="active-filters">
                <span class="active-filters-label">Active filters:</span>
                <div class="active-filters-list">
                    @if(request('search'))
                        <span class="filter-tag">
                            <i class="bi bi-search"></i>
                            Search: {{ request('search') }}
                            <button type="button" onclick="removeFilter('search')" class="filter-tag-remove">
                                <i class="bi bi-x"></i>
                            </button>
                        </span>
                    @endif
                    @if(request('role'))
                        <span class="filter-tag">
                            <i class="bi bi-shield-check"></i>
                            Role: {{ $roles->find(request('role'))->display_name ?? 'Unknown' }}
                            <button type="button" onclick="removeFilter('role')" class="filter-tag-remove">
                                <i class="bi bi-x"></i>
                            </button>
                        </span>
                    @endif
                    @if(request('status'))
                        <span class="filter-tag">
                            <i class="bi bi-toggle-on"></i>
                            Status: {{ ucfirst(request('status')) }}
                            <button type="button" onclick="removeFilter('status')" class="filter-tag-remove">
                                <i class="bi bi-x"></i>
                            </button>
                        </span>
                    @endif
                </div>
            </div>
            @endif
        </form>
    </div>

    <!-- Users Data Section -->
    <div class="users-data-section">
        <div class="data-header">
            <div class="data-info">
                <h2 class="data-title">
                    <i class="bi bi-people"></i>
                    Users
                </h2>
                <p class="data-subtitle">{{ $users->total() }} total users</p>
            </div>
            <div class="data-actions">
                <div class="view-toggle">
                    <button type="button" class="view-btn active" data-view="table" title="Table View">
                        <i class="bi bi-table"></i>
                    </button>
                    <button type="button" class="view-btn" data-view="grid" title="Grid View">
                        <i class="bi bi-grid-3x3-gap"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Compact Users Table -->
        <div class="users-table-container">
            <div class="table-wrapper">
                <table class="users-table">
                    <thead class="table-head">
                        <tr>
                            <th class="th-user">
                                <div class="th-content">
                                    <i class="bi bi-person"></i>
                                    <span>User</span>
                                </div>
                            </th>
                            <th class="th-contact">
                                <div class="th-content">
                                    <i class="bi bi-telephone"></i>
                                    <span>Contact</span>
                                </div>
                            </th>
                            <th class="th-role">
                                <div class="th-content">
                                    <i class="bi bi-shield-check"></i>
                                    <span>Role</span>
                                </div>
                            </th>
                            <th class="th-status">
                                <div class="th-content">
                                    <i class="bi bi-toggle-on"></i>
                                    <span>Status</span>
                                </div>
                            </th>
                            <th class="th-actions">
                                <div class="th-content">
                                    <i class="bi bi-gear"></i>
                                    <span>Actions</span>
                                </div>
                            </th>
                        </tr>
                    </thead>
                    <tbody class="table-body">
                        @forelse($users as $user)
                        <tr class="user-row" data-user-id="{{ $user->id }}">
                            <td class="td-user">
                                <div class="user-cell">
                                    <div class="user-avatar">
                                        <span>{{ strtoupper(substr($user->name, 0, 1)) }}</span>
                                    </div>
                                    <div class="user-info">
                                        <div class="user-name">{{ $user->name }}</div>
                                        <div class="user-type">{{ ucfirst($user->user_type) }}</div>
                                    </div>
                                </div>
                            </td>
                            <td class="td-contact">
                                <div class="contact-cell">
                                    <div class="phone-number">{{ $user->phone_number }}</div>
                                    <div class="contact-label">Primary contact</div>
                                </div>
                            </td>
                            <td class="td-role">
                                @if($user->role)
                                    <div class="role-badge role-assigned">
                                        <i class="bi bi-shield-check"></i>
                                        <span>{{ $user->role->display_name }}</span>
                                    </div>
                                @else
                                    <div class="role-badge role-none">
                                        <i class="bi bi-shield-x"></i>
                                        <span>No Role</span>
                                    </div>
                                @endif
                            </td>
                            <td class="td-status">
                                @if($user->is_active)
                                    <div class="status-badge status-active">
                                        <i class="bi bi-check-circle"></i>
                                        <span>Active</span>
                                    </div>
                                @else
                                    <div class="status-badge status-inactive">
                                        <i class="bi bi-x-circle"></i>
                                        <span>Inactive</span>
                                    </div>
                                @endif
                            </td>
                            <td class="td-actions">
                                <div class="action-buttons">
                                    @if(auth()->user()->hasPermission('view_users'))
                                    <a href="{{ route('admin.users.show', $user) }}"
                                       class="action-btn action-btn-view"
                                       title="View Details">
                                        <i class="bi bi-eye"></i>
                                    </a>
                                    @endif

                                    @if(auth()->user()->hasPermission('edit_users'))
                                    <a href="{{ route('admin.users.edit', $user) }}"
                                       class="action-btn action-btn-edit"
                                       title="Edit User">
                                        <i class="bi bi-pencil"></i>
                                    </a>
                                    @endif

                                    @if(auth()->user()->hasPermission('edit_users'))
                                    <form method="POST" action="{{ route('admin.users.toggle-status', $user) }}" class="d-inline">
                                        @csrf
                                        @method('PATCH')
                                        <button type="submit"
                                                class="action-btn action-btn-toggle"
                                                title="{{ $user->is_active ? 'Deactivate' : 'Activate' }} User"
                                                onclick="return confirm('Are you sure you want to {{ $user->is_active ? 'deactivate' : 'activate' }} this user?')">
                                            <i class="bi bi-{{ $user->is_active ? 'pause' : 'play' }}"></i>
                                        </button>
                                    </form>
                                    @endif

                                    @if(auth()->user()->hasPermission('delete_users') && $user->id !== auth()->id())
                                    <form method="POST" action="{{ route('admin.users.destroy', $user) }}" class="d-inline">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit"
                                                class="action-btn action-btn-delete"
                                                title="Delete User"
                                                onclick="return confirm('Are you sure you want to delete this user? This action cannot be undone.')">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </form>
                                    @endif
                                </div>
                            </td>
                        </tr>
                        @empty
                        <tr class="empty-row">
                            <td colspan="5" class="empty-cell">
                                <div class="empty-state">
                                    <div class="empty-icon">
                                        <i class="bi bi-people"></i>
                                    </div>
                                    <div class="empty-title">No Users Found</div>
                                    <div class="empty-subtitle">
                                        @if(request()->hasAny(['search', 'role', 'status']))
                                            No users match your current filters. Try adjusting your search criteria.
                                        @else
                                            There are no users in the system yet.
                                        @endif
                                    </div>
                                    @if(auth()->user()->hasPermission('create_users'))
                                    <a href="{{ route('admin.users.create') }}" class="btn btn-primary btn-modern">
                                        <i class="bi bi-plus-circle"></i>
                                        <span>Add First User</span>
                                    </a>
                                    @endif
                                </div>
                            </td>
                        </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Compact Pagination -->
    @if($users->hasPages())
    <div class="pagination-section">
        <div class="pagination-info">
            <span class="pagination-text">
                Showing {{ $users->firstItem() }}-{{ $users->lastItem() }} of {{ $users->total() }} users
            </span>
        </div>
        <div class="pagination-controls">
            {{ $users->links() }}
        </div>
    </div>
    @endif
</div>

<script>
// Filter management
function clearSearch() {
    document.querySelector('input[name="search"]').value = '';
    document.querySelector('.filters-form').submit();
}

function removeFilter(filterName) {
    const form = document.querySelector('.filters-form');
    const input = form.querySelector(`[name="${filterName}"]`);
    if (input) {
        input.value = '';
        form.submit();
    }
}

// View toggle functionality
document.addEventListener('DOMContentLoaded', function() {
    const viewButtons = document.querySelectorAll('.view-btn');
    viewButtons.forEach(button => {
        button.addEventListener('click', function() {
            viewButtons.forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');

            const view = this.dataset.view;
            // Add view switching logic here if needed
        });
    });
});
</script>

@endsection

@section('styles')
<style>
/* Admin Users - Modern Compact Design */
:root {
    --primary-color: #3b82f6;
    --primary-dark: #2563eb;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --neutral-50: #f9fafb;
    --neutral-100: #f3f4f6;
    --neutral-200: #e5e7eb;
    --neutral-300: #d1d5db;
    --neutral-400: #9ca3af;
    --neutral-500: #6b7280;
    --neutral-600: #4b5563;
    --neutral-700: #374151;
    --neutral-800: #1f2937;
    --neutral-900: #111827;
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
}

/* Modern Header */
.admin-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    border-radius: var(--radius-xl);
    padding: 2rem;
    color: white;
    box-shadow: var(--shadow-lg);
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 1rem;
}

.header-info {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.header-icon {
    width: 3rem;
    height: 3rem;
    background: rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    backdrop-filter: blur(10px);
}

.header-title {
    font-size: 1.875rem;
    font-weight: 700;
    margin: 0;
    line-height: 1.2;
}

.header-subtitle {
    font-size: 1rem;
    opacity: 0.9;
    margin: 0.25rem 0 0 0;
}

.header-actions .btn-modern {
    background: rgba(255, 255, 255, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    backdrop-filter: blur(10px);
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.25rem;
    border-radius: var(--radius-lg);
    font-weight: 500;
    transition: all 0.3s ease;
}

.header-actions .btn-modern:hover {
    background: rgba(255, 255, 255, 0.25);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.action-buttons {
    display: flex;
    gap: 0.75rem;
    flex-wrap: wrap;
}

.btn-success {
    background: rgba(16, 185, 129, 0.15);
    border: 1px solid rgba(16, 185, 129, 0.2);
    color: white;
}

.btn-success:hover {
    background: rgba(16, 185, 129, 0.25);
    border-color: rgba(16, 185, 129, 0.3);
}

/* Compact Filters */
.filters-card {
    background: white;
    border-radius: var(--radius-lg);
    padding: 1.25rem;
    box-shadow: var(--shadow-md);
    border: 1px solid var(--neutral-200);
}

.filters-row {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    flex-wrap: wrap;
}

.filter-group {
    position: relative;
}

.search-group {
    flex: 1;
    min-width: 250px;
}

.search-input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
}

.search-icon {
    position: absolute;
    left: 0.75rem;
    color: var(--neutral-400);
    font-size: 1rem;
    z-index: 2;
}

.search-input {
    width: 100%;
    padding: 0.75rem 0.75rem 0.75rem 2.5rem;
    border: 1px solid var(--neutral-300);
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    background: white;
    transition: all 0.2s ease;
}

.search-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.search-clear {
    position: absolute;
    right: 0.5rem;
    width: 1.5rem;
    height: 1.5rem;
    border: none;
    background: var(--neutral-200);
    color: var(--neutral-600);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.search-clear:hover {
    background: var(--neutral-300);
    transform: scale(1.1);
}

.filter-select {
    padding: 0.75rem 1rem;
    border: 1px solid var(--neutral-300);
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    background: white;
    color: var(--neutral-700);
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 140px;
}

.filter-select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.filter-actions {
    display: flex;
    gap: 0.5rem;
}

.btn-filter-apply,
.btn-filter-clear {
    width: 2.5rem;
    height: 2.5rem;
    border: 1px solid var(--neutral-300);
    border-radius: var(--radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.875rem;
    text-decoration: none;
}

.btn-filter-apply {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.btn-filter-apply:hover {
    background: var(--primary-dark);
    border-color: var(--primary-dark);
    transform: translateY(-1px);
}

.btn-filter-clear {
    background: white;
    color: var(--danger-color);
    border-color: var(--danger-color);
}

.btn-filter-clear:hover {
    background: var(--danger-color);
    color: white;
    transform: translateY(-1px);
}

/* Active Filters */
.active-filters {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid var(--neutral-200);
}

.active-filters-label {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--neutral-600);
    flex-shrink: 0;
}

.active-filters-list {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    flex: 1;
}

.filter-tag {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: var(--primary-color);
    color: white;
    padding: 0.375rem 0.75rem;
    border-radius: var(--radius-md);
    font-size: 0.75rem;
    font-weight: 500;
}

.filter-tag-remove {
    background: none;
    border: none;
    color: white;
    font-size: 0.875rem;
    cursor: pointer;
    padding: 0;
    margin-left: 0.25rem;
    border-radius: 50%;
    width: 1rem;
    height: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.filter-tag-remove:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.1);
}

/* Data Section */
.users-data-section {
    background: white;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--neutral-200);
    overflow: hidden;
}

.data-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1.5rem;
    border-bottom: 1px solid var(--neutral-200);
    background: var(--neutral-50);
}

.data-title {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--neutral-800);
    margin: 0;
}

.data-title i {
    color: var(--primary-color);
}

.data-subtitle {
    font-size: 0.875rem;
    color: var(--neutral-600);
    margin: 0.25rem 0 0 0;
}

.view-toggle {
    display: flex;
    background: var(--neutral-100);
    border-radius: var(--radius-md);
    padding: 0.25rem;
}

.view-btn {
    padding: 0.5rem 0.75rem;
    border: none;
    background: transparent;
    color: var(--neutral-600);
    border-radius: var(--radius-sm);
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.875rem;
}

.view-btn.active {
    background: white;
    color: var(--primary-color);
    box-shadow: var(--shadow-sm);
}

.view-btn:hover:not(.active) {
    color: var(--neutral-800);
}

/* Compact Table */
.users-table-container {
    overflow: hidden;
}

.table-wrapper {
    overflow-x: auto;
}

.users-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.875rem;
}

.table-head {
    background: var(--neutral-50);
    border-bottom: 1px solid var(--neutral-200);
}

.table-head th {
    padding: 1rem 0.75rem;
    text-align: left;
    font-weight: 600;
    color: var(--neutral-700);
    border: none;
}

.th-content {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.th-content i {
    color: var(--neutral-500);
    font-size: 0.875rem;
}

.table-body tr {
    border-bottom: 1px solid var(--neutral-100);
    transition: all 0.2s ease;
}

.table-body tr:hover {
    background: var(--neutral-50);
}

.table-body td {
    padding: 1rem 0.75rem;
    vertical-align: middle;
    border: none;
}

/* User Cell */
.user-cell {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.user-avatar {
    width: 2.5rem;
    height: 2.5rem;
    background: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 0.875rem;
    flex-shrink: 0;
}

.user-name {
    font-weight: 600;
    color: var(--neutral-800);
    margin-bottom: 0.125rem;
}

.user-type {
    font-size: 0.75rem;
    color: var(--neutral-600);
    text-transform: capitalize;
}

/* Contact Cell */
.contact-cell {
    display: flex;
    flex-direction: column;
    gap: 0.125rem;
}

.phone-number {
    font-weight: 500;
    color: var(--neutral-800);
}

.contact-label {
    font-size: 0.75rem;
    color: var(--neutral-600);
}

/* Role Badge */
.role-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.375rem;
    padding: 0.375rem 0.75rem;
    border-radius: var(--radius-md);
    font-size: 0.75rem;
    font-weight: 500;
}

.role-assigned {
    background: rgba(59, 130, 246, 0.1);
    color: var(--primary-color);
}

.role-none {
    background: rgba(107, 114, 128, 0.1);
    color: var(--neutral-600);
}

/* Status Badge */
.status-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.375rem;
    padding: 0.375rem 0.75rem;
    border-radius: var(--radius-md);
    font-size: 0.75rem;
    font-weight: 500;
}

.status-active {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
}

.status-inactive {
    background: rgba(239, 68, 68, 0.1);
    color: var(--danger-color);
}

/* Action Buttons */
.action-buttons {
    display: flex;
    gap: 0.375rem;
    justify-content: center;
}

.action-btn {
    width: 2rem;
    height: 2rem;
    border: 1px solid var(--neutral-300);
    border-radius: var(--radius-sm);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.875rem;
    text-decoration: none;
    background: white;
}

.action-btn-view {
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.action-btn-view:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-1px);
}

.action-btn-edit {
    color: var(--warning-color);
    border-color: var(--warning-color);
}

.action-btn-edit:hover {
    background: var(--warning-color);
    color: white;
    transform: translateY(-1px);
}

.action-btn-toggle {
    color: var(--neutral-600);
    border-color: var(--neutral-300);
}

.action-btn-toggle:hover {
    background: var(--neutral-600);
    color: white;
    transform: translateY(-1px);
}

.action-btn-delete {
    color: var(--danger-color);
    border-color: var(--danger-color);
}

.action-btn-delete:hover {
    background: var(--danger-color);
    color: white;
    transform: translateY(-1px);
}

/* Empty State */
.empty-row td {
    padding: 3rem 1rem;
}

.empty-state {
    text-align: center;
    color: var(--neutral-600);
}

.empty-icon {
    font-size: 3rem;
    color: var(--neutral-400);
    margin-bottom: 1rem;
}

.empty-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--neutral-700);
    margin-bottom: 0.5rem;
}

.empty-subtitle {
    font-size: 0.875rem;
    color: var(--neutral-600);
    margin-bottom: 1.5rem;
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
}

/* Pagination */
.pagination-section {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1.5rem;
    border-top: 1px solid var(--neutral-200);
    background: var(--neutral-50);
}

.pagination-text {
    font-size: 0.875rem;
    color: var(--neutral-600);
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        align-items: stretch;
        text-align: center;
    }

    .header-info {
        justify-content: center;
        margin-bottom: 1rem;
    }

    .filters-row {
        flex-direction: column;
        align-items: stretch;
    }

    .search-group {
        min-width: 100%;
        order: 1;
    }

    .filter-group {
        order: 2;
    }

    .filter-actions {
        order: 3;
        justify-content: center;
        margin-top: 0.5rem;
    }

    .data-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .table-wrapper {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }

    .users-table {
        min-width: 600px;
    }

    .pagination-section {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .action-buttons {
        flex-wrap: wrap;
    }
}

@media (max-width: 480px) {
    .admin-header {
        padding: 1.5rem;
    }

    .header-title {
        font-size: 1.5rem;
    }

    .filters-card {
        padding: 1rem;
    }

    .users-table {
        min-width: 500px;
        font-size: 0.8rem;
    }

    .table-head th,
    .table-body td {
        padding: 0.75rem 0.5rem;
    }

    .user-avatar {
        width: 2rem;
        height: 2rem;
        font-size: 0.75rem;
    }

    .action-btn {
        width: 1.75rem;
        height: 1.75rem;
        font-size: 0.75rem;
    }
}
</style>
@endsection
