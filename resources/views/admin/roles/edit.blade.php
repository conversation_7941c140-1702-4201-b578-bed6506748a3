@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">
                        <i class="bi bi-pencil-square text-primary me-2"></i>
                        Edit Role: {{ $role->display_name }}
                    </h4>
                    <a href="{{ route('admin.roles.show', $role) }}" class="btn btn-outline-secondary">
                        <i class="bi bi-arrow-left me-1"></i>
                        Back to Role
                    </a>
                </div>

                <div class="card-body">
                    @if(session('success'))
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="bi bi-check-circle me-2"></i>{{ session('success') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    @endif

                    @if($errors->any())
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="bi bi-exclamation-triangle me-2"></i>
                            <strong>Please fix the following errors:</strong>
                            <ul class="mb-0 mt-2">
                                @foreach($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    @endif

                    <form action="{{ route('admin.roles.update', $role) }}" method="POST">
                        @csrf
                        @method('PUT')

                        <div class="row">
                            <!-- Role Information -->
                            <div class="col-md-6">
                                <div class="card h-100">
                                    <div class="card-header">
                                        <h5 class="mb-0">
                                            <i class="bi bi-info-circle text-info me-2"></i>
                                            Role Information
                                        </h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label for="name" class="form-label">Role Name</label>
                                            <input type="text" 
                                                   class="form-control @error('name') is-invalid @enderror" 
                                                   id="name" 
                                                   name="name" 
                                                   value="{{ old('name', $role->name) }}" 
                                                   required>
                                            @error('name')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                            <div class="form-text">Unique identifier for the role (lowercase, underscores allowed)</div>
                                        </div>

                                        <div class="mb-3">
                                            <label for="display_name" class="form-label">Display Name</label>
                                            <input type="text" 
                                                   class="form-control @error('display_name') is-invalid @enderror" 
                                                   id="display_name" 
                                                   name="display_name" 
                                                   value="{{ old('display_name', $role->display_name) }}" 
                                                   required>
                                            @error('display_name')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                            <div class="form-text">Human-readable name for the role</div>
                                        </div>

                                        <div class="mb-3">
                                            <label for="description" class="form-label">Description</label>
                                            <textarea class="form-control @error('description') is-invalid @enderror" 
                                                      id="description" 
                                                      name="description" 
                                                      rows="3">{{ old('description', $role->description) }}</textarea>
                                            @error('description')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                            <div class="form-text">Brief description of the role's purpose</div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Permissions -->
                            <div class="col-md-6">
                                <div class="card h-100">
                                    <div class="card-header">
                                        <h5 class="mb-0">
                                            <i class="bi bi-shield-check text-success me-2"></i>
                                            Permissions
                                        </h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <div class="d-flex justify-content-between align-items-center mb-2">
                                                <span class="form-label">Select Permissions</span>
                                                <div>
                                                    <button type="button" class="btn btn-sm btn-outline-success" onclick="selectAllPermissions()">
                                                        <i class="bi bi-check-all me-1"></i>Select All
                                                    </button>
                                                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="deselectAllPermissions()">
                                                        <i class="bi bi-x-circle me-1"></i>Deselect All
                                                    </button>
                                                </div>
                                            </div>
                                            
                                            <div class="permissions-container" style="max-height: 400px; overflow-y: auto; border: 1px solid #dee2e6; border-radius: 0.375rem; padding: 1rem;">
                                                @php
                                                    $currentPermissions = $role->permissions ?? [];
                                                    $groupedPermissions = [];
                                                    
                                                    foreach($allPermissions as $permission) {
                                                        $parts = explode('_', $permission);
                                                        $group = $parts[0];
                                                        $groupedPermissions[$group][] = $permission;
                                                    }
                                                @endphp

                                                @foreach($groupedPermissions as $group => $permissions)
                                                    <div class="permission-group mb-3">
                                                        <h6 class="text-primary mb-2">
                                                            <i class="bi bi-folder me-1"></i>
                                                            {{ ucwords(str_replace('_', ' ', $group)) }} Permissions
                                                        </h6>
                                                        <div class="row">
                                                            @foreach($permissions as $permission)
                                                                <div class="col-12 mb-1">
                                                                    <div class="form-check">
                                                                        <input class="form-check-input permission-checkbox" 
                                                                               type="checkbox" 
                                                                               name="permissions[]" 
                                                                               value="{{ $permission }}" 
                                                                               id="permission_{{ $permission }}"
                                                                               {{ in_array($permission, $currentPermissions) ? 'checked' : '' }}>
                                                                        <label class="form-check-label small" for="permission_{{ $permission }}">
                                                                            {{ ucwords(str_replace('_', ' ', $permission)) }}
                                                                        </label>
                                                                    </div>
                                                                </div>
                                                            @endforeach
                                                        </div>
                                                    </div>
                                                @endforeach
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Submit Buttons -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="d-flex justify-content-between">
                                    <a href="{{ route('admin.roles.show', $role) }}" class="btn btn-secondary">
                                        <i class="bi bi-x-circle me-1"></i>
                                        Cancel
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="bi bi-check-circle me-1"></i>
                                        Update Role
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
function selectAllPermissions() {
    document.querySelectorAll('.permission-checkbox').forEach(checkbox => {
        checkbox.checked = true;
    });
}

function deselectAllPermissions() {
    document.querySelectorAll('.permission-checkbox').forEach(checkbox => {
        checkbox.checked = false;
    });
}

// Form validation
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('form');
    
    form.addEventListener('submit', function(e) {
        const checkedPermissions = document.querySelectorAll('.permission-checkbox:checked');
        
        if (checkedPermissions.length === 0) {
            e.preventDefault();
            alert('Please select at least one permission for this role.');
            return false;
        }
    });
});
</script>
@endpush
@endsection
