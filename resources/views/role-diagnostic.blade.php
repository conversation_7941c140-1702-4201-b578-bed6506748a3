<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Role Configuration Diagnostic</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Inter', sans-serif;
        }
        .diagnostic-container {
            max-width: 800px;
            margin: 2rem auto;
            padding: 0 1rem;
        }
        .diagnostic-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
            overflow: hidden;
        }
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        .card-body {
            padding: 2rem;
        }
        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem;
            margin-bottom: 0.5rem;
            background: #f8f9fa;
            border-radius: 10px;
            border-left: 4px solid #dee2e6;
        }
        .status-item.success {
            border-left-color: #28a745;
            background: #d4edda;
        }
        .status-item.warning {
            border-left-color: #ffc107;
            background: #fff3cd;
        }
        .status-item.error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
        .btn-fix {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            border: none;
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: 10px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .btn-fix:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(40, 167, 69, 0.3);
            color: white;
        }
        .loading {
            display: none;
            text-align: center;
            padding: 2rem;
        }
        .spinner {
            width: 40px;
            height: 40px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 1rem;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .result-section {
            margin-top: 2rem;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 10px;
            display: none;
        }
        .fix-item {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 5px;
            padding: 0.75rem;
            margin-bottom: 0.5rem;
        }
    </style>
</head>
<body>
    <div class="diagnostic-container">
        <div class="diagnostic-card">
            <div class="card-header">
                <h2><i class="bi bi-shield-check"></i> Role Configuration Diagnostic</h2>
                <p class="mb-0">Diagnose and fix user role configuration issues</p>
            </div>
            <div class="card-body">
                <div id="initialStatus">
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle"></i>
                        <strong>Role Configuration Issue Detected</strong><br>
                        Your user account is active, but your role configuration may be incomplete. 
                        Click the button below to diagnose and automatically fix any issues.
                    </div>
                    
                    <div class="text-center">
                        <button class="btn btn-fix" onclick="diagnoseAndFix()">
                            <i class="bi bi-tools"></i> Diagnose & Fix Role Configuration
                        </button>
                    </div>
                </div>

                <div class="loading" id="loading">
                    <div class="spinner"></div>
                    <p>Diagnosing role configuration...</p>
                </div>

                <div class="result-section" id="results">
                    <!-- Results will be populated here -->
                </div>

                <div class="mt-4">
                    <h5>Quick Actions</h5>
                    <div class="row">
                        <div class="col-md-6 mb-2">
                            <button class="btn btn-outline-primary w-100" onclick="initializeRoles()">
                                <i class="bi bi-gear"></i> Initialize All Roles
                            </button>
                        </div>
                        <div class="col-md-6 mb-2">
                            <button class="btn btn-outline-secondary w-100" onclick="getUserInfo()">
                                <i class="bi bi-person"></i> View My Role Info
                            </button>
                        </div>
                    </div>
                </div>

                <div class="mt-4">
                    <div class="alert alert-light">
                        <h6><i class="bi bi-lightbulb"></i> What this tool does:</h6>
                        <ul class="mb-0">
                            <li>Checks if your user account has proper role assignments</li>
                            <li>Creates missing roles if they don't exist</li>
                            <li>Assigns the correct role based on your user type</li>
                            <li>Ensures you have the necessary permissions to access features</li>
                            <li>Fixes common role configuration issues automatically</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function showLoading() {
            document.getElementById('initialStatus').style.display = 'none';
            document.getElementById('results').style.display = 'none';
            document.getElementById('loading').style.display = 'block';
        }

        function hideLoading() {
            document.getElementById('loading').style.display = 'none';
        }

        function showResults(data) {
            const resultsDiv = document.getElementById('results');
            let html = '';

            if (data.status === 'success') {
                html += `<div class="alert alert-success">
                    <i class="bi bi-check-circle"></i> <strong>${data.message}</strong>
                </div>`;

                if (data.fixes_applied && data.fixes_applied.length > 0) {
                    html += '<h6>Fixes Applied:</h6>';
                    data.fixes_applied.forEach(fix => {
                        html += `<div class="fix-item"><i class="bi bi-check"></i> ${fix}</div>`;
                    });
                }

                html += '<h6>Diagnostic Results:</h6>';
                html += '<div class="row">';
                
                // User Status
                html += `<div class="col-md-6">
                    <div class="status-item ${data.diagnostics.user_active ? 'success' : 'error'}">
                        <span>User Active</span>
                        <i class="bi bi-${data.diagnostics.user_active ? 'check-circle text-success' : 'x-circle text-danger'}"></i>
                    </div>
                </div>`;

                // Role Assignment
                html += `<div class="col-md-6">
                    <div class="status-item ${data.diagnostics.has_primary_role ? 'success' : 'warning'}">
                        <span>Primary Role Assigned</span>
                        <i class="bi bi-${data.diagnostics.has_primary_role ? 'check-circle text-success' : 'exclamation-triangle text-warning'}"></i>
                    </div>
                </div>`;

                // Reports Access
                html += `<div class="col-md-6">
                    <div class="status-item ${data.diagnostics.can_access_reports ? 'success' : 'warning'}">
                        <span>Can Access Reports</span>
                        <i class="bi bi-${data.diagnostics.can_access_reports ? 'check-circle text-success' : 'exclamation-triangle text-warning'}"></i>
                    </div>
                </div>`;

                html += '</div>';

                html += `<div class="mt-3">
                    <p><strong>User Type:</strong> ${data.diagnostics.user_type}</p>
                    <p><strong>Expected Role:</strong> ${data.diagnostics.expected_role}</p>
                    <p><strong>Current Role:</strong> ${data.diagnostics.primary_role || 'None'}</p>
                </div>`;

                if (data.diagnostics.can_access_reports) {
                    html += `<div class="alert alert-success mt-3">
                        <i class="bi bi-check-circle"></i> 
                        <strong>Configuration Complete!</strong> You can now access the reports page.
                        <br><a href="/reports/polling-station-submission" class="btn btn-success btn-sm mt-2">
                            <i class="bi bi-graph-up"></i> Go to Reports
                        </a>
                    </div>`;
                }
            } else {
                html += `<div class="alert alert-danger">
                    <i class="bi bi-exclamation-triangle"></i> <strong>Error:</strong> ${data.message}
                </div>`;
            }

            resultsDiv.innerHTML = html;
            resultsDiv.style.display = 'block';
        }

        function diagnoseAndFix() {
            showLoading();
            
            fetch('/role-diagnostic')
                .then(response => response.json())
                .then(data => {
                    hideLoading();
                    showResults(data);
                })
                .catch(error => {
                    hideLoading();
                    console.error('Error:', error);
                    document.getElementById('results').innerHTML = `
                        <div class="alert alert-danger">
                            <i class="bi bi-exclamation-triangle"></i> 
                            <strong>Error:</strong> Failed to diagnose role configuration. Please try again.
                        </div>
                    `;
                    document.getElementById('results').style.display = 'block';
                });
        }

        function initializeRoles() {
            showLoading();
            
            fetch('/initialize-roles', { method: 'POST', headers: { 'X-CSRF-TOKEN': '{{ csrf_token() }}' } })
                .then(response => response.json())
                .then(data => {
                    hideLoading();
                    const resultsDiv = document.getElementById('results');
                    resultsDiv.innerHTML = `
                        <div class="alert alert-success">
                            <i class="bi bi-check-circle"></i> <strong>${data.message}</strong>
                            <br>Created: ${data.created.join(', ') || 'None'}
                            <br>Updated: ${data.updated.join(', ') || 'None'}
                        </div>
                    `;
                    resultsDiv.style.display = 'block';
                })
                .catch(error => {
                    hideLoading();
                    console.error('Error:', error);
                });
        }

        function getUserInfo() {
            showLoading();
            
            fetch('/role-info')
                .then(response => response.json())
                .then(data => {
                    hideLoading();
                    const user = data.user;
                    const resultsDiv = document.getElementById('results');
                    resultsDiv.innerHTML = `
                        <div class="alert alert-info">
                            <h6><i class="bi bi-person"></i> User Information</h6>
                            <p><strong>Name:</strong> ${user.name}</p>
                            <p><strong>User Type:</strong> ${user.user_type}</p>
                            <p><strong>Expected Role:</strong> ${user.expected_role}</p>
                            <p><strong>Current Role:</strong> ${user.primary_role ? user.primary_role.display_name : 'None'}</p>
                            <p><strong>Can Access Reports:</strong> ${user.can_access_reports ? 'Yes' : 'No'}</p>
                        </div>
                    `;
                    resultsDiv.style.display = 'block';
                })
                .catch(error => {
                    hideLoading();
                    console.error('Error:', error);
                });
        }
    </script>
</body>
</html>
