<!doctype html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <!-- CSRF <PERSON>ken -->
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>{{ config('app.name', 'Laravel') }}</title>

    <!-- Fonts -->
    <link rel="dns-prefetch" href="//fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=Nunito" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">

   <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.7/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-LN+7fdVzj6u52u30Kp6M/trliBMCMKTyK833zpbD+pXdCLuTusPj697FH4R/5mcr" crossorigin="anonymous">
   <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
   <link href="{{ asset('css/main.css') }}" rel="stylesheet">

   @yield('styles')
   
</head>
<body>
    <div id="app">
        @if(!request()->is('login') && !request()->is('/') && !Auth::guest())
        <nav class="navbar navbar-expand-lg navbar-custom sticky-top">
            <div class="container-fluid px-3">
                <a class="navbar-brand navbar-brand-custom" href="{{ url('/') }}">
                    <div class="brand-icon">
                        <i class="bi bi-check2-circle"></i>
                    </div>
                    {{ config('app.name', 'Vote Count') }}
                </a>
                <button class="navbar-toggler navbar-toggler-custom" type="button" data-bs-toggle="collapse" data-bs-target="#navbarSupportedContent" aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="{{ __('Toggle navigation') }}">
                    <span class="navbar-toggler-icon-custom"></span>
                </button>

                <div class="collapse navbar-collapse" id="navbarSupportedContent">
                    <!-- Left Side Of Navbar -->
                    <ul class="navbar-nav me-auto">
                        @guest
                        @else
                            <li class="nav-item">
                                <a class="nav-link nav-link-custom {{ request()->routeIs('home') ? 'active' : '' }}" href="{{ route('home') }}">
                                    <i class="bi bi-house-door"></i> {{ __('Dashboard') }}
                                </a>
                            </li>

                            @if(Auth::check() && Auth::user()->hasPermission('view_polling_stations'))
                                <li class="nav-item">
                                    <a class="nav-link nav-link-custom {{ request()->routeIs('manager.*') ? 'active' : '' }}" href="{{ route('manager.dashboard') }}">
                                        <i class="bi bi-calculator"></i> {{ __('Tallying') }}
                                    </a>
                                </li>
                            @endif

                            @if(Auth::check() && Auth::user()->hasPermission('view_dashboard'))
                                <li class="nav-item dropdown">
                                    <a class="nav-link nav-link-custom dropdown-toggle {{ request()->routeIs('reports.*') ? 'active' : '' }}"
                                       href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                        <i class="bi bi-graph-up"></i> {{ __('Reports') }}
                                    </a>
                                    <ul class="dropdown-menu">
                                        <li>
                                            <a class="dropdown-item {{ request()->routeIs('reports.polling-station-submission') ? 'active' : '' }}"
                                               href="{{ route('reports.polling-station-submission') }}">
                                                <i class="bi bi-clipboard-data"></i> Submission Report
                                            </a>
                                        </li>
                                    </ul>
                                </li>
                            @endif

                            @if(Auth::check() && Auth::user()->user_type === 'admin')
                                <li class="nav-item">
                                    <a class="nav-link nav-link-custom {{ request()->routeIs('positions.*') ? 'active' : '' }}" href="{{ route('positions.index') }}">
                                        <i class="bi bi-person-badge"></i> {{ __('Positions') }}
                                    </a>
                                </li>
                            @endif

                            @if(Auth::check() && Auth::user()->hasPermission('view_polling_stations'))
                                <li class="nav-item dropdown">
                                    <a class="nav-link nav-link-custom dropdown-toggle {{ request()->routeIs('polling_stations.*') || request()->routeIs('polling_villages.*') ? 'active' : '' }}"
                                       href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                        <i class="bi bi-building"></i> {{ __('Stations') }}
                                    </a>
                                    <ul class="dropdown-menu dropdown-menu-custom">
                                        <li>
                                            <a class="dropdown-item dropdown-item-custom {{ request()->routeIs('polling_villages.*') ? 'active' : '' }}" href="{{ route('polling_villages.index') }}">
                                                <i class="bi bi-geo-alt"></i> {{ __('Polling Villages') }}
                                            </a>
                                        </li>
                                        <li>
                                            <a class="dropdown-item dropdown-item-custom {{ request()->routeIs('polling_stations.index') ? 'active' : '' }}" href="{{ route('polling_stations.index') }}">
                                                <i class="bi bi-gear"></i> {{ __('Manage Stations') }}
                                            </a>
                                        </li>
                                        <li>
                                            <a class="dropdown-item dropdown-item-custom {{ request()->routeIs('polling_stations.view') ? 'active' : '' }}" href="{{ route('polling_stations.view') }}">
                                                <i class="bi bi-building"></i> {{ __('Monitor Stations') }}
                                            </a>
                                        </li>
                                    </ul>
                                </li>
                            @endif

                            @if(Auth::check() && Auth::user()->user_type === 'admin')
                                <li class="nav-item dropdown">
                                    <a class="nav-link nav-link-custom dropdown-toggle {{ request()->routeIs('monitoring.*') || request()->routeIs('admin.audit.*') || request()->routeIs('admin.api-logs.*') ? 'active' : '' }}"
                                       href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                        <i class="bi bi-graph-up"></i> {{ __('Monitoring') }}
                                    </a>
                                    <ul class="dropdown-menu dropdown-menu-custom">
                                        <li>
                                            <a class="dropdown-item dropdown-item-custom {{ request()->routeIs('monitoring.*') ? 'active' : '' }}" href="{{ route('monitoring.index') }}">
                                                <i class="bi bi-star"></i> {{ __('Candidate Monitoring') }}
                                            </a>
                                        </li>
                                        <li><hr class="dropdown-divider"></li>
                                            <li>
                                                <a class="dropdown-item dropdown-item-custom {{ request()->routeIs('admin.audit.index') ? 'active' : '' }}" href="{{ route('admin.audit.index') }}">
                                                    <i class="bi bi-shield-check"></i> {{ __('Vote Audit') }}
                                                </a>
                                            </li>
                                            <li>
                                                <a class="dropdown-item dropdown-item-custom {{ request()->routeIs('admin.audit.activity') ? 'active' : '' }}" href="{{ route('admin.audit.activity') }}">
                                                    <i class="bi bi-activity"></i> {{ __('Live Activity') }}
                                                </a>
                                            </li>
                                            <li>
                                                <a class="dropdown-item dropdown-item-custom {{ request()->routeIs('admin.audit.flagged') ? 'active' : '' }}" href="{{ route('admin.audit.flagged') }}">
                                                    <i class="bi bi-flag"></i> {{ __('Flagged Submissions') }}
                                                </a>
                                            </li>
                                            <li>
                                                <a class="dropdown-item dropdown-item-custom {{ request()->routeIs('admin.audit.statistics') ? 'active' : '' }}" href="{{ route('admin.audit.statistics') }}">
                                                    <i class="bi bi-graph-up"></i> {{ __('Audit Statistics') }}
                                                </a>
                                            </li>
                                            <li><hr class="dropdown-divider"></li>
                                            <li>
                                                <a class="dropdown-item dropdown-item-custom {{ request()->routeIs('admin.api-logs.*') ? 'active' : '' }}" href="{{ route('admin.api-logs.index') }}">
                                                    <i class="bi bi-journal-text"></i> {{ __('API Logs') }}
                                                </a>
                                            </li>
                                    </ul>
                                </li>
                            @endif

                            @if(Auth::check() && Auth::user()->user_type === 'admin')
                                <li class="nav-item dropdown">
                                    <a class="nav-link nav-link-custom dropdown-toggle {{ request()->routeIs('admin.users.*') || request()->routeIs('admin.roles.*') || request()->routeIs('admin.permissions.*') || request()->routeIs('polling_stations.*') ? 'active' : '' }}"
                                       href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                        <i class="bi bi-gear"></i> {{ __('Admin') }}
                                    </a>
                                    <ul class="dropdown-menu dropdown-menu-custom">
                                        <li>
                                            <a class="dropdown-item dropdown-item-custom {{ request()->routeIs('polling_stations.*') ? 'active' : '' }}" href="{{ route('polling_stations.index') }}">
                                                <i class="bi bi-building"></i> {{ __('Manage Stations') }}
                                            </a>
                                        </li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li>
                                            <a class="dropdown-item dropdown-item-custom" href="{{ route('admin.users.index') }}">
                                                <i class="bi bi-people"></i> {{ __('Users') }}
                                            </a>
                                        </li>
                                        <li>
                                            <a class="dropdown-item dropdown-item-custom" href="{{ route('admin.roles.index') }}">
                                                <i class="bi bi-shield-check"></i> {{ __('Roles') }}
                                            </a>
                                        </li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li>
                                            <a class="dropdown-item dropdown-item-custom" href="{{ route('admin.permissions.info') }}">
                                                <i class="bi bi-info-circle"></i> {{ __('Permissions Info') }}
                                            </a>
                                        </li>
                                    </ul>
                                </li>
                            @endif

                  
                            
                        @endguest
                    </ul>

                    <!-- Right Side Of Navbar -->
                    <ul class="navbar-nav ms-auto">
                        <!-- Notification Dropdown -->
                        @auth
                            <li class="nav-item dropdown me-2">
                                <a id="notificationDropdown" class="nav-link p-2" href="#" role="button" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false" v-pre>
                                    <i class="bi bi-bell position-relative" style="font-size: 1rem;">
                                        @if(Auth::user()->unreadNotifications->count() > 0)
                                            <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger" style="font-size: 0.45rem; padding: 2px 4px;">
                                                {{ Auth::user()->unreadNotifications->count() }}
                                            </span>
                                        @endif
                                    </i>
                                </a>

                                <div class="dropdown-menu dropdown-menu-custom dropdown-menu-end" aria-labelledby="notificationDropdown" style="width: 350px; max-height: 400px; overflow-y: auto;">
                                    <div class="d-flex justify-content-between align-items-center px-3 py-2 border-bottom">
                                        <h6 class="mb-0 text-gold">Notifications</h6>
                                        @if(Auth::user()->unreadNotifications->count() > 0)
                                            <form action="{{ route('notifications.read-all') }}" method="POST">
                                                @csrf
                                                <button type="submit" class="btn btn-compact btn-gold-outline">Mark all as read</button>
                                            </form>
                                        @endif
                                    </div>
                                    
                                    @if(Auth::user()->unreadNotifications->count() > 0)
                                        @foreach(Auth::user()->unreadNotifications->take(5) as $notification)
                                            @php
                                                $data = $notification->data;
                                                $isVoteGapAlert = isset($data['candidate_id']);
                                            @endphp
                                            <div class="dropdown-item border-bottom py-2 px-3 bg-light">
                                                @if($isVoteGapAlert)
                                                    <div class="d-flex justify-content-between align-items-start">
                                                        <div>
                                                            <p class="mb-1 fw-bold">
                                                                <i class="bi bi-exclamation-triangle-fill text-warning me-1"></i>
                                                                {{ $data['candidate_name'] }}
                                                            </p>
                                                            <p class="mb-1 small">
                                                                {{ $data['is_leading'] 
                                                                    ? "Lead has fallen to only {$data['gap']} votes." 
                                                                    : "Trailing by {$data['gap']} votes." 
                                                                }}
                                                            </p>
                                                            <p class="mb-0 text-muted" style="font-size: 0.75rem;">
                                                                {{ $notification->created_at->diffForHumans() }}
                                                            </p>
                                                        </div>
                                                        <div>
                                                            <a href="{{ route('monitoring.compare', $data['candidate_id']) }}" class="btn btn-compact btn-gold">
                                                                View
                                                            </a>
                                                        </div>
                                                    </div>
                                                @else
                                                    <p class="mb-1">{{ json_encode($data) }}</p>
                                                    <p class="mb-0 text-muted" style="font-size: 0.75rem;">
                                                        {{ $notification->created_at->diffForHumans() }}
                                                    </p>
                                                @endif
                                            </div>
                                        @endforeach
                                        <a href="{{ route('notifications.index') }}" class="dropdown-item dropdown-item-custom text-center py-2">
                                            View all notifications
                                        </a>
                                    @else
                                        <div class="dropdown-item text-center py-3">
                                            <i class="bi bi-bell-slash text-muted d-block mb-2" style="font-size: 1.5rem;"></i>
                                            <p class="mb-0 text-muted">No new notifications</p>
                                        </div>
                                    @endif
                                </div>
                            </li>
                        @endauth
                        
                        <!-- Authentication Links -->
                        @guest
                            @if (Route::has('login'))
                                <li class="nav-item">
                                    <a class="nav-link nav-link-custom" href="{{ route('login') }}">
                                        <i class="bi bi-box-arrow-in-right"></i> {{ __('Login') }}
                                    </a>
                                </li>
                            @endif                           
                        @else
                            <li class="nav-item dropdown">
                                <a id="navbarDropdown" class="nav-link p-0" href="#" role="button" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false" v-pre>
                                    <div class="user-dropdown">
                                        <div class="user-avatar">
                                            {{ substr(Auth::user()->name, 0, 1) }}
                                        </div>
                                        <span class="user-name d-none d-sm-inline">{{ Auth::user()->name }}</span>
                                        <i class="bi bi-chevron-down"></i>
                                    </div>
                                </a>

                                <div class="dropdown-menu dropdown-menu-custom dropdown-menu-end" aria-labelledby="navbarDropdown">
                                    <div class="px-4 py-3 text-center">
                                        <div class="user-avatar mx-auto mb-2" style="width: 60px; height: 60px; font-size: 1.5rem;">
                                            {{ substr(Auth::user()->name, 0, 1) }}
                                        </div>
                                        <h6 class="mb-0">{{ Auth::user()->name }}</h6>
                                        <small class="text-muted">{{ Auth::user()->user_type }}</small>
                                    </div>
                                    <div class="dropdown-divider"></div>
                                    <a class="dropdown-item dropdown-item-custom" href="{{ route('users.create') }}">
                                        <i class="bi bi-person"></i> {{ __('Change Password') }}
                                    </a>
                                    {{-- <a class="dropdown-item dropdown-item-custom" href="#">
                                        <i class="bi bi-gear"></i> {{ __('Settings') }}
                                    </a>  --}}
                                    <a class="dropdown-item dropdown-item-custom logout" href="#" onclick="event.preventDefault(); document.getElementById('logout-form').submit();">
                                        <i class="bi bi-box-arrow-right"></i> {{ __('Logout') }}
                                    </a>

                                    <form id="logout-form" action="{{ route('logout') }}" method="POST" class="d-none">
                                        @csrf
                                        <input type="hidden" name="_token" value="{{ csrf_token() }}">
                                    </form>
                                </div>
                            </li>
                        @endguest
                    </ul>
                </div>
            </div>
        </nav>
        @endif

        <main class="main-content">
            <div class="{{ request()->routeIs('home') ? 'container-fluid' : 'container' }}">
                @if (session('status'))
                    <div class="alert alert-success alert-custom" role="alert">
                        <i class="bi bi-check-circle me-2"></i> {{ session('status') }}
                    </div>
                @endif
                @yield('content')
            </div>
        </main>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.7/dist/js/bootstrap.bundle.min.js" integrity="sha384-ndDqU0Gzau9qJ1lfW4pNLlhNTkCfHzAVBReH9diLvGRem5+R9g2FzA8ZGN954O5Q" crossorigin="anonymous"></script>
    <script src="{{ asset('js/smooth-refresh.js') }}"></script>

    <!-- Flash Messages as Toast Notifications -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Check for flash messages and show as toast notifications
            @if (session('error'))
                if (window.smoothRefreshUtils && window.smoothRefreshUtils.showToast) {
                    window.smoothRefreshUtils.showToast('{{ session('error') }}', 'error', 5000);
                }
            @endif

            @if (session('success'))
                if (window.smoothRefreshUtils && window.smoothRefreshUtils.showToast) {
                    window.smoothRefreshUtils.showToast('{{ session('success') }}', 'success', 4000);
                }
            @endif

            @if (session('warning'))
                if (window.smoothRefreshUtils && window.smoothRefreshUtils.showToast) {
                    window.smoothRefreshUtils.showToast('{{ session('warning') }}', 'warning', 4000);
                }
            @endif

            @if (session('info'))
                if (window.smoothRefreshUtils && window.smoothRefreshUtils.showToast) {
                    window.smoothRefreshUtils.showToast('{{ session('info') }}', 'info', 4000);
                }
            @endif
        });
    </script>

    <!-- Smooth Auto-Refresh System -->
    <script>
        class SmoothRefreshManager {
            constructor() {
                this.refreshInterval = null;
                this.voteCheckInterval = null;
                this.refreshRate = 60000; // 60 seconds - reasonable interval
                this.voteCheckRate = 30000; // 30 seconds for vote updates
                this.isRefreshing = false;
                this.lastRefreshTime = Date.now();
                this.lastVoteCheck = Date.now();
                this.refreshIndicator = null;

                // Enhanced user activity tracking
                this.lastUserActivity = Date.now();
                this.userActivityThreshold = 10000; // 10 seconds
                this.lastScrollTime = 0;
                this.scrollThreshold = 3000; // 3 seconds
                this.formInteractionTime = 0;
                this.formInteractionThreshold = 15000; // 15 seconds

                this.init();
            }

            init() {
                this.createRefreshIndicator();
                this.createToastContainer();
                this.startAutoRefresh();
                this.startVoteChecking();
                this.setupEventListeners();
                this.setupVisibilityChange();
            }

            createRefreshIndicator() {
                // Create a subtle refresh indicator
                this.refreshIndicator = document.createElement('div');
                this.refreshIndicator.id = 'refresh-indicator';
                this.refreshIndicator.innerHTML = `
                    <div class="d-flex align-items-center">
                        <div class="spinner-border spinner-border-sm me-2" role="status" style="width: 12px; height: 12px;">
                            <span class="visually-hidden">Updating...</span>
                        </div>
                        <small>Updating data...</small>
                    </div>
                `;
                this.refreshIndicator.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    background: rgba(40, 167, 69, 0.9);
                    color: white;
                    padding: 8px 12px;
                    border-radius: 20px;
                    font-size: 12px;
                    z-index: 9999;
                    opacity: 0;
                    transform: translateY(-10px);
                    transition: all 0.3s ease;
                    pointer-events: none;
                `;
                document.body.appendChild(this.refreshIndicator);

                // Create pause notification
                this.createPauseIndicator();
            }

            createToastContainer() {
                // Create toast container for vote notifications
                if (!document.getElementById('vote-toast-container')) {
                    this.toastContainer = document.createElement('div');
                    this.toastContainer.id = 'vote-toast-container';
                    this.toastContainer.style.cssText = `
                        position: fixed;
                        top: 80px;
                        right: 20px;
                        z-index: 10000;
                        max-width: 400px;
                        pointer-events: none;
                    `;
                    document.body.appendChild(this.toastContainer);
                }
            }

            showVoteUpdateToast(update) {
                const toast = document.createElement('div');
                toast.className = 'vote-update-toast';
                toast.style.cssText = `
                    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
                    color: white;
                    padding: 16px 20px;
                    border-radius: 12px;
                    margin-bottom: 12px;
                    box-shadow: 0 8px 32px rgba(40, 167, 69, 0.3);
                    transform: translateX(100%);
                    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
                    pointer-events: auto;
                    cursor: pointer;
                    border-left: 4px solid rgba(255, 255, 255, 0.3);
                `;

                toast.innerHTML = `
                    <div class="d-flex align-items-start">
                        <div class="flex-shrink-0 me-3">
                            <div style="background: rgba(255, 255, 255, 0.2); border-radius: 50%; width: 40px; height: 40px; display: flex; align-items: center; justify-content: center;">
                                <i class="bi bi-arrow-clockwise" style="font-size: 18px; animation: spin 1s linear infinite;"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1">
                            <div class="fw-bold mb-1" style="font-size: 14px;">
                                <i class="bi bi-broadcast me-1"></i>Vote Update Detected
                            </div>
                            <div style="font-size: 13px; opacity: 0.95; line-height: 1.4;">
                                <strong>${update.candidate_name}</strong> (${update.position_name})<br>
                                <span style="opacity: 0.8;">${update.polling_station}</span><br>
                                <span style="background: rgba(255, 255, 255, 0.2); padding: 2px 8px; border-radius: 10px; font-size: 12px;">
                                    ${update.votes.toLocaleString()} votes
                                </span>
                            </div>
                            <div style="font-size: 11px; opacity: 0.7; margin-top: 4px;">
                                ${update.location.parish}, ${update.location.subcounty}
                            </div>
                            <div style="font-size: 11px; opacity: 0.8; margin-top: 6px; padding: 4px 8px; background: rgba(255, 255, 255, 0.1); border-radius: 6px;">
                                <i class="bi bi-info-circle me-1"></i>Page will refresh in 3 seconds...
                            </div>
                        </div>
                        <button onclick="this.parentElement.parentElement.remove()" style="background: none; border: none; color: rgba(255, 255, 255, 0.7); font-size: 16px; padding: 0; margin-left: 8px; cursor: pointer;">
                            <i class="bi bi-x"></i>
                        </button>
                    </div>
                `;

                this.toastContainer.appendChild(toast);

                // Animate in
                setTimeout(() => {
                    toast.style.transform = 'translateX(0)';
                }, 100);

                // Don't auto-remove since page will reload
                // Toast will disappear when page reloads in 3 seconds

                // Click to dismiss
                toast.addEventListener('click', () => {
                    toast.style.transform = 'translateX(100%)';
                    toast.style.opacity = '0';
                    setTimeout(() => {
                        if (toast.parentElement) {
                            toast.parentElement.removeChild(toast);
                        }
                    }, 400);
                });
            }

            createPauseIndicator() {
                if (!document.getElementById('refresh-pause-indicator')) {
                    this.pauseIndicator = document.createElement('div');
                    this.pauseIndicator.id = 'refresh-pause-indicator';
                    this.pauseIndicator.innerHTML = `
                        <div class="d-flex align-items-center">
                            <i class="bi bi-pause-circle me-2"></i>
                            <small>Auto-refresh paused</small>
                        </div>
                    `;
                    this.pauseIndicator.style.cssText = `
                        position: fixed;
                        bottom: 20px;
                        right: 20px;
                        background: rgba(255, 193, 7, 0.9);
                        color: #856404;
                        padding: 6px 10px;
                        border-radius: 15px;
                        font-size: 11px;
                        z-index: 9998;
                        opacity: 0;
                        transform: translateY(10px);
                        transition: all 0.3s ease;
                        pointer-events: none;
                        cursor: pointer;
                    `;

                    document.body.appendChild(this.pauseIndicator);
                }
            }

            showPauseIndicator(reason = '') {
                if (this.pauseIndicator) {
                    const message = reason ? `Auto-refresh paused: ${reason}` : 'Auto-refresh paused';
                    this.pauseIndicator.querySelector('small').textContent = message;
                    this.pauseIndicator.style.opacity = '1';
                    this.pauseIndicator.style.transform = 'translateY(0)';
                    this.pauseIndicator.style.pointerEvents = 'auto';

                    // Auto-hide after 3 seconds
                    setTimeout(() => {
                        this.hidePauseIndicator();
                    }, 3000);
                }
            }

            hidePauseIndicator() {
                if (this.pauseIndicator) {
                    this.pauseIndicator.style.opacity = '0';
                    this.pauseIndicator.style.transform = 'translateY(10px)';
                    this.pauseIndicator.style.pointerEvents = 'none';
                }
            }

            showRefreshIndicator() {
                if (this.refreshIndicator) {
                    this.refreshIndicator.style.opacity = '1';
                    this.refreshIndicator.style.transform = 'translateY(0)';
                }
            }

            hideRefreshIndicator() {
                if (this.refreshIndicator) {
                    this.refreshIndicator.style.opacity = '0';
                    this.refreshIndicator.style.transform = 'translateY(-10px)';
                }
            }

            showRefreshError() {
                // Show a temporary error message
                const errorIndicator = document.createElement('div');
                errorIndicator.innerHTML = `
                    <div class="d-flex align-items-center">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        <small>Update failed - <a href="#" onclick="window.location.reload()" class="text-white text-decoration-underline">Refresh page</a></small>
                    </div>
                `;
                errorIndicator.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    background: rgba(220, 53, 69, 0.9);
                    color: white;
                    padding: 8px 12px;
                    border-radius: 20px;
                    font-size: 12px;
                    z-index: 9999;
                    opacity: 1;
                    transform: translateY(0);
                    transition: all 0.3s ease;
                `;
                document.body.appendChild(errorIndicator);

                // Remove after 5 seconds
                setTimeout(() => {
                    errorIndicator.style.opacity = '0';
                    errorIndicator.style.transform = 'translateY(-10px)';
                    setTimeout(() => document.body.removeChild(errorIndicator), 300);
                }, 5000);
            }

            showSuccessIndicator() {
                // Briefly change the refresh indicator to show success
                if (this.refreshIndicator) {
                    const originalContent = this.refreshIndicator.innerHTML;
                    const originalBackground = this.refreshIndicator.style.background;

                    this.refreshIndicator.innerHTML = `
                        <div class="d-flex align-items-center">
                            <i class="bi bi-check-circle me-2"></i>
                            <small>Updated!</small>
                        </div>
                    `;
                    this.refreshIndicator.style.background = 'rgba(40, 167, 69, 0.9)';

                    setTimeout(() => {
                        this.refreshIndicator.innerHTML = originalContent;
                        this.refreshIndicator.style.background = originalBackground;
                    }, 1500);
                }
            }

            shouldSkipRefresh() {
                // Don't refresh if user has been recently active
                if (this.isUserRecentlyActive()) {
                    console.log('Auto-refresh paused: User recently active');
                    this.showPauseIndicator('user active');
                    return true;
                }

                // Don't refresh if map is open
                const mapContainer = document.getElementById('mapViewContainer');
                if (mapContainer && mapContainer.style.display !== 'none') {
                    console.log('Auto-refresh paused: Map is open');
                    this.showPauseIndicator('map open');
                    return true;
                }

                // Don't refresh if user is actively interacting with forms
                if (this.isUserInteractingWithForms()) {
                    console.log('Auto-refresh paused: User is interacting with forms');
                    this.showPauseIndicator('form interaction');
                    return true;
                }

                // Don't refresh if modal is open
                const openModal = document.querySelector('.modal.show');
                if (openModal) {
                    console.log('Auto-refresh paused: Modal is open');
                    this.showPauseIndicator('modal open');
                    return true;
                }

                // Don't refresh if dropdown is open
                const openDropdown = document.querySelector('.dropdown-menu.show');
                if (openDropdown) {
                    console.log('Auto-refresh paused: Dropdown is open');
                    this.showPauseIndicator('dropdown open');
                    return true;
                }

                // Don't refresh if user is scrolling
                if (this.isUserScrolling()) {
                    console.log('Auto-refresh paused: User is scrolling');
                    this.showPauseIndicator('scrolling');
                    return true;
                }

                // Don't refresh if user is selecting text
                if (this.hasTextSelection()) {
                    console.log('Auto-refresh paused: User has text selected');
                    this.showPauseIndicator('text selected');
                    return true;
                }

                // Don't refresh if important operations are in progress
                if (this.hasImportantOperationsInProgress()) {
                    console.log('Auto-refresh paused: Important operations in progress');
                    this.showPauseIndicator('operation in progress');
                    return true;
                }

                return false;
            }

            // Enhanced user activity detection methods
            isUserRecentlyActive() {
                const timeSinceLastActivity = Date.now() - this.lastUserActivity;
                return timeSinceLastActivity < this.userActivityThreshold;
            }

            isUserInteractingWithForms() {
                // Check if user is actively typing or has focus on form elements
                const activeElement = document.activeElement;
                if (activeElement && (
                    activeElement.tagName === 'INPUT' ||
                    activeElement.tagName === 'TEXTAREA' ||
                    activeElement.tagName === 'SELECT' ||
                    activeElement.isContentEditable
                )) {
                    this.formInteractionTime = Date.now();
                    return true;
                }

                // Check if user was recently interacting with forms
                const timeSinceFormInteraction = Date.now() - this.formInteractionTime;
                return timeSinceFormInteraction < this.formInteractionThreshold;
            }

            isUserScrolling() {
                const timeSinceLastScroll = Date.now() - this.lastScrollTime;
                return timeSinceLastScroll < this.scrollThreshold;
            }

            hasTextSelection() {
                const selection = window.getSelection();
                return selection && selection.toString().length > 0;
            }

            hasImportantOperationsInProgress() {
                // Check for file uploads
                const fileInputs = document.querySelectorAll('input[type="file"]');
                for (let input of fileInputs) {
                    if (input.files && input.files.length > 0) {
                        return true;
                    }
                }

                // Check for forms being submitted
                const submittingForms = document.querySelectorAll('form[data-submitting="true"]');
                if (submittingForms.length > 0) {
                    return true;
                }

                // Check for AJAX requests in progress
                if (document.querySelector('.loading, .spinner-border, [data-loading="true"]')) {
                    return true;
                }

                // Check for video/audio playing
                const mediaElements = document.querySelectorAll('video, audio');
                for (let media of mediaElements) {
                    if (!media.paused) {
                        return true;
                    }
                }

                return false;
            }

            async performSmoothRefresh() {
                if (this.isRefreshing || this.shouldSkipRefresh()) {
                    return;
                }

                this.isRefreshing = true;
                this.showRefreshIndicator();

                try {
                    // Simple page reload approach - more reliable
                    const currentUrl = window.location.href;
                    const response = await fetch(currentUrl, {
                        method: 'GET',
                        headers: {
                            'X-Requested-With': 'XMLHttpRequest',
                            'Accept': 'text/html',
                            'Cache-Control': 'no-cache'
                        },
                        credentials: 'same-origin'
                    });

                    if (response.ok) {
                        const newHtml = await response.text();

                        // Parse the new HTML
                        const parser = new DOMParser();
                        const newDoc = parser.parseFromString(newHtml, 'text/html');

                        // Update main content area
                        const currentMain = document.querySelector('.main-content');
                        const newMain = newDoc.querySelector('.main-content');

                        if (currentMain && newMain) {
                            // Smooth transition
                            currentMain.style.opacity = '0.7';
                            setTimeout(() => {
                                currentMain.innerHTML = newMain.innerHTML;
                                currentMain.style.opacity = '1';

                                // Re-initialize any JavaScript components if needed
                                this.reinitializeComponents();
                            }, 200);
                        }

                        // Update notifications count
                        await this.refreshNotifications();

                        this.lastRefreshTime = Date.now();
                        console.log('✅ Page refreshed successfully at', new Date().toLocaleTimeString());

                        // Show subtle success feedback
                        this.showSuccessIndicator();

                    } else {
                        console.warn('Refresh failed with status:', response.status);
                    }

                } catch (error) {
                    console.error('Smooth refresh failed:', error);

                    // Fallback: show a simple message and offer manual refresh
                    this.showRefreshError();
                } finally {
                    this.isRefreshing = false;
                    setTimeout(() => this.hideRefreshIndicator(), 1000);
                }
            }

            reinitializeComponents() {
                // Re-initialize any JavaScript components that might be needed
                try {
                    // Re-initialize charts if they exist
                    if (typeof loadChartData === 'function') {
                        const currentPosition = document.querySelector('.position-tab.active')?.dataset.position || 'overall';
                        setTimeout(() => loadChartData(currentPosition), 100);
                    }

                    // Re-initialize any other components
                    if (typeof initializeTooltips === 'function') {
                        initializeTooltips();
                    }

                    // Re-initialize Bootstrap components
                    if (window.bootstrap) {
                        // Re-initialize tooltips
                        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
                        tooltipTriggerList.map(function (tooltipTriggerEl) {
                            return new bootstrap.Tooltip(tooltipTriggerEl);
                        });
                    }

                } catch (error) {
                    console.error('Failed to reinitialize components:', error);
                }
            }

            async refreshNotifications() {
                try {
                    // Check if notifications endpoint exists
                    const response = await fetch('/notifications/count', {
                        headers: {
                            'X-Requested-With': 'XMLHttpRequest',
                            'Accept': 'application/json'
                        }
                    });

                    if (response.ok) {
                        const data = await response.json();
                        this.updateNotificationBadge(data.count || 0);
                    }
                } catch (error) {
                    // Silently fail if notifications endpoint doesn't exist
                    console.log('Notifications endpoint not available');
                }
            }



            updateNotificationBadge(count) {
                const badge = document.querySelector('#notificationDropdown .badge');
                if (badge) {
                    if (count > 0) {
                        badge.textContent = count;
                        badge.style.display = 'inline';
                    } else {
                        badge.style.display = 'none';
                    }
                }
            }

            animateNumber(element, targetNumber) {
                // Use enhanced animation if available, fallback to simple version
                if (window.smoothRefreshUtils && window.smoothRefreshUtils.enhancedAnimateNumber) {
                    window.smoothRefreshUtils.enhancedAnimateNumber(element, targetNumber);
                    return;
                }

                // Fallback simple animation
                if (!element) return;

                const currentNumber = parseInt(element.textContent) || 0;
                const increment = (targetNumber - currentNumber) / 20;
                let current = currentNumber;

                const timer = setInterval(() => {
                    current += increment;
                    if ((increment > 0 && current >= targetNumber) || (increment < 0 && current <= targetNumber)) {
                        current = targetNumber;
                        clearInterval(timer);
                    }
                    element.textContent = Math.round(current);
                }, 50);
            }

            startAutoRefresh() {
                this.refreshInterval = setInterval(() => {
                    this.performSmoothRefresh();
                }, this.refreshRate);

                console.log(`Smooth auto-refresh started (${this.refreshRate/1000}s interval)`);
            }

            startVoteChecking() {
                this.voteCheckInterval = setInterval(() => {
                    this.checkForVoteUpdates();
                }, this.voteCheckRate);

                console.log(`Vote checking started (${this.voteCheckRate/1000}s interval)`);
            }

            async checkForVoteUpdates() {
                if (this.shouldSkipRefresh()) {
                    return;
                }

                try {
                    const response = await fetch('/api/vote-updates?last_check=' + new Date(this.lastVoteCheck).toISOString(), {
                        headers: {
                            'X-Requested-With': 'XMLHttpRequest',
                            'Accept': 'application/json'
                        }
                    });

                    if (response.ok) {
                        const data = await response.json();

                        if (data.has_updates && data.updates.length > 0) {
                            // Stop regular auto-refresh to avoid conflicts
                            this.stopAutoRefresh();

                            // Show toast notifications for new vote updates
                            data.updates.forEach(update => {
                                this.showVoteUpdateToast(update);
                            });

                            // Perform full page reload after showing toasts
                            setTimeout(() => {
                                console.log('Vote updates detected - performing full page reload');
                                window.location.reload();
                            }, 3000); // Wait 3 seconds to let users see the toast
                        }

                        this.lastVoteCheck = Date.now();
                    }
                } catch (error) {
                    console.error('Vote update check failed:', error);
                }
            }

            stopAutoRefresh() {
                if (this.refreshInterval) {
                    clearInterval(this.refreshInterval);
                    this.refreshInterval = null;
                    console.log('Auto-refresh stopped');
                }
                if (this.voteCheckInterval) {
                    clearInterval(this.voteCheckInterval);
                    this.voteCheckInterval = null;
                    console.log('Vote checking stopped');
                }
            }

            setupEventListeners() {
                // Stop auto-refresh when map is opened
                const toggleMapBtn = document.getElementById('toggleMapView');
                if (toggleMapBtn) {
                    toggleMapBtn.addEventListener('click', () => this.stopAutoRefresh());
                }

                // Resume auto-refresh when map is closed
                const closeMapBtn = document.getElementById('closeMapView');
                if (closeMapBtn) {
                    closeMapBtn.addEventListener('click', () => {
                        this.startAutoRefresh();
                        this.startVoteChecking();
                    });
                }

                // Enhanced user activity tracking
                const activityEvents = [
                    'mousedown', 'mouseup', 'mousemove', 'click',
                    'keydown', 'keyup', 'keypress',
                    'touchstart', 'touchend', 'touchmove',
                    'focus', 'blur', 'change', 'input'
                ];

                activityEvents.forEach(event => {
                    document.addEventListener(event, () => {
                        this.lastUserActivity = Date.now();

                        // Reset refresh timer on significant user interaction
                        if (['mousedown', 'keydown', 'touchstart', 'focus'].includes(event)) {
                            this.stopAutoRefresh();
                            this.startAutoRefresh();
                            this.startVoteChecking();
                        }
                    }, { passive: true });
                });

                // Track scrolling activity
                let scrollTimeout;
                document.addEventListener('scroll', () => {
                    this.lastScrollTime = Date.now();
                    this.lastUserActivity = Date.now();

                    // Clear existing timeout
                    clearTimeout(scrollTimeout);

                    // Reset refresh timer after scrolling stops
                    scrollTimeout = setTimeout(() => {
                        this.stopAutoRefresh();
                        this.startAutoRefresh();
                        this.startVoteChecking();
                    }, 1000);
                }, { passive: true });

                // Track form interactions more precisely
                document.addEventListener('focusin', (e) => {
                    if (e.target.matches('input, textarea, select, [contenteditable]')) {
                        this.formInteractionTime = Date.now();
                        console.log('Form interaction started');
                    }
                });

                document.addEventListener('focusout', (e) => {
                    if (e.target.matches('input, textarea, select, [contenteditable]')) {
                        // Extend the form interaction time when user leaves a form field
                        this.formInteractionTime = Date.now();
                        console.log('Form interaction ended, extended protection');
                    }
                });

                // Track form submissions
                document.addEventListener('submit', (e) => {
                    e.target.setAttribute('data-submitting', 'true');
                    console.log('Form submission started');

                    // Remove the attribute after a reasonable time
                    setTimeout(() => {
                        e.target.removeAttribute('data-submitting');
                    }, 10000);
                });

                // Manual refresh button
                this.addManualRefreshButton();
            }

            setupVisibilityChange() {
                // Enhanced visibility change handling
                document.addEventListener('visibilitychange', () => {
                    if (document.hidden) {
                        this.stopAutoRefresh();
                        console.log('Tab hidden: Auto-refresh paused');
                    } else {
                        console.log('Tab visible: Resuming auto-refresh');
                        this.startAutoRefresh();
                        this.startVoteChecking();

                        // Only refresh immediately if user hasn't been active recently
                        // and no important operations are in progress
                        setTimeout(() => {
                            if (!this.shouldSkipRefresh()) {
                                console.log('Tab became visible: Performing refresh');
                                this.performSmoothRefresh();
                            } else {
                                console.log('Tab became visible: Skipping refresh due to user activity');
                            }
                        }, 1000);
                    }
                });

                // Handle page focus/blur for better user experience
                window.addEventListener('focus', () => {
                    this.lastUserActivity = Date.now();
                    console.log('Window focused: User activity detected');
                });

                window.addEventListener('blur', () => {
                    console.log('Window blurred: Reducing refresh frequency');
                    // Could implement reduced refresh rate when window is not focused
                });
            }

            addManualRefreshButton() {
                // Add a manual refresh button to the navbar
                const navbar = document.querySelector('.navbar-nav.ms-auto');
                if (navbar) {
                    const refreshBtn = document.createElement('li');
                    refreshBtn.className = 'nav-item me-2';
                    refreshBtn.innerHTML = `
                        <button class="btn btn-outline-secondary btn-sm" id="manual-refresh-btn" title="Refresh page data" style="border-radius: 6px; padding: 0.4rem 0.6rem;">
                            <i class="bi bi-arrow-clockwise" style="font-size: 0.9rem;"></i>
                        </button>
                    `;

                    const refreshButton = refreshBtn.querySelector('#manual-refresh-btn');
                    refreshButton.addEventListener('click', () => {
                        refreshButton.innerHTML = '<i class="bi bi-arrow-clockwise spinning" style="font-size: 0.9rem;"></i>';
                        refreshButton.disabled = true;

                        this.performSmoothRefresh().finally(() => {
                            refreshButton.innerHTML = '<i class="bi bi-arrow-clockwise" style="font-size: 0.9rem;"></i>';
                            refreshButton.disabled = false;
                        });
                    });

                    navbar.insertBefore(refreshBtn, navbar.firstChild);
                }
            }
        }

        // Initialize smooth refresh system when page loads - only on home page
        document.addEventListener('DOMContentLoaded', function() {
            // Only initialize on home page
            if (window.location.pathname === '/home' || window.location.pathname === '/') {
                window.smoothRefresh = new SmoothRefreshManager();
            }
        });

        // Add CSS for spinning animation
        const style = document.createElement('style');
        style.textContent = `
            .spinning {
                animation: spin 1s linear infinite;
            }
            @keyframes spin {
                from { transform: rotate(0deg); }
                to { transform: rotate(360deg); }
            }
        `;
        document.head.appendChild(style);
    </script>

    @stack('scripts')
</body>
</html>
